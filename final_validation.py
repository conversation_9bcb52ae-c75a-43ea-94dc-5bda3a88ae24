#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def final_validation(file_path):
    """最终验证新WXML文件的结构"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    total_lines = len(lines)
    
    # 移除注释
    content_no_comments = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
    
    # 统计标签
    view_open = len(re.findall(r'<view(?:\s[^>]*)?>', content_no_comments))
    view_close = len(re.findall(r'</view>', content_no_comments))
    scroll_view_open = len(re.findall(r'<scroll-view(?:\s[^>]*)?>', content_no_comments))
    scroll_view_close = len(re.findall(r'</scroll-view>', content_no_comments))
    
    # 检查tab页面
    tab_pages = [
        'basic', 'paipan', 'advanced', 'fortune', 
        'professional', 'classical', 'timing', 'liuqin'
    ]
    
    found_tabs = []
    for tab in tab_pages:
        if f'currentTab === \'{tab}\'' in content:
            found_tabs.append(tab)
    
    print(f"📊 文件统计:")
    print(f"  总行数: {total_lines}")
    print(f"  <view> 标签: {view_open} 开始, {view_close} 结束")
    print(f"  <scroll-view> 标签: {scroll_view_open} 开始, {scroll_view_close} 结束")
    print(f"  标签平衡: {'✅' if view_open == view_close and scroll_view_open == scroll_view_close else '❌'}")
    
    print(f"\n🏷️ Tab页面检查:")
    print(f"  预期页面: {len(tab_pages)} 个")
    print(f"  找到页面: {len(found_tabs)} 个")
    print(f"  页面列表: {', '.join(found_tabs)}")
    print(f"  页面完整: {'✅' if len(found_tabs) == len(tab_pages) else '❌'}")
    
    # 检查结构完整性
    structure_check = True
    
    # 检查必要的容器
    required_classes = [
        'tianggong-container', 'tianggong-main-content', 
        'tianggong-tab-nav', 'tianggong-tab-content'
    ]
    
    missing_classes = []
    for cls in required_classes:
        if cls not in content:
            missing_classes.append(cls)
            structure_check = False
    
    print(f"\n🏗️ 结构检查:")
    if structure_check:
        print("  ✅ 所有必要的结构元素都存在")
    else:
        print(f"  ❌ 缺少结构元素: {', '.join(missing_classes)}")
    
    # 最终评估
    overall_success = (
        view_open == view_close and 
        scroll_view_open == scroll_view_close and
        len(found_tabs) == len(tab_pages) and
        structure_check
    )
    
    print(f"\n🎉 最终评估:")
    if overall_success:
        print("  ✅ WXML文件重构成功！")
        print("  ✅ 所有标签平衡")
        print("  ✅ 所有tab页面完整")
        print("  ✅ 结构完整性良好")
        print("  🚀 可以进行编译测试")
    else:
        print("  ❌ 重构存在问题，需要进一步修复")
    
    return overall_success

if __name__ == "__main__":
    final_validation("pages/bazi-result/index.wxml")
