/**
 * scroll-view内容分析器
 * 专门分析scroll-view内部的标签匹配情况
 */

const fs = require('fs');
const path = require('path');

function analyzeScrollViewContent() {
  console.log('🔍 分析scroll-view内容结构');
  
  const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
  const content = fs.readFileSync(wxmlPath, 'utf8');
  const lines = content.split('\n');
  
  // 找到scroll-view的开始和结束位置
  const scrollViewStart = lines.findIndex(line => line.includes('<scroll-view'));
  const scrollViewEnd = lines.findIndex(line => line.includes('</scroll-view>'));
  
  console.log(`📍 scroll-view位置:`);
  console.log(`开始: 第${scrollViewStart + 1}行`);
  console.log(`结束: 第${scrollViewEnd + 1}行`);
  console.log(`内容行数: ${scrollViewEnd - scrollViewStart - 1}`);
  
  if (scrollViewStart === -1 || scrollViewEnd === -1) {
    console.log('❌ 未找到scroll-view标签');
    return;
  }
  
  // 分析scroll-view内部的标签匹配
  console.log(`\n🔍 分析scroll-view内部标签匹配:`);
  
  let depth = 0;
  const tagStack = [];
  const issues = [];
  
  for (let i = scrollViewStart + 1; i < scrollViewEnd; i++) {
    const line = lines[i];
    const lineNum = i + 1;
    
    // 匹配开始标签（排除自闭合标签）
    const openTags = line.match(/<(\w+)(?:\s[^>]*)?>/g) || [];
    const selfClosingTags = line.match(/<\w+[^>]*\/>/g) || [];
    const closeTags = line.match(/<\/(\w+)>/g) || [];
    
    // 处理开始标签
    openTags.forEach(tag => {
      if (!selfClosingTags.some(selfTag => selfTag.includes(tag.match(/<(\w+)/)[1]))) {
        const tagName = tag.match(/<(\w+)/)[1];
        tagStack.push({ tag: tagName, line: lineNum });
        depth++;
      }
    });
    
    // 处理结束标签
    closeTags.forEach(tag => {
      const tagName = tag.match(/<\/(\w+)>/)[1];
      if (tagStack.length > 0) {
        const lastTag = tagStack.pop();
        if (lastTag.tag !== tagName) {
          issues.push({
            line: lineNum,
            issue: `标签不匹配: 期望 </${lastTag.tag}> 但找到 </${tagName}>`,
            expected: lastTag.tag,
            found: tagName
          });
        }
        depth--;
      } else {
        issues.push({
          line: lineNum,
          issue: `多余的结束标签: </${tagName}>`,
          found: tagName
        });
        depth--;
      }
    });
  }
  
  console.log(`📊 分析结果:`);
  console.log(`最终深度: ${depth}`);
  console.log(`未匹配的开始标签: ${tagStack.length}`);
  console.log(`发现的问题: ${issues.length}`);
  
  if (tagStack.length > 0) {
    console.log(`\n❌ 未关闭的标签:`);
    tagStack.forEach(item => {
      console.log(`  第${item.line}行: <${item.tag}>`);
    });
  }
  
  if (issues.length > 0) {
    console.log(`\n❌ 发现的问题:`);
    issues.forEach(issue => {
      console.log(`  第${issue.line}行: ${issue.issue}`);
    });
  }
  
  // 检查scroll-view结束行的具体内容
  console.log(`\n📄 scroll-view结束行详情:`);
  const endLine = lines[scrollViewEnd];
  console.log(`第${scrollViewEnd + 1}行: "${endLine}"`);
  console.log(`行长度: ${endLine.length}`);
  console.log(`第7个字符: "${endLine[6] || '(不存在)'}"`);
  
  // 检查前后几行
  console.log(`\n📄 scroll-view结束前后内容:`);
  for (let i = Math.max(0, scrollViewEnd - 3); i <= Math.min(lines.length - 1, scrollViewEnd + 3); i++) {
    const marker = i === scrollViewEnd ? ' ← 错误行' : '';
    console.log(`${i + 1}: ${lines[i]}${marker}`);
  }
  
  // 给出修复建议
  if (depth > 0) {
    console.log(`\n🔧 修复建议:`);
    console.log(`scroll-view内部缺少 ${depth} 个结束标签`);
    if (tagStack.length > 0) {
      console.log(`需要添加的结束标签:`);
      for (let i = tagStack.length - 1; i >= 0; i--) {
        console.log(`  </${tagStack[i].tag}>`);
      }
    }
  } else if (depth < 0) {
    console.log(`\n🔧 修复建议:`);
    console.log(`scroll-view内部有 ${Math.abs(depth)} 个多余的结束标签`);
  } else if (issues.length === 0) {
    console.log(`\n✅ scroll-view内部标签匹配正确`);
  }
}

// 运行分析
if (require.main === module) {
  analyzeScrollViewContent();
}

module.exports = { analyzeScrollViewContent };
