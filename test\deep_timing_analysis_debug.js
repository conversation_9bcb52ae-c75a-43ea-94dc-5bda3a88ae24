/**
 * 深度调试应期分析系统
 * 检查用户发现的致命错误：
 * 1. 任何出生时间都达标
 * 2. 不同用户的阈值都一样
 * 3. 平衡分数固定55分
 */

// 模拟不同的八字数据
const testCases = [
  {
    name: '用户A - 强旺八字',
    bazi: {
      year: { gan: '甲', zhi: '子' },
      month: { gan: '丙', zhi: '寅' },
      day: { gan: '戊', zhi: '午' },
      hour: { gan: '庚', zhi: '申' }
    },
    expectedDifference: '应该有高能量值'
  },
  {
    name: '用户B - 偏弱八字',
    bazi: {
      year: { gan: '乙', zhi: '丑' },
      month: { gan: '丁', zhi: '卯' },
      day: { gan: '己', zhi: '未' },
      hour: { gan: '辛', zhi: '酉' }
    },
    expectedDifference: '应该有低能量值'
  },
  {
    name: '用户C - 平衡八字',
    bazi: {
      year: { gan: '壬', zhi: '戌' },
      month: { gan: '甲', zhi: '子' },
      day: { gan: '丙', zhi: '寅' },
      hour: { gan: '戊', zhi: '午' }
    },
    expectedDifference: '应该有中等能量值'
  }
];

// 模拟五行能量提取函数
function extractElementEnergies(bazi) {
  console.log('🔍 提取五行能量数据...');
  
  const elementMap = {
    '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
    '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水',
    '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土',
    '巳': '火', '午': '火', '未': '土', '申': '金', '酉': '金',
    '戌': '土', '亥': '水'
  };
  
  const elementCounts = { 木: 0, 火: 0, 土: 0, 金: 0, 水: 0 };
  
  // 统计四柱中的五行
  ['year', 'month', 'day', 'hour'].forEach(pillar => {
    if (bazi[pillar]) {
      const ganElement = elementMap[bazi[pillar].gan];
      const zhiElement = elementMap[bazi[pillar].zhi];
      
      if (ganElement) elementCounts[ganElement] += 2; // 天干权重2
      if (zhiElement) elementCounts[zhiElement] += 1; // 地支权重1
    }
  });
  
  // 转换为百分比（总分12分）
  const total = Object.values(elementCounts).reduce((sum, count) => sum + count, 0);
  const elementEnergies = {};
  
  Object.keys(elementCounts).forEach(element => {
    elementEnergies[element] = total > 0 ? (elementCounts[element] / total * 100) : 0;
  });
  
  return elementEnergies;
}

// 模拟平衡度计算
function calculateBalance(elementEnergies) {
  const values = Object.values(elementEnergies);
  const average = values.reduce((sum, val) => sum + val, 0) / values.length;
  const variance = values.reduce((sum, val) => sum + Math.pow(val - average, 2), 0) / values.length;
  const standardDeviation = Math.sqrt(variance);
  
  // 标准差越小，平衡度越高
  const balance = Math.max(0, Math.min(100, 100 - standardDeviation * 2));
  return balance;
}

// 模拟统一能量计算
function calculateUnifiedEnergy(elementEnergies, eventType, threshold) {
  const elementWeights = {
    marriage: { 金: 0.35, 木: 0.05, 水: 0.15, 火: 0.35, 土: 0.10 },
    promotion: { 金: 0.40, 木: 0.15, 水: 0.35, 火: 0.05, 土: 0.05 },
    childbirth: { 金: 0.05, 木: 0.35, 水: 0.40, 火: 0.15, 土: 0.05 },
    wealth: { 金: 0.15, 木: 0.35, 火: 0.35, 土: 0.15, 水: 0.00 }
  };
  
  const weights = elementWeights[eventType];
  
  // 计算加权能量
  const weightedEnergy = elementEnergies.金 * weights.金 + 
                        elementEnergies.木 * weights.木 + 
                        elementEnergies.水 * weights.水 + 
                        elementEnergies.火 * weights.火 + 
                        elementEnergies.土 * weights.土;
  
  // 标准化到0-100%范围
  const normalizedEnergy = weightedEnergy; // 直接使用加权结果
  
  // 应用平衡度调整
  const balance = calculateBalance(elementEnergies);
  const balanceAdjustment = (balance - 50) * 0.2;
  
  const finalEnergy = Math.max(0, Math.min(100, normalizedEnergy + balanceAdjustment));
  const met = finalEnergy >= (threshold * 100);
  
  return {
    actual: finalEnergy,
    required: threshold * 100,
    met: met,
    balance: balance,
    weightedEnergy: weightedEnergy,
    balanceAdjustment: balanceAdjustment
  };
}

// 主调试函数
function deepTimingAnalysisDebug() {
  console.log('🧪 ===== 深度调试应期分析系统 =====\n');
  
  console.log('🚨 用户发现的致命错误:');
  console.log('  1. 任何出生时间都达标');
  console.log('  2. 不同用户的阈值都一样');
  console.log('  3. 平衡分数固定55分');
  
  // 阈值配置
  const thresholds = {
    marriage: 0.45,    // 45%
    promotion: 0.55,   // 55%
    childbirth: 0.40,  // 40%
    wealth: 0.50       // 50%
  };
  
  console.log('\n📊 阈值配置:');
  Object.entries(thresholds).forEach(([eventType, threshold]) => {
    console.log(`  ${eventType}: ${(threshold * 100).toFixed(1)}%`);
  });
  
  console.log('\n🧪 测试不同用户的计算结果:');
  
  const allResults = [];
  
  testCases.forEach((testCase, index) => {
    console.log(`\n🔍 ${testCase.name}:`);
    console.log(`  八字: ${testCase.bazi.year.gan}${testCase.bazi.year.zhi} ${testCase.bazi.month.gan}${testCase.bazi.month.zhi} ${testCase.bazi.day.gan}${testCase.bazi.day.zhi} ${testCase.bazi.hour.gan}${testCase.bazi.hour.zhi}`);
    
    // 提取五行能量
    const elementEnergies = extractElementEnergies(testCase.bazi);
    console.log(`  五行能量: 金${elementEnergies.金.toFixed(1)}% 木${elementEnergies.木.toFixed(1)}% 水${elementEnergies.水.toFixed(1)}% 火${elementEnergies.火.toFixed(1)}% 土${elementEnergies.土.toFixed(1)}%`);
    
    // 计算平衡度
    const balance = calculateBalance(elementEnergies);
    console.log(`  平衡分数: ${balance.toFixed(1)}分`);
    
    // 计算各维度能量
    const results = {};
    const eventTypes = ['marriage', 'promotion', 'childbirth', 'wealth'];
    
    eventTypes.forEach(eventType => {
      const result = calculateUnifiedEnergy(elementEnergies, eventType, thresholds[eventType]);
      results[eventType] = result;
      
      console.log(`    ${eventType}: ${result.actual.toFixed(1)}% / ${result.required.toFixed(1)}% ${result.met ? '✅' : '❌'}`);
    });
    
    const metCount = Object.values(results).filter(r => r.met).length;
    const metRate = (metCount / eventTypes.length * 100).toFixed(1);
    
    console.log(`  总体达标率: ${metCount}/4 (${metRate}%)`);
    
    allResults.push({
      name: testCase.name,
      elementEnergies: elementEnergies,
      balance: balance,
      results: results,
      metCount: metCount,
      metRate: parseFloat(metRate)
    });
  });
  
  console.log('\n🎯 问题验证:');
  
  // 验证1: 是否所有用户都达标
  const allFullyMet = allResults.every(r => r.metRate === 100);
  console.log(`  问题1 - 所有用户都达标: ${allFullyMet ? '❌ 确实存在' : '✅ 不存在'}`);
  
  if (allFullyMet) {
    console.log('    所有用户达标率都是100%，说明阈值设置过低或计算有误');
  }
  
  // 验证2: 阈值是否都一样
  const uniqueThresholds = [...new Set(Object.values(thresholds))];
  const thresholdsAllSame = uniqueThresholds.length === 1;
  console.log(`  问题2 - 阈值都一样: ${thresholdsAllSame ? '❌ 确实存在' : '✅ 不存在'}`);
  console.log(`    阈值种类: ${uniqueThresholds.length}种 (${uniqueThresholds.map(t => (t*100).toFixed(1)+'%').join(', ')})`);
  
  // 验证3: 平衡分数是否固定
  const balanceScores = allResults.map(r => r.balance);
  const uniqueBalanceScores = [...new Set(balanceScores.map(b => b.toFixed(1)))];
  const balanceScoresAllSame = uniqueBalanceScores.length === 1;
  console.log(`  问题3 - 平衡分数固定: ${balanceScoresAllSame ? '❌ 确实存在' : '✅ 不存在'}`);
  console.log(`    平衡分数: ${balanceScores.map(b => b.toFixed(1)).join(', ')}`);
  
  if (balanceScoresAllSame && balanceScores[0] === 55) {
    console.log('    所有用户平衡分数都是55分，说明计算逻辑有问题');
  }
  
  // 验证4: 能量值是否有差异
  const marriageEnergies = allResults.map(r => r.results.marriage.actual);
  const uniqueMarriageEnergies = [...new Set(marriageEnergies.map(e => e.toFixed(1)))];
  const energiesHaveDifference = uniqueMarriageEnergies.length > 1;
  console.log(`  验证4 - 能量值有差异: ${energiesHaveDifference ? '✅ 有差异' : '❌ 无差异'}`);
  console.log(`    婚姻能量值: ${marriageEnergies.map(e => e.toFixed(1)+'%').join(', ')}`);
  
  console.log('\n🔍 深度问题分析:');
  
  if (allFullyMet) {
    console.log('  🚨 致命问题: 所有用户都100%达标');
    console.log('    可能原因:');
    console.log('    1. 阈值设置过低');
    console.log('    2. 能量计算公式有误');
    console.log('    3. 数据提取逻辑错误');
  }
  
  if (balanceScoresAllSame) {
    console.log('  🚨 致命问题: 平衡分数固定不变');
    console.log('    可能原因:');
    console.log('    1. 五行数据提取错误');
    console.log('    2. 平衡度计算公式有误');
    console.log('    3. 使用了固定的默认值');
  }
  
  if (!energiesHaveDifference) {
    console.log('  🚨 致命问题: 不同八字的能量值相同');
    console.log('    可能原因:');
    console.log('    1. 五行提取逻辑错误');
    console.log('    2. 使用了固定的测试数据');
    console.log('    3. 计算过程中数据被覆盖');
  }
  
  console.log('\n💡 修复建议:');
  console.log('  1. 检查五行数据提取是否正确');
  console.log('  2. 验证平衡度计算公式');
  console.log('  3. 调整阈值设置到合理范围');
  console.log('  4. 确保不同八字产生不同结果');
  console.log('  5. 添加详细的调试日志');
  
  return {
    allFullyMet: allFullyMet,
    thresholdsAllSame: thresholdsAllSame,
    balanceScoresAllSame: balanceScoresAllSame,
    energiesHaveDifference: energiesHaveDifference,
    allResults: allResults
  };
}

// 运行深度调试
deepTimingAnalysisDebug();
