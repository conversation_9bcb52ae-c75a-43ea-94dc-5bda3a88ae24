/**
 * 验证standardizeResult修复效果
 * 测试统一计算器是否正确使用专业级引擎的真实计算结果
 */

// 清除Node.js模块缓存
function clearModuleCache() {
  const moduleKeys = Object.keys(require.cache);
  moduleKeys.forEach(key => {
    if (key.includes('professional_wuxing_engine') || 
        key.includes('unified_wuxing_calculator')) {
      delete require.cache[key];
    }
  });
}

clearModuleCache();

// 重新加载模块
const UnifiedWuxingCalculator = require('../utils/unified_wuxing_calculator_safe.js');

function testStandardizeResultFix() {
  console.log('🧪 ===== standardizeResult修复验证测试 =====\n');
  
  // 测试用例：乙亥年 辛巳月 庚子日 壬午时
  const testBazi = {
    year: { gan: '乙', zhi: '亥' },
    month: { gan: '辛', zhi: '巳' },
    day: { gan: '庚', zhi: '子' },
    hour: { gan: '壬', zhi: '午' }
  };
  
  console.log('📋 测试八字:', testBazi);
  console.log('   完整八字: 乙亥年 辛巳月 庚子日 壬午时\n');
  
  console.log('🧪 测试: 通过统一计算器获取结果');
  
  // 执行计算
  const result = UnifiedWuxingCalculator.calculate(testBazi);
  
  console.log('\n📊 计算结果分析:');
  console.log('   数据来源:', result.source);
  console.log('   算法:', result.algorithm);
  console.log('   版本:', result.version);
  
  console.log('\n   五行力量值:');
  console.log(`     木(wood): ${result.wood}`);
  console.log(`     火(fire): ${result.fire}`);
  console.log(`     土(earth): ${result.earth}`);
  console.log(`     金(metal): ${result.metal}`);
  console.log(`     水(water): ${result.water}`);
  
  console.log('\n   五行强度详情:');
  Object.entries(result.wuxingStrength).forEach(([element, data]) => {
    console.log(`     ${data.chineseName}(${element}): ${data.value} (${data.percentage}%) - ${data.level}`);
  });
  
  console.log('\n🎯 修复验证:');
  
  // 验证1: 不再是固定的50
  const notFixedFifty = !(result.wood === 50 && result.fire === 50 && 
                          result.earth === 50 && result.metal === 50 && 
                          result.water === 50);
  console.log('   摆脱固定50:', notFixedFifty ? '✅ 成功' : '❌ 失败');
  
  // 验证2: 结果是个性化的
  const values = [result.wood, result.fire, result.earth, result.metal, result.water];
  const isPersonalized = !values.every(v => v === values[0]);
  console.log('   结果个性化:', isPersonalized ? '✅ 成功' : '❌ 失败');
  
  // 验证3: 百分比不全是20%
  const percentages = Object.values(result.wuxingStrength).map(data => data.percentage);
  const notAllTwenty = !percentages.every(p => p === 20);
  console.log('   百分比多样化:', notAllTwenty ? '✅ 成功' : '❌ 失败');
  
  // 验证4: 强弱等级不全是"中和"
  const levels = Object.values(result.wuxingStrength).map(data => data.level);
  const notAllZhonghe = !levels.every(l => l === '中和');
  console.log('   等级多样化:', notAllZhonghe ? '✅ 成功' : '❌ 失败');
  
  // 验证5: 数据来源正确
  const correctSource = result.source === 'professional_wuxing_engine';
  console.log('   数据来源正确:', correctSource ? '✅ 成功' : '❌ 失败');
  
  // 验证6: 有原始引擎结果
  const hasRawResult = result.rawEngineResult !== undefined;
  console.log('   保留原始结果:', hasRawResult ? '✅ 成功' : '❌ 失败');
  
  console.log('\n🎉 修复效果总结:');
  const successCount = [notFixedFifty, isPersonalized, notAllTwenty, notAllZhonghe, correctSource, hasRawResult].filter(Boolean).length;
  console.log(`   成功项目: ${successCount}/6`);
  console.log(`   修复状态: ${successCount >= 5 ? '✅ 修复成功' : '❌ 需要进一步修复'}`);
  
  if (successCount >= 5) {
    console.log('\n✅ standardizeResult修复成功！');
    console.log('💡 现在统一计算器返回的是：');
    console.log('   - 基于专业级引擎的真实计算结果');
    console.log('   - 个性化的五行分布');
    console.log('   - 多样化的百分比和等级');
    console.log('   - 不再是硬编码的固定值');
    
    console.log('\n🎯 预期前端效果：');
    console.log('   - 五行力量分布将显示真实的个性化数据');
    console.log('   - 强弱等级将显示多样化的结果');
    console.log('   - 平衡度将不再是100分');
    console.log('   - 所有分析都基于真实八字计算');
  } else {
    console.log('\n❌ 修复不完整，需要进一步调试');
    console.log('💡 可能的问题：');
    console.log('   - 专业级引擎结果格式不匹配');
    console.log('   - 数据转换逻辑有误');
    console.log('   - 还有其他硬编码的地方');
  }
  
  // 显示详细的数据对比
  console.log('\n📋 详细数据对比:');
  console.log('修复前（硬编码）: 木50 火50 土50 金50 水50 (全部20%中和)');
  console.log(`修复后（真实）: 木${result.wood} 火${result.fire} 土${result.earth} 金${result.metal} 水${result.water}`);
  
  const percentageStr = Object.values(result.wuxingStrength)
    .map(data => `${data.chineseName}${data.percentage}%`)
    .join(' ');
  console.log(`百分比分布: ${percentageStr}`);
  
  const levelStr = Object.values(result.wuxingStrength)
    .map(data => `${data.chineseName}${data.level}`)
    .join(' ');
  console.log(`强弱等级: ${levelStr}`);
  
  return {
    success: successCount >= 5,
    details: {
      notFixedFifty,
      isPersonalized,
      notAllTwenty,
      notAllZhonghe,
      correctSource,
      hasRawResult
    },
    result: result
  };
}

// 运行测试
testStandardizeResultFix();
