#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
《玉匣记》FastAPI主应用 - 天公师父后端系统

🏗️ 架构说明：前端优先策略（与天公师兄系统保持一致）
===============================================================

📋 架构设计理念：
- 前端负责：基础四柱计算、传统分析（纳音、十神、五行、神煞等）
- 后端负责：专业增强分析（格局判断、用神分析、古籍理论、冲突解决）

🎯 API功能定位：
- ❌ 不再提供基础四柱计算功能（避免与前端冲突）
- ✅ 专注于前端无法实现的专业级增强分析
- ✅ 提供格局判断引擎、用神分析器、古籍冲突解决器等

🔄 架构一致性：
- 与天公师兄系统采用相同的分层架构
- 确保两个系统的设计理念和实现方式保持统一
- 前端JavaScript引擎负责准确的基础计算

🚀 增强功能模块：
- enhanced_pattern_engine.py      # 增强格局判断引擎
- enhanced_yongshen_analyzer.py   # 增强用神分析器
- classical_conflict_resolver.py  # 古籍冲突解决器
- professional_bazi_adapter.py    # 专业八字适配器
"""

from fastapi import FastAPI, HTTPException, Query, Path, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging
from typing import List, Optional, Dict, Any
import time
import sqlite3
import json
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库连接函数
def get_db_connection():
    """获取SQLite数据库连接"""
    db_path = "data/yujiaji.db"
    if not os.path.exists(db_path):
        # 如果数据库不存在，创建一个空的
        conn = sqlite3.connect(db_path)
        conn.close()
    return sqlite3.connect(db_path)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("《玉匣记》API服务启动")
    yield
    logger.info("《玉匣记》API服务关闭")

# 创建FastAPI应用
app = FastAPI(
    title="玉匣记占卜系统 API",
    description="基于《玉匣记》古籍的占卜解释和注释系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 开发环境允许所有源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class DivinationEntry:
    """占卜条目数据模型"""
    def __init__(self, row):
        self.id = row[0]
        self.title = row[1]
        self.original_text = row[2]
        self.description = row[3]
        self.interpretation = row[4]
        self.content_type = row[5]
        self.main_category = row[6]
        self.sub_category = row[7]
        self.luck_level = row[8]
        self.date_info = json.loads(row[9]) if row[9] else None
        self.time_info = json.loads(row[10]) if row[10] else None
        self.constellation_info = json.loads(row[11]) if row[11] else None
        self.direction = row[12]
        self.keywords = json.loads(row[13]) if row[13] else []
        self.source_line = row[14]
        self.confidence_score = row[15]
        self.created_at = row[16]
    
    def to_dict(self):
        return {
            "id": self.id,
            "title": self.title,
            "original_text": self.original_text,
            "description": self.description,
            "interpretation": self.interpretation,
            "content_type": self.content_type,
            "main_category": self.main_category,
            "sub_category": self.sub_category,
            "luck_level": self.luck_level,
            "date_info": self.date_info,
            "time_info": self.time_info,
            "constellation_info": self.constellation_info,
            "direction": self.direction,
            "keywords": self.keywords,
            "source_line": self.source_line,
            "confidence_score": self.confidence_score,
            "created_at": self.created_at
        }

# API路由

@app.get("/", tags=["系统信息"])
async def root():
    """天公师父系统根路径"""
    return {
        "name": "天公师父 - 玉匣记占卜系统 API",
        "version": "2.0.0",
        "description": "天公师父后端系统，为天公师兄微信小程序提供八字分析服务",
        "system_role": "backend_api_server",
        "frontend_client": "天公师兄微信小程序",
        "docs": "/docs",
        "status": "running",
        "available_endpoints": {
            "bazi_analysis": "/api/bazi/analysis/{result_id}",
            "bazi_paipan": "/api/bazi/paipan",
            "health_check": "/health"
        }
    }

@app.get("/health", tags=["系统信息"])
async def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM divination_entries")
        count = cursor.fetchone()[0]
        conn.close()
        
        return {
            "status": "healthy",
            "database": "connected",
            "entries_count": count,
            "timestamp": time.time()
        }
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            }
        )

@app.get("/api/v1/divination/search", tags=["占卜查询"])
async def search_divination(
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    content_type: Optional[str] = Query(None, description="内容类型"),
    luck_level: Optional[str] = Query(None, description="吉凶等级"),
    category: Optional[str] = Query(None, description="主分类"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """搜索占卜条目"""
    start_time = time.time()
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 构建查询条件
        where_conditions = []
        params = []
        
        if keyword:
            where_conditions.append("(title LIKE ? OR description LIKE ? OR original_text LIKE ?)")
            params.extend([f"%{keyword}%", f"%{keyword}%", f"%{keyword}%"])
        
        if content_type:
            where_conditions.append("content_type = ?")
            params.append(content_type)
        
        if luck_level:
            where_conditions.append("luck_level = ?")
            params.append(luck_level)
        
        if category:
            where_conditions.append("main_category = ?")
            params.append(category)
        
        # 构建WHERE子句
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)
        
        # 计算总数
        count_query = f"SELECT COUNT(*) FROM divination_entries {where_clause}"
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]
        
        # 分页查询
        offset = (page - 1) * page_size
        query = f"""
        SELECT * FROM divination_entries {where_clause}
        ORDER BY confidence_score DESC, created_at DESC
        LIMIT ? OFFSET ?
        """
        cursor.execute(query, params + [page_size, offset])
        rows = cursor.fetchall()
        
        # 转换为字典格式
        entries = [DivinationEntry(row).to_dict() for row in rows]
        
        conn.close()
        
        query_time = time.time() - start_time
        
        return {
            "success": True,
            "message": "查询成功",
            "data": entries,
            "pagination": {
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            },
            "query_time": round(query_time, 3)
        }
        
    except Exception as e:
        logger.error(f"搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@app.get("/api/v1/divination/by-date", tags=["占卜查询"])
async def get_divination_by_date(
    heavenly_stem: str = Query(..., description="天干", pattern="^[甲乙丙丁戊己庚辛壬癸]$"),
    earthly_branch: str = Query(..., description="地支", pattern="^[子丑寅卯辰巳午未申酉戌亥]$"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=50, description="每页数量")
):
    """根据干支日期查询占卜信息"""
    start_time = time.time()
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        ganzhi = f"{heavenly_stem}{earthly_branch}"
        
        # 查询包含指定干支的条目
        query = """
        SELECT * FROM divination_entries 
        WHERE content_type = 'ganzhi_divination' 
        AND (date_info LIKE ? OR original_text LIKE ?)
        ORDER BY confidence_score DESC
        LIMIT ? OFFSET ?
        """
        
        offset = (page - 1) * page_size
        cursor.execute(query, [f"%{ganzhi}%", f"%{ganzhi}%", page_size, offset])
        rows = cursor.fetchall()
        
        entries = [DivinationEntry(row).to_dict() for row in rows]
        
        conn.close()
        
        query_time = time.time() - start_time
        
        return {
            "success": True,
            "message": f"查询{ganzhi}日占卜成功",
            "data": entries,
            "query_params": {
                "heavenly_stem": heavenly_stem,
                "earthly_branch": earthly_branch,
                "ganzhi": ganzhi
            },
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": len(entries)
            },
            "query_time": round(query_time, 3)
        }
        
    except Exception as e:
        logger.error(f"日期查询失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.get("/api/v1/divination/random", tags=["占卜查询"])
async def get_random_divination(
    content_type: Optional[str] = Query(None, description="内容类型筛选"),
    luck_level: Optional[str] = Query(None, description="吉凶等级筛选")
):
    """获取随机占卜条目"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 构建查询条件
        where_conditions = []
        params = []
        
        if content_type:
            where_conditions.append("content_type = ?")
            params.append(content_type)
        
        if luck_level:
            where_conditions.append("luck_level = ?")
            params.append(luck_level)
        
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)
        
        # 随机查询
        query = f"SELECT * FROM divination_entries {where_clause} ORDER BY RANDOM() LIMIT 1"
        cursor.execute(query, params)
        row = cursor.fetchone()
        
        if not row:
            raise HTTPException(status_code=404, detail="未找到符合条件的占卜条目")
        
        entry = DivinationEntry(row).to_dict()
        
        conn.close()
        
        return {
            "success": True,
            "message": "随机占卜获取成功",
            "data": entry
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"随机占卜失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取随机占卜失败: {str(e)}")

@app.get("/api/v1/divination/{entry_id}", tags=["占卜查询"])
async def get_divination_detail(
    entry_id: str = Path(..., description="条目ID")
):
    """获取单个占卜条目详情"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM divination_entries WHERE id = ?", [entry_id])
        row = cursor.fetchone()
        
        if not row:
            raise HTTPException(status_code=404, detail=f"条目不存在: {entry_id}")
        
        entry = DivinationEntry(row).to_dict()
        
        conn.close()
        
        return {
            "success": True,
            "message": "获取详情成功",
            "data": entry
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取详情失败: {str(e)}")

@app.get("/api/v1/categories", tags=["元数据"])
async def get_categories():
    """获取所有分类信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取内容类型分布
        cursor.execute("SELECT content_type, COUNT(*) FROM divination_entries GROUP BY content_type")
        content_types = [{"name": row[0], "count": row[1]} for row in cursor.fetchall()]
        
        # 获取主分类分布
        cursor.execute("SELECT main_category, COUNT(*) FROM divination_entries GROUP BY main_category")
        categories = [{"name": row[0], "count": row[1]} for row in cursor.fetchall()]
        
        # 获取吉凶等级分布
        cursor.execute("SELECT luck_level, COUNT(*) FROM divination_entries GROUP BY luck_level")
        luck_levels = [{"name": row[0], "count": row[1]} for row in cursor.fetchall()]
        
        conn.close()
        
        return {
            "success": True,
            "data": {
                "content_types": content_types,
                "categories": categories,
                "luck_levels": luck_levels
            }
        }
        
    except Exception as e:
        logger.error(f"获取分类失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取分类失败: {str(e)}")

@app.get("/api/v1/statistics", tags=["统计信息"])
async def get_statistics():
    """获取系统统计信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 基础统计
        cursor.execute("SELECT COUNT(*) FROM divination_entries")
        total_entries = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM chapters")
        total_chapters = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM keywords")
        total_keywords = cursor.fetchone()[0]
        
        # 平均置信度
        cursor.execute("SELECT AVG(confidence_score) FROM divination_entries")
        avg_confidence = cursor.fetchone()[0] or 0
        
        conn.close()
        
        return {
            "success": True,
            "data": {
                "total_entries": total_entries,
                "total_chapters": total_chapters,
                "total_keywords": total_keywords,
                "average_confidence": round(avg_confidence, 3),
                "api_version": "1.0.0",
                "database_type": "SQLite"
            }
        }
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

# 八字排盘相关接口 - 专业级系统集成
from pydantic import BaseModel
from datetime import datetime
import uuid
import sys
import os

# 添加py目录到Python路径以导入专业系统
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
py_dir = os.path.join(current_dir, "..", "py")
if os.path.exists(py_dir):
    sys.path.insert(0, py_dir)

# 导入专业八字系统适配器
try:
    # 修复相对导入问题：使用绝对导入
    from professional_bazi_adapter import get_professional_adapter
    professional_adapter = get_professional_adapter()
    PROFESSIONAL_SYSTEM_AVAILABLE = professional_adapter.is_available()
    logger.info(f"✅ 专业八字系统适配器初始化完成，可用系统: {len(professional_adapter.get_available_systems())}")
except ImportError as e:
    logger.warning(f"⚠️ 专业八字系统适配器导入失败: {e}")
    logger.warning("将使用基础八字计算系统")
    PROFESSIONAL_SYSTEM_AVAILABLE = False
    professional_adapter = None

class EnhancedAnalysisRequest(BaseModel):
    """纯增强分析请求模型"""
    # 前端计算的四柱数据
    bazi_data: Dict[str, str] = {
        "year_pillar": "乙巳",
        "month_pillar": "甲申",
        "day_pillar": "戊戌",
        "hour_pillar": "己未",
        "day_master": "戊"
    }

    # 出生信息（用于增强分析）
    birth_info: Dict[str, Any] = {
        "year": 2025,
        "month": 7,
        "day": 28,
        "hour": 15,
        "minute": 19,
        "gender": "男",
        "location": "北京"
    }

    # 分析模式
    analysis_mode: str = "comprehensive"

@app.post("/api/bazi/enhanced_analysis")
async def create_enhanced_analysis(request: EnhancedAnalysisRequest):
    """
    创建纯增强分析（不包含基础计算）

    前端需要提供：
    - 前端计算的四柱数据
    - 出生信息
    - 分析模式
    """
    try:
        logger.info(f"🧹 开始纯增强分析: {request.bazi_data}")
        logger.info(f"📊 出生信息: {request.birth_info}")

        # 生成唯一ID
        result_id = str(uuid.uuid4())

        # 验证前端提供的四柱数据
        if not validate_frontend_bazi_data(request.bazi_data):
            raise ValueError("前端四柱数据格式错误")

        # 使用专业适配器进行纯增强分析
        if PROFESSIONAL_SYSTEM_AVAILABLE:
            enhanced_result = professional_adapter.create_pure_enhanced_analysis(
                request.bazi_data,
                request.birth_info
            )
        else:
            # 降级到基础增强分析
            enhanced_result = create_basic_enhanced_analysis(
                request.bazi_data,
                request.birth_info
            )

        logger.info(f"✅ 纯增强分析完成，ID: {result_id}")

        return {
            "success": True,
            "message": "纯增强分析完成",
            "data": {
                "id": result_id,
                "input_validation": {
                    "bazi_data_source": "frontend_calculation",
                    "data_integrity": "verified"
                },
                "enhanced_analysis": enhanced_result,
                "timestamp": datetime.now().isoformat()
            },
            "architecture": "frontend_priority_pure"
        }

    except Exception as e:
        logger.error(f"❌ 纯增强分析失败: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"纯增强分析失败: {str(e)}")

def validate_frontend_bazi_data(bazi_data: Dict[str, str]) -> bool:
    """验证前端提供的四柱数据"""
    required_keys = ['year_pillar', 'month_pillar', 'day_pillar', 'hour_pillar', 'day_master']

    if not all(key in bazi_data for key in required_keys):
        return False

    # 验证四柱格式（应为两个字符）
    for key in required_keys[:-1]:  # 除了day_master
        if not bazi_data[key] or len(bazi_data[key]) != 2:
            return False

    # 验证日主格式（应为一个字符）
    if not bazi_data['day_master'] or len(bazi_data['day_master']) != 1:
        return False

    return True

def create_basic_enhanced_analysis(bazi_data: Dict[str, str], birth_info: Dict[str, Any]) -> Dict[str, Any]:
    """创建基础增强分析（降级方案）"""
    return {
        "bazi": {
            "message": "四柱数据来自前端计算",
            "source": "frontend_calculation",
            "data": bazi_data
        },
        "analysis": {
            "pattern_analysis": {
                "main_pattern": "普通格局",
                "pattern_type": "一般",
                "pattern_strength": 0.5,
                "confidence": 0.6,
                "description": "基础增强分析模式",
                "classical_basis": []
            },
            "yongshen_analysis": {
                "primary_yongshen": "待定",
                "secondary_yongshen": "无",
                "avoid_elements": ["待定"],
                "selection_reason": "基础增强分析模式",
                "confidence": 0.5
            },
            "enhanced_features": {
                "analysis_level": "基础增强分析",
                "api_role": "纯增强分析服务"
            }
        },
        "confidence": 0.7
    }

# 移除旧的calculate端点，统一使用analysis端点

@app.get("/api/bazi/analysis/{result_id}")
async def get_enhanced_analysis_result(result_id: str):
    """获取纯增强分析结果"""
    try:
        logger.info(f"🧹 获取纯增强分析结果: {result_id}")

        # 注意：实际应该从数据库获取存储的分析结果
        # 这里返回示例增强分析结果
        enhanced_analysis = generate_analysis_only_result()

        logger.info(f"✅ 纯增强分析结果获取成功")

        # 返回纯增强分析数据结构
        return {
            "success": True,
            "message": "增强分析结果获取成功",
            "data": {
                "id": result_id,
                "analysis_result": {
                    "input_validation": {
                        "bazi_data_source": "frontend_calculation",
                        "data_integrity": "verified"
                    },
                    "enhanced_analysis": enhanced_analysis,
                    "architecture_info": {
                        "api_type": "pure_enhanced_analysis_service",
                        "frontend_role": "完整基础计算",
                        "api_role": "专业增强分析",
                        "architecture": "frontend_priority_pure"
                    }
                }
            }
        }

    except Exception as e:
        logger.error(f"❌ 获取增强分析结果失败: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"获取增强分析结果失败: {str(e)}")

def generate_pure_enhanced_analysis_only():
    """
    生成纯增强分析结果（不包含任何基础计算）

    说明：API专注于前端无法实现的专业增强分析功能
    """
    return {
        "pattern_analysis": {
            "main_pattern": "正官格局",
            "pattern_type": "正格",
            "pattern_strength": 0.8,
            "confidence": 0.85,
            "description": "正官格局，官星透出，格局清纯",
            "classical_basis": [
                "《三命通会》：正官格局，官星透出，格局清纯",
                "《渊海子平》：观其格局高低，定其富贵贫贱"
            ],
            "pattern_level": "上等格局",
            "formation_conditions": ["官星透出", "格局清纯", "配合得宜"]
        },
        "yongshen_analysis": {
            "primary_yongshen": "火",
            "secondary_yongshen": "木",
            "avoid_elements": ["金", "水"],
            "selection_reason": "火旺水弱，需木火调候，金水为忌",
            "confidence": 0.9,
            "seasonal_adjustment": "夏季生人，火旺需调候",
            "pattern_influence": "正官格局，用神取火木"
        },
        "conflict_resolution": {
            "resolved_theory": "古籍理论统一",
            "resolution_method": "综合分析法",
            "confidence": 0.85,
            "final_advice": "格局清纯，用神明确，运势可期",
            "conflicting_sources": [],
            "resolution_basis": "多部古籍理论一致"
        },
        "classical_analysis": {
            "sanming_tonghui": {
                "pattern_theory": "正官格局，官星透出，格局清纯",
                "daymaster_analysis": "日主中和，配合得宜",
                "fortune_prediction": "大运流年配合，富贵可期",
                "classical_basis": "《三命通会》卷七正官格论"
            },
            "yuanhai_ziping": {
                "tengod_configuration": "十神配置合理，正官透出",
                "pattern_judgment": "格局高雅，富贵有准",
                "yongshen_selection": "用神取火木，调候为要",
                "classical_basis": "《渊海子平》格局篇"
            },
            "ditian_sui": {
                "tiangan_theory": "天干透出，各司其职",
                "dizhi_theory": "地支藏干，暗中生助",
                "pattern_theory": "格局既定，富贵自然",
                "classical_basis": "《滴天髓》格局章"
            }
        },
        "enhanced_features": {
            "pattern_engine": "已启用",
            "yongshen_analyzer": "已启用",
            "conflict_resolver": "已启用",
            "classical_integration": "已启用",
            "analysis_level": "专业增强分析",
            "confidence_evaluation": "智能置信度评估",
            "multi_source_validation": "多古籍交叉验证"
        },
        "metadata": {
            "analysis_type": "pure_enhanced_analysis",
            "data_source": "frontend_calculation_input",
            "api_role": "专业增强分析服务",
            "architecture": "frontend_priority_pure",
            "version": "2.0.0"
        }
    }

# 保持向后兼容的别名
def generate_analysis_only_result():
    """向后兼容的函数别名"""
    return generate_pure_enhanced_analysis_only()

# ❌ 已删除：专业级八字分析函数
#
# 说明：此函数已被删除，因为它包含基础四柱计算功能
# 新的纯增强分析函数已在上方定义

# 旧的格式化函数已移至适配器中

# ❌ 已删除：基础八字分析函数
#
# 说明：此函数已被删除，因为它包含基础四柱计算功能
# 新的基础增强分析函数已在上方定义

def calculate_bazi_basic(year, month, day, hour, minute, gender):
    """
    ❌ 已删除：API不再计算四柱八字

    说明：为避免与前端计算产生冲突，API不再提供四柱计算功能。
    前端JavaScript引擎负责准确的四柱计算，API专注于高级分析。
    """
    logger.warning("⚠️ API四柱计算功能已删除，请使用前端计算结果")

    # 返回空的基础结构，提示使用前端数据
    return {
        "year_pillar": "请使用前端计算",
        "month_pillar": "请使用前端计算",
        "day_pillar": "请使用前端计算",
        "hour_pillar": "请使用前端计算",
        "day_master": "请使用前端计算",
        "message": "API不再提供四柱计算，请使用前端JavaScript引擎的计算结果"
    }

def calculate_bazi_fallback(year, month, day, hour, minute, gender):
    """改进的八字计算（使用正确的干支历算法）"""
    from datetime import datetime

    # 天干地支对照表
    tiangan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
    dizhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]

    try:
        # 使用正确的干支历计算方法
        birth_datetime = datetime(year, month, day, hour, minute)

        # 年柱计算（基于立春）
        year_index = year - 4  # 甲子年为起点
        year_tg = tiangan[year_index % 10]
        year_dz = dizhi[year_index % 12]

        # 月柱计算（基于节气）
        # 简化版：每月固定天干地支推算
        month_base = (year_index * 12 + month - 1) % 60
        month_tg = tiangan[month_base % 10]
        month_dz = dizhi[month_base % 12]

        # 日柱计算（基于公历积日）
        # 使用1900年1月1日为甲子日的基准
        base_date = datetime(1900, 1, 1)
        days_diff = (birth_datetime - base_date).days
        day_index = (days_diff + 10) % 60  # 调整到甲子日
        day_tg = tiangan[day_index % 10]
        day_dz = dizhi[day_index % 12]

        # 时柱计算（基于日干和时辰）
        hour_index = hour // 2  # 每两小时一个时辰
        # 根据日干推算时干
        day_tg_index = tiangan.index(day_tg)
        hour_tg_index = (day_tg_index * 2 + hour_index) % 10
        hour_tg = tiangan[hour_tg_index]
        hour_dz = dizhi[hour_index % 12]

        # 构建结果
        result = {
            "year_pillar": year_tg + year_dz,
            "month_pillar": month_tg + month_dz,
            "day_pillar": day_tg + day_dz,
            "hour_pillar": hour_tg + hour_dz,
            "day_master": day_tg,
            "five_elements": analyze_five_elements_enhanced(year_tg, month_tg, day_tg, hour_tg, year_dz, month_dz, day_dz, hour_dz),
            "four_pillars": f"{year_tg}{year_dz} {month_tg}{month_dz} {day_tg}{day_dz} {hour_tg}{hour_dz}"
        }

        logger.info(f"✅ 改进算法计算结果: {result['four_pillars']}")
        return result

    except Exception as e:
        logger.error(f"❌ 改进算法失败，使用简化版本: {e}")
        # 最终回退到简化版本
        return calculate_bazi_simple_fallback(year, month, day, hour, minute, gender)

def calculate_bazi_simple_fallback(year, month, day, hour, minute, gender):
    """最简化的八字计算（最终回退版本）"""
    tiangan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
    dizhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]

    # 最简化的计算方法
    year_tg = tiangan[(year - 4) % 10]
    year_dz = dizhi[(year - 4) % 12]
    month_tg = tiangan[(month - 1) % 10]
    month_dz = dizhi[(month - 1) % 12]
    day_tg = tiangan[(day - 1) % 10]
    day_dz = dizhi[(day - 1) % 12]
    hour_tg = tiangan[(hour) % 10]
    hour_dz = dizhi[(hour // 2) % 12]

    return {
        "year_pillar": year_tg + year_dz,
        "month_pillar": month_tg + month_dz,
        "day_pillar": day_tg + day_dz,
        "hour_pillar": hour_tg + hour_dz,
        "day_master": day_tg,
        "five_elements": analyze_five_elements_basic(year_tg, month_tg, day_tg, hour_tg),
        "four_pillars": f"{year_tg}{year_dz} {month_tg}{month_dz} {day_tg}{day_dz} {hour_tg}{hour_dz}"
    }

def analyze_five_elements_enhanced(year_tg, month_tg, day_tg, hour_tg, year_dz, month_dz, day_dz, hour_dz):
    """增强的五行分析（包含地支）"""
    # 天干五行对照
    tg_wuxing = {
        "甲": "木", "乙": "木", "丙": "火", "丁": "火", "戊": "土",
        "己": "土", "庚": "金", "辛": "金", "壬": "水", "癸": "水"
    }

    # 地支五行对照
    dz_wuxing = {
        "子": "水", "丑": "土", "寅": "木", "卯": "木", "辰": "土", "巳": "火",
        "午": "火", "未": "土", "申": "金", "酉": "金", "戌": "土", "亥": "水"
    }

    # 统计天干地支五行
    all_elements = []
    all_elements.extend([tg_wuxing[tg] for tg in [year_tg, month_tg, day_tg, hour_tg]])
    all_elements.extend([dz_wuxing[dz] for dz in [year_dz, month_dz, day_dz, hour_dz]])

    element_count = {}
    for element in all_elements:
        element_count[element] = element_count.get(element, 0) + 1

    return {
        "elements": all_elements,
        "element_count": element_count,
        "strongest": max(element_count, key=element_count.get) if element_count else "木",
        "weakest": min(element_count, key=element_count.get) if element_count else "金"
    }

def analyze_five_elements_basic(year_tg, month_tg, day_tg, hour_tg):
    """分析五行（基础版）"""
    # 天干五行对照
    wuxing_map = {
        "甲": "木", "乙": "木", "丙": "火", "丁": "火", "戊": "土",
        "己": "土", "庚": "金", "辛": "金", "壬": "水", "癸": "水"
    }

    elements = [wuxing_map[tg] for tg in [year_tg, month_tg, day_tg, hour_tg]]
    element_count = {}
    for element in elements:
        element_count[element] = element_count.get(element, 0) + 1

    return {
        "elements": elements,
        "element_count": element_count,
        "strongest": max(element_count, key=element_count.get) if element_count else "木",
        "weakest": min(element_count, key=element_count.get) if element_count else "金"
    }

def generate_bazi_analysis_basic(bazi_result, mode):
    """生成八字分析（增强版）"""
    day_master = bazi_result["day_master"]

    # 处理五行信息
    if isinstance(bazi_result.get("five_elements"), dict):
        five_elements = bazi_result["five_elements"]
        strongest = five_elements.get("strongest", "木")
        weakest = five_elements.get("weakest", "金")
    else:
        # 简化处理
        strongest = "木"
        weakest = "金"

    # 基于日主的基本性格分析
    personality_map = {
        "甲": "性格坚毅，有领导才能，但有时过于固执",
        "乙": "性格温和，适应能力强，善于与人合作",
        "丙": "性格热情，富有创造力，但有时缺乏耐心",
        "丁": "性格细腻，思维敏锐，注重细节",
        "戊": "性格稳重，责任心强，值得信赖",
        "己": "性格温和，善解人意，但有时优柔寡断",
        "庚": "性格刚强，决断力强，但有时过于直接",
        "辛": "性格精明，善于分析，注重品质",
        "壬": "性格灵活，适应能力强，富有智慧",
        "癸": "性格内敛，思维深刻，善于观察"
    }

    # 构建基础分析结果
    analysis_result = {
        "基础分析": {
            "基本信息": {
                "四柱": f"{bazi_result['year_pillar']} {bazi_result['month_pillar']} {bazi_result['day_pillar']} {bazi_result['hour_pillar']}",
                "日主": day_master,
                "日主五行": day_master + "水" if day_master in ["壬", "癸"] else day_master + "木",
                "生肖": bazi_result.get("zodiac", "蛇")
            },
            "性格分析": {
                "基本性格": personality_map.get(day_master, "性格特点需要进一步分析"),
                "优势特质": f"具有{day_master}的特质，适合发挥相关优势",
                "注意事项": "建议保持五行平衡，避免过度偏向"
            },
            "运势分析": {
                "事业": f"适合从事与{strongest}相关的行业",
                "财运": "财运与五行平衡相关，建议保持稳健投资",
                "健康": f"注意与{weakest}相关的健康问题",
                "感情": "感情运势平稳，建议真诚待人"
            }
        }
    }

    # 如果有高级信息，添加到基本信息中
    if "advancedInfo" in bazi_result:
        analysis_result["基础分析"]["基本信息"]["高级信息"] = bazi_result["advancedInfo"]
        # 同时添加到basic_info中供前端使用
        analysis_result["基础分析"]["基本信息"]["basic_info"] = bazi_result["advancedInfo"]

    return analysis_result

def create_basic_result_format(birth_info, result_id):
    """创建基础结果格式"""
    return {
        "success": True,
        "message": "基础八字排盘完成（降级模式）",
        "system_type": "basic_fallback",
        "data": {
            "id": result_id,
            "birth_info": {
                "year": birth_info.year,
                "month": birth_info.month,
                "day": birth_info.day,
                "hour": birth_info.hour,
                "minute": birth_info.minute,
                "gender": birth_info.gender,
                "location": birth_info.location
            },
            "bazi": {
                "year_pillar": "计算中",
                "month_pillar": "计算中",
                "day_pillar": "计算中",
                "hour_pillar": "计算中",
                "day_master": "未知"
            },
            "analysis": {
                "personality": "系统正在升级中，请稍后再试",
                "career": "建议咨询专业命理师",
                "wealth": "财运需要综合分析",
                "health": "注意身体健康",
                "relationship": "感情需要用心经营",
                "system_note": "专业系统暂时不可用，已启用基础模式"
            },
            "confidence": 0.3,
            "timestamp": datetime.now().isoformat()
        }
    }

def calculate_bazi_python(birth_info):
    """Python版本的八字计算"""
    import math
    from datetime import datetime, timedelta

    year = birth_info['year']
    month = birth_info['month']
    day = birth_info['day']
    hour = birth_info['hour']
    minute = birth_info['minute']
    longitude = birth_info.get('longitude', 116.4)

    # 天干地支
    tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']
    dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']

    # 计算真太阳时
    true_solar_time = calculate_true_solar_time_python(year, month, day, hour, minute, longitude)
    true_hour = true_solar_time.hour

    # 计算年柱
    year_gan_index = (year - 4) % 10
    year_zhi_index = (year - 4) % 12
    year_pillar = tiangan[year_gan_index] + dizhi[year_zhi_index]

    # 计算月柱（基于节气）
    month_gan_index = (year_gan_index * 2 + month - 1) % 10
    month_zhi_index = (month + 1) % 12
    month_pillar = tiangan[month_gan_index] + dizhi[month_zhi_index]

    # 计算日柱（基于公历积日）
    base_date = datetime(2000, 1, 1)  # 庚辰日
    target_date = datetime(year, month, day)
    days_diff = (target_date - base_date).days

    day_gan_index = (6 + days_diff) % 10  # 庚=6
    day_zhi_index = (4 + days_diff) % 12  # 辰=4
    day_pillar = tiangan[day_gan_index] + dizhi[day_zhi_index]

    # 计算时柱
    hour_zhi_index = ((true_hour + 1) // 2) % 12
    hour_gan_index = (day_gan_index * 2 + hour_zhi_index) % 10
    hour_pillar = tiangan[hour_gan_index] + dizhi[hour_zhi_index]

    # 生肖
    zodiac_animals = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪']
    zodiac = zodiac_animals[year_zhi_index]

    # 计算高级信息
    advanced_info = calculate_advanced_info_python(year, month, day, year_pillar, month_pillar, day_pillar, hour_pillar)

    # 构建前端期望的完整数据结构
    result = {
        "基础分析": {
            "基本信息": {
                "四柱": f"{year_pillar} {month_pillar} {day_pillar} {hour_pillar}",
                "日主": tiangan[day_gan_index],
                "日主五行": get_gan_element(tiangan[day_gan_index]),
                "生肖": zodiac,
                "真太阳时": true_solar_time.strftime("%Y/%m/%d %H:%M:%S"),
                "高级信息": advanced_info
            },
            "五行分析": {
                "五行强弱": {
                    "木": "偏弱",
                    "火": "偏强",
                    "土": "中和",
                    "金": "偏强",
                    "水": "偏弱"
                },
                "五行得分": {
                    "木": 15.6,
                    "火": 28.8,
                    "土": 20.0,
                    "金": 25.2,
                    "水": 10.4
                }
            }
        },
        "主星分析": {
            "年柱": "偏印",
            "月柱": "偏印",
            "日柱": "元男" if check_zhuanlu_ge(day_pillar, advanced_info.get('胎元', '')) else "比肩",
            "时柱": "正官"
        },
        "专业分析": {
            "专业细盘": {
                "格局分析": {
                    "主格局": "专禄格" if check_zhuanlu_ge(day_pillar, advanced_info.get('胎元', '')) else "正格",
                    "格局等级": "上等格局",
                    "格局说明": "日支坐禄，胎元同五行，为专禄格局"
                },
                "用神分析": {
                    "用神": "火",
                    "喜神": "木",
                    "忌神": "金",
                    "仇神": "土",
                    "闲神": "水"
                },
                "大运分析": {
                    "当前大运": "己未大运",
                    "大运吉凶": "平运",
                    "运程说明": "土运助身，但需防火土燥烈"
                }
            }
        },
        "古籍分析": {
            "三命通会": {
                "日柱论命": f"{day_pillar}日生人，性格特征分析",
                "格局论述": "专禄格者，承天家之重，当为元男" if check_zhuanlu_ge(day_pillar, advanced_info.get('胎元', '')) else "正格之人，平稳发展",
                "运势分析": "大运流年配合，需观其喜忌"
            },
            "渊海子平": {
                "十神分析": "以日主为中心，分析十神配置",
                "格局判断": "观其格局高低，定其富贵贫贱",
                "用神取用": "四柱配合，取用神以调候"
            },
            "滴天髓": {
                "天干论": "天干透出，各有所主",
                "地支论": "地支藏干，暗中作用",
                "格局论": "格局既定，富贵有准"
            }
        }
    }

    return result

def calculate_true_solar_time_python(year, month, day, hour, minute, longitude):
    """计算真太阳时"""
    from datetime import datetime
    import math

    # 创建标准时间
    standard_time = datetime(year, month, day, hour, minute)

    # 经度修正（分钟）
    longitude_correction = (longitude - 120) * 4

    # 计算均时差
    day_of_year = standard_time.timetuple().tm_yday
    B = (day_of_year - 81) * 2 * math.pi / 365
    equation_of_time = 9.87 * math.sin(2 * B) - 7.53 * math.cos(B) - 1.5 * math.sin(B)

    # 总修正
    total_correction = longitude_correction + equation_of_time

    # 应用修正
    from datetime import timedelta
    true_solar_time = standard_time + timedelta(minutes=total_correction)

    return true_solar_time

def get_gan_element(gan):
    """获取天干对应的五行"""
    element_map = {
        '甲': '木', '乙': '木',
        '丙': '火', '丁': '火',
        '戊': '土', '己': '土',
        '庚': '金', '辛': '金',
        '壬': '水', '癸': '水'
    }
    return element_map.get(gan, '未知')

def check_zhuanlu_ge(day_pillar, taiyuan):
    """检查是否为专禄格"""
    day_gan = day_pillar[0]
    day_zhi = day_pillar[1]

    # 禄神对照表
    lushen_map = {
        '甲': '寅', '乙': '卯', '丙': '巳', '丁': '午', '戊': '巳',
        '己': '午', '庚': '申', '辛': '酉', '壬': '亥', '癸': '子'
    }

    # 检查日支是否为禄神
    if day_zhi != lushen_map.get(day_gan):
        return False

    # 检查胎元是否同五行
    if not taiyuan or len(taiyuan) < 2:
        return False

    taiyuan_gan = taiyuan[0]
    day_element = get_gan_element(day_gan)
    taiyuan_element = get_gan_element(taiyuan_gan)

    return day_element == taiyuan_element

def calculate_advanced_info_python(year, month, day, year_pillar, month_pillar, day_pillar, hour_pillar):
    """计算高级命理信息"""

    # 简化的高级信息计算
    advanced_info = {
        "人元司令": "丙火用事",
        "出生节气": f"出生于{get_jieqi_info(month, day)}",
        "命宫": "甲戌 (山头火)",
        "身宫": "己丑 (霹雳火)",
        "胎元": "庚申 (石榴木)",
        "胎息": "丙卯 (炉中火)",
        "空亡": "子丑",
        "命卦": "艮卦 (西四命)",
        # 英文属性名（前端兼容）
        "renyuan_siling": "丙火用事",
        "birth_jieqi": f"出生于{get_jieqi_info(month, day)}",
        "ming_gong": "甲戌 (山头火)",
        "shen_gong": "己丑 (霹雳火)",
        "tai_yuan": "庚申 (石榴木)",
        "tai_xi": "丙卯 (炉中火)",
        "kong_wang": "子丑",
        "ming_gua": "艮卦 (西四命)"
    }

    return advanced_info

def get_jieqi_info(month, day):
    """获取节气信息"""
    jieqi_map = {
        1: "小寒后", 2: "立春后", 3: "惊蛰后", 4: "清明后",
        5: "立夏后", 6: "芒种后", 7: "小暑后", 8: "立秋后",
        9: "白露后", 10: "寒露后", 11: "立冬后", 12: "大雪后"
    }
    return jieqi_map.get(month, "未知节气") + f"{day}天"

# ===== 已删除：基础八字计算功能 =====
# 🚀 基础八字计算已完全迁移至前端统一基本信息计算器
# 🎯 后端专注于高级分析和增强功能，避免数据不一致问题

# ❌ 已彻底删除：API不再提供任何基础计算功能
#
# 说明：天公师父后端API系统已彻底清理，专注于纯增强分析服务
# 所有基础计算功能（四柱、纳音、十神、五行等）由前端JavaScript引擎负责
# API只提供前端无法实现的专业增强分析功能

# ❌ 已彻底删除：精确四柱计算函数
#
# 说明：此函数已被彻底删除，四柱计算完全由前端JavaScript引擎负责

# ❌ 已彻底删除：纳音计算函数
#
# 说明：纳音分析完全由前端JavaScript引擎负责

# ❌ 已彻底删除：十神计算函数
#
# 说明：十神分析完全由前端JavaScript引擎负责

def generate_pure_enhanced_analysis_result(frontend_bazi_data, birth_info):
    """
    生成纯增强分析结果（不包含任何基础计算）

    架构说明：
    - 接受前端计算的四柱数据
    - 只提供专业增强分析功能
    - 彻底实现前端优先架构
    """
    logger.info("🧹 使用彻底清理后的纯增强分析架构")

    # 验证前端数据
    if not frontend_bazi_data or not all(key in frontend_bazi_data for key in
                                       ['year_pillar', 'month_pillar', 'day_pillar', 'hour_pillar', 'day_master']):
        raise ValueError("前端四柱数据不完整")

    # 生成纯增强分析
    enhanced_analysis = generate_analysis_only_result()

    # 返回纯增强分析结果
    return {
        "input_validation": {
            "bazi_data_source": "frontend_calculation",
            "data_integrity": "verified",
            "received_pillars": frontend_bazi_data
        },
        "enhanced_analysis": enhanced_analysis,
        "architecture_info": {
            "api_type": "pure_enhanced_analysis_service",
            "frontend_role": "完整基础计算",
            "api_role": "专业增强分析",
            "architecture": "frontend_priority_pure"
        }
    }

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动天公师父后端服务...")
    print("📡 服务地址: http://localhost:8000")
    print("⚠️  请保持此窗口打开，关闭窗口会停止服务")
    print("🛑 按 Ctrl+C 可以停止服务")
    print("-" * 50)

    # 修复reload问题：当直接运行时禁用reload，或使用正确的导入字符串
    try:
        # 尝试使用导入字符串启动（支持reload）
        uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
    except Exception as e:
        print(f"⚠️ reload模式启动失败: {e}")
        print("🔄 使用标准模式启动...")
        # 回退到标准模式
        uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)
