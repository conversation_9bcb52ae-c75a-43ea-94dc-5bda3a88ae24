#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import json

def validate_final_bindings():
    """最终验证所有数据绑定的正确性"""
    
    print("🔍 最终数据绑定验证")
    print("=" * 50)
    
    # 读取WXML文件
    with open('pages/bazi-result/index.wxml', 'r', encoding='utf-8') as f:
        wxml_content = f.read()
    
    # 提取所有数据绑定
    bindings = re.findall(r'\{\{([^}]+)\}\}', wxml_content)
    
    print(f"📊 总数据绑定: {len(bindings)} 个")
    
    # 分析数据绑定的有效性
    valid_bindings = {
        'baziData': [],
        'professionalAnalysis': [],
        'timingAnalysis': [],
        'liuqinAnalysis': [],
        'classicalAnalysis': [],
        'pageState': [],
        'currentTab': [],
        'item': [],
        'conditional': [],
        'other': []
    }
    
    problematic_bindings = []
    
    for binding in bindings:
        binding = binding.strip()
        
        # 跳过条件表达式
        if '?' in binding or '===' in binding or '||' in binding:
            valid_bindings['conditional'].append(binding)
            continue
        
        # 分类有效绑定
        if binding.startswith('baziData.'):
            valid_bindings['baziData'].append(binding)
        elif binding.startswith('professionalAnalysis.'):
            valid_bindings['professionalAnalysis'].append(binding)
        elif binding.startswith('timingAnalysis.'):
            valid_bindings['timingAnalysis'].append(binding)
        elif binding.startswith('liuqinAnalysis.'):
            valid_bindings['liuqinAnalysis'].append(binding)
        elif binding.startswith('classicalAnalysis.'):
            valid_bindings['classicalAnalysis'].append(binding)
        elif binding.startswith('pageState.'):
            valid_bindings['pageState'].append(binding)
        elif binding == 'currentTab' or binding.startswith('currentTab'):
            valid_bindings['currentTab'].append(binding)
        elif binding.startswith('item.'):
            valid_bindings['item'].append(binding)
        else:
            # 检查是否是问题绑定
            if binding.startswith('unifiedData.'):
                problematic_bindings.append({
                    'binding': binding,
                    'issue': '仍使用unifiedData前缀',
                    'suggestion': binding.replace('unifiedData.', 'baziData.')
                })
            elif binding.startswith('userInfo.') and not binding.startswith('baziData.userInfo.'):
                problematic_bindings.append({
                    'binding': binding,
                    'issue': '直接访问userInfo',
                    'suggestion': binding.replace('userInfo.', 'baziData.userInfo.')
                })
            elif binding.startswith('birthInfo.') and not binding.startswith('baziData.birthInfo.'):
                problematic_bindings.append({
                    'binding': binding,
                    'issue': '直接访问birthInfo',
                    'suggestion': binding.replace('birthInfo.', 'baziData.birthInfo.')
                })
            elif binding.startswith('baziInfo.') and not binding.startswith('baziData.baziInfo.'):
                problematic_bindings.append({
                    'binding': binding,
                    'issue': '直接访问baziInfo',
                    'suggestion': binding.replace('baziInfo.', 'baziData.baziInfo.')
                })
            else:
                valid_bindings['other'].append(binding)
    
    # 输出验证结果
    print("\n✅ 有效数据绑定分类:")
    for category, bindings_list in valid_bindings.items():
        if bindings_list:
            print(f"  {category}: {len(bindings_list)} 个")
    
    # 输出问题绑定
    if problematic_bindings:
        print(f"\n❌ 发现 {len(problematic_bindings)} 个问题绑定:")
        for i, problem in enumerate(problematic_bindings, 1):
            print(f"  {i}. {problem['binding']}")
            print(f"     问题: {problem['issue']}")
            print(f"     建议: {problem['suggestion']}")
            print()
    else:
        print("\n✅ 没有发现问题绑定")
    
    # 检查关键数据绑定的完整性
    print("\n🔍 关键数据绑定检查:")
    
    # 检查基本信息绑定
    basic_info_bindings = [b for b in bindings if any(field in b for field in ['name', 'gender', 'birthDate', 'birthTime', 'location'])]
    print(f"  基本信息绑定: {len(basic_info_bindings)} 个")
    
    # 检查四柱绑定
    pillar_bindings = [b for b in bindings if any(pillar in b for pillar in ['year_gan', 'month_gan', 'day_gan', 'hour_gan', 'year_zhi', 'month_zhi', 'day_zhi', 'hour_zhi'])]
    print(f"  四柱数据绑定: {len(pillar_bindings)} 个")
    
    # 检查分析数据绑定
    analysis_bindings = [b for b in bindings if any(analysis in b for analysis in ['professionalAnalysis', 'timingAnalysis', 'liuqinAnalysis', 'classicalAnalysis'])]
    print(f"  分析数据绑定: {len(analysis_bindings)} 个")
    
    # 检查交互绑定
    interaction_bindings = [b for b in bindings if 'currentTab' in b or 'pageState' in b]
    print(f"  交互状态绑定: {len(interaction_bindings)} 个")
    
    # 生成修复建议
    print(f"\n🎯 修复状态总结:")
    print(f"  ✅ 总数据绑定: {len(bindings)} 个")
    print(f"  ✅ 有效绑定: {len(bindings) - len(problematic_bindings)} 个")
    print(f"  {'❌' if problematic_bindings else '✅'} 问题绑定: {len(problematic_bindings)} 个")
    
    if not problematic_bindings:
        print(f"\n🎉 数据绑定修复完成！")
        print(f"📋 建议测试项目:")
        print(f"  1. 基本信息页面 - 用户信息显示")
        print(f"  2. 四柱排盘页面 - 八字数据显示")
        print(f"  3. 神煞五行页面 - 五行分析显示")
        print(f"  4. 大运流年页面 - 运势数据显示")
        print(f"  5. 格局用神页面 - 专业分析显示")
        print(f"  6. 古籍分析页面 - 古典分析显示")
        print(f"  7. 时运分析页面 - 时运数据显示")
        print(f"  8. 六亲分析页面 - 六亲关系显示")
        print(f"  9. Tab切换功能 - 页面切换正常")
        print(f"  10. 加载状态 - 页面状态管理")
    
    return {
        'total_bindings': len(bindings),
        'valid_bindings': len(bindings) - len(problematic_bindings),
        'problematic_bindings': problematic_bindings,
        'binding_categories': valid_bindings
    }

def generate_test_checklist():
    """生成测试检查清单"""
    
    checklist = {
        "数据显示测试": [
            "基本信息页面显示用户姓名、性别、出生日期等",
            "四柱排盘页面显示年月日时四柱的天干地支",
            "五行分析显示五行统计图表和分析文字",
            "神煞分析显示吉神凶煞列表",
            "大运流年显示当前大运和流年信息",
            "专业分析显示格局、用神等专业内容",
            "古籍分析显示传统命理分析",
            "时运分析显示各方面运势",
            "六亲分析显示配偶、子女等关系分析"
        ],
        "交互功能测试": [
            "Tab页面切换功能正常",
            "页面滚动功能正常",
            "加载状态显示正常",
            "错误状态处理正常",
            "数据为空时的默认显示"
        ],
        "数据绑定测试": [
            "所有数据字段都能正确显示",
            "没有显示undefined或null的情况",
            "条件渲染逻辑正确",
            "循环渲染数据正确",
            "数据更新时页面能正确刷新"
        ]
    }
    
    print(f"\n📋 测试检查清单:")
    for category, items in checklist.items():
        print(f"\n{category}:")
        for i, item in enumerate(items, 1):
            print(f"  {i}. [ ] {item}")

if __name__ == "__main__":
    result = validate_final_bindings()
    generate_test_checklist()
    
    print(f"\n🚀 下一步行动:")
    if result['problematic_bindings']:
        print(f"1. 修复剩余的 {len(result['problematic_bindings'])} 个问题绑定")
        print(f"2. 重新运行验证脚本")
        print(f"3. 进行功能测试")
    else:
        print(f"1. 在微信开发者工具中编译项目")
        print(f"2. 测试页面加载和数据显示")
        print(f"3. 验证所有交互功能")
        print(f"4. 进行真机调试测试")
