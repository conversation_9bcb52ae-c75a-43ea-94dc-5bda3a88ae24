#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def accurate_check(file_path):
    """准确检查WXML文件的标签结构，处理跨行标签"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除注释
    content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
    
    # 使用栈来跟踪标签
    view_stack = []
    
    # 查找所有view标签（包括跨行的）
    view_pattern = r'<(/?view)(?:\s[^>]*)?>'
    matches = re.finditer(view_pattern, content, re.DOTALL)
    
    line_starts = [0]
    for i, char in enumerate(content):
        if char == '\n':
            line_starts.append(i + 1)
    
    def get_line_number(pos):
        for i, start in enumerate(line_starts):
            if i == len(line_starts) - 1 or pos < line_starts[i + 1]:
                return i + 1
        return len(line_starts)
    
    for match in matches:
        tag = match.group(1)
        pos = match.start()
        line_num = get_line_number(pos)
        
        if tag == 'view':
            # 开始标签
            view_stack.append(line_num)
        elif tag == '/view':
            # 结束标签
            if view_stack:
                view_stack.pop()
            else:
                print(f"❌ 第{line_num}行：多余的 </view> 标签")
    
    print(f"🔍 最终分析结果:")
    print(f"  未闭合的 <view> 标签数量: {len(view_stack)}")
    
    if view_stack:
        print(f"  未闭合标签的开始行号: {view_stack}")
        
        # 显示未闭合的标签内容
        lines = content.split('\n')
        print(f"\n📋 未闭合标签的内容:")
        for line_num in view_stack:
            if line_num <= len(lines):
                print(f"  第{line_num}行: {lines[line_num-1].strip()}")
    else:
        print("✅ 所有 <view> 标签都正确闭合！")

if __name__ == "__main__":
    accurate_check("pages/bazi-result/index.wxml")
