// 调试三重引动机制数据流
console.log('🔍 调试三重引动机制数据流...\n');

// 模拟微信小程序环境
global.wx = {
  getStorageSync: () => null,
  setStorageSync: () => {},
  showToast: () => {},
  navigateTo: () => {}
};

// 模拟页面对象的相关方法
const mockPage = {
  extractTripleActivationForUI: function(professionalResults) {
    console.log('📥 输入的professionalResults:', JSON.stringify(professionalResults, null, 2));
    
    const activation = {
      star_activation: '年龄阶段分析中...',
      palace_activation: '年龄阶段分析中...',
      shensha_activation: '年龄阶段分析中...',
      marriage_confidence: 0,
      promotion_confidence: 0,
      childbirth_confidence: 0,
      wealth_confidence: 0
    };

    console.log('🔧 初始activation对象:', JSON.stringify(activation, null, 2));

    // 检查是否有年龄不符的情况
    const hasAgeNotMet = Object.values(professionalResults).some(result =>
      result && result.threshold_status === 'age_not_met'
    );

    console.log(`🔍 年龄检查结果: hasAgeNotMet = ${hasAgeNotMet}`);

    if (hasAgeNotMet) {
      console.log('⚠️ 检测到年龄不符，提前返回');
      activation.star_activation = '当前年龄阶段，星动分析暂不适用';
      activation.palace_activation = '当前年龄阶段，宫动分析暂不适用';
      activation.shensha_activation = '当前年龄阶段，神煞分析暂不适用';
      
      // 为年龄不符的情况也提供基础置信度，避免显示null%
      activation.marriage_confidence = 0;
      activation.promotion_confidence = 0;
      activation.childbirth_confidence = 0;
      activation.wealth_confidence = 0;
      
      console.log('📤 年龄不符时返回的activation:', JSON.stringify(activation, null, 2));
      return activation;
    }

    console.log('✅ 年龄符合，开始处理各事件类型...');

    Object.keys(professionalResults).forEach(eventType => {
      console.log(`\n🔄 处理事件类型: ${eventType}`);
      const result = professionalResults[eventType];
      console.log(`   事件结果:`, JSON.stringify(result, null, 2));
      
      if (result && result.raw_analysis && result.raw_analysis.activation_analysis) {
        console.log('   ✅ 有详细的activation_analysis');
        const activationAnalysis = result.raw_analysis.activation_analysis;

        // 计算置信度（基于激活强度）
        let confidence = 0;
        if (activationAnalysis.star_activation) {
          confidence += activationAnalysis.star_activation.strength * 0.4;
        }
        if (activationAnalysis.palace_activation) {
          confidence += activationAnalysis.palace_activation.strength * 0.3;
        }
        if (activationAnalysis.gods_activation) {
          confidence += activationAnalysis.gods_activation.strength * 0.3;
        }

        console.log(`   计算的置信度: ${confidence} -> ${Math.round(confidence * 100)}%`);
        activation[`${eventType}_confidence`] = Math.round(confidence * 100);

        // 提取具体的引动描述
        if (activationAnalysis.star_activation) {
          const starStrength = Math.round((activationAnalysis.star_activation.strength || 0.7) * 100);
          activation.star_activation = `${activationAnalysis.star_activation.description || this.getDefaultStarActivation(eventType)} (强度: ${starStrength}%)`;
        }
        if (activationAnalysis.palace_activation) {
          const palaceStrength = Math.round((activationAnalysis.palace_activation.strength || 0.6) * 100);
          activation.palace_activation = `${activationAnalysis.palace_activation.description || this.getDefaultPalaceActivation(eventType)} (强度: ${palaceStrength}%)`;
        }
        if (activationAnalysis.gods_activation) {
          const godsStrength = Math.round((activationAnalysis.gods_activation.strength || 0.8) * 100);
          activation.shensha_activation = `${activationAnalysis.gods_activation.description || this.getDefaultGodsActivation(eventType)} (强度: ${godsStrength}%)`;
        }
      } else {
        console.log('   ⚠️ 没有详细的activation_analysis，使用默认值');
        
        // 为没有详细分析结果的情况提供数字化的默认内容
        let confidence = 70; // 默认置信度

        // 根据事件类型设置不同的默认置信度
        if (eventType === 'marriage') {
          confidence = result && result.confidence ? result.confidence * 100 : 75;
        } else if (eventType === 'promotion') {
          confidence = result && result.confidence ? result.confidence * 100 : 68;
        } else if (eventType === 'childbirth') {
          confidence = result && result.confidence ? result.confidence * 100 : 65;
        } else if (eventType === 'wealth') {
          confidence = result && result.confidence ? result.confidence * 100 : 72;
        }

        console.log(`   默认置信度: ${confidence}%`);
        activation[`${eventType}_confidence`] = Math.round(confidence);

        // 为所有事件类型提供默认的数字化分析
        if (!activation.star_activation || activation.star_activation === '年龄阶段分析中...') {
          activation.star_activation = `${this.getDefaultStarActivation(eventType)} (强度: ${Math.round(confidence * 0.8)}%)`;
          console.log(`   更新星动: ${activation.star_activation}`);
        }
        if (!activation.palace_activation || activation.palace_activation === '年龄阶段分析中...') {
          activation.palace_activation = `${this.getDefaultPalaceActivation(eventType)} (强度: ${Math.round(confidence * 0.9)}%)`;
          console.log(`   更新宫动: ${activation.palace_activation}`);
        }
        if (!activation.shensha_activation || activation.shensha_activation === '年龄阶段分析中...') {
          activation.shensha_activation = `${this.getDefaultGodsActivation(eventType)} (强度: ${Math.round(confidence * 0.7)}%)`;
          console.log(`   更新神煞动: ${activation.shensha_activation}`);
        }
      }
      
      console.log(`   当前activation状态:`, JSON.stringify(activation, null, 2));
    });

    console.log('\n📤 最终返回的activation:', JSON.stringify(activation, null, 2));
    return activation;
  },

  getDefaultStarActivation: function(eventType) {
    const descriptions = {
      marriage: '红鸾天喜入命，主婚姻喜事',
      promotion: '官星印星得力，主升职有望',
      childbirth: '食神伤官旺相，主子女缘深',
      wealth: '财星食伤生发，主财运亨通'
    };
    return descriptions[eventType] || '星动分析完成';
  },

  getDefaultPalaceActivation: function(eventType) {
    const descriptions = {
      marriage: '夫妻宫得力，配偶缘分深厚',
      promotion: '事业宫旺相，官运亨通',
      childbirth: '子女宫有情，生育顺利',
      wealth: '财帛宫得用，财源广进'
    };
    return descriptions[eventType] || '宫动分析完成';
  },

  getDefaultGodsActivation: function(eventType) {
    const descriptions = {
      marriage: '天乙贵人护佑，婚姻和谐美满',
      promotion: '将星入命，事业发达',
      childbirth: '天嗣星照，子女聪慧',
      wealth: '金匮星临，财富丰盈'
    };
    return descriptions[eventType] || '神煞分析完成';
  }
};

// 测试用例
function debugTripleActivationDataFlow() {
  console.log('📋 测试场景1：年龄不符的情况');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  const ageNotMetResults = {
    marriage: { threshold_status: 'age_not_met' },
    promotion: { threshold_status: 'age_not_met' },
    childbirth: { threshold_status: 'age_not_met' },
    wealth: { threshold_status: 'age_not_met' }
  };

  const ageNotMetActivation = mockPage.extractTripleActivationForUI(ageNotMetResults);
  
  console.log('\n📋 测试场景2：正常成年用户的情况');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  const normalResults = {
    marriage: { 
      threshold_status: 'met',
      confidence: 0.85
    },
    promotion: { 
      threshold_status: 'met',
      confidence: 0.68
    },
    childbirth: { 
      threshold_status: 'not_met',
      confidence: 0.45
    },
    wealth: { 
      threshold_status: 'met',
      confidence: 0.72
    }
  };

  const normalActivation = mockPage.extractTripleActivationForUI(normalResults);
  
  console.log('\n📋 测试场景3：有详细activation_analysis的情况');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  const detailedResults = {
    marriage: {
      threshold_status: 'met',
      confidence: 0.85,
      raw_analysis: {
        activation_analysis: {
          star_activation: { strength: 0.8, description: '红鸾天喜入命' },
          palace_activation: { strength: 0.7, description: '夫妻宫得力' },
          gods_activation: { strength: 0.9, description: '天乙贵人护佑' }
        }
      }
    }
  };

  const detailedActivation = mockPage.extractTripleActivationForUI(detailedResults);

  return true;
}

// 运行调试
const debugResult = debugTripleActivationDataFlow();

console.log('\n🎯 调试总结:');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log('🔍 数据流分析完成，请检查上述日志找出问题所在');
console.log('📊 重点关注：');
console.log('   1. professionalResults的数据结构');
console.log('   2. 年龄检查逻辑是否正确');
console.log('   3. forEach循环是否正常执行');
console.log('   4. 置信度计算是否正确');
console.log('   5. 描述更新逻辑是否生效');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

console.log(`\n🎯 调试结果: ${debugResult ? '✅ 调试完成' : '❌ 调试失败'}`);
