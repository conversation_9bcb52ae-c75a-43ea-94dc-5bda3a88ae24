/**
 * 年龄验证和应期时间范围分析
 * 解决用户发现的严重问题：
 * 1. 7岁孩子预测2026年生育（11岁）
 * 2. 7岁孩子预测2028年婚姻（13岁）
 * 3. 能量阈值模型的时间范围问题
 */

function ageValidationAndTimingAnalysis() {
  console.log('🧪 ===== 年龄验证和应期时间范围分析 =====\n');
  
  console.log('🚨 用户发现的严重问题:');
  console.log('  输入: 2022年8月4日出生（目前7岁）');
  console.log('  错误预测: 2026年生育（11岁）❌');
  console.log('  错误预测: 2028年婚姻（13岁）❌');
  console.log('  错误预测: 2028年升职（13岁）❌');
  console.log('  这完全违背常识和古籍规则！');
  
  // 1. 年龄计算验证
  console.log('\n📅 年龄计算验证:');
  
  const birthDate = new Date('2022-08-04');
  const currentDate = new Date('2025-01-01'); // 假设当前时间
  const currentAge = currentDate.getFullYear() - birthDate.getFullYear();
  
  console.log(`  出生日期: ${birthDate.toLocaleDateString()}`);
  console.log(`  当前日期: ${currentDate.toLocaleDateString()}`);
  console.log(`  当前年龄: ${currentAge}岁`);
  
  // 2. 古籍权威年龄要求
  console.log('\n📚 古籍权威年龄要求:');
  
  const ancientAgeRequirements = {
    marriage: {
      min_age: 18,
      ancient_basis: '《礼记·内则》：男子二十而冠，女子十五而笄',
      modern_legal: '《婚姻法》：男22岁，女20岁',
      recommended_min: 20
    },
    promotion: {
      min_age: 16,
      ancient_basis: '《论语》：十有五而志于学',
      modern_context: '具备基本工作能力',
      recommended_min: 18
    },
    childbirth: {
      min_age: 18,
      ancient_basis: '《黄帝内经》：女子二七而天癸至',
      modern_medical: '生理成熟，具备生育能力',
      recommended_min: 22
    },
    wealth: {
      min_age: 12,
      ancient_basis: '《论语》：吾十有五而志于学',
      modern_context: '具备基本经济概念',
      recommended_min: 16
    }
  };
  
  Object.entries(ancientAgeRequirements).forEach(([eventType, requirement]) => {
    console.log(`  ${eventType}:`);
    console.log(`    最低年龄: ${requirement.min_age}岁`);
    console.log(`    推荐年龄: ${requirement.recommended_min}岁`);
    console.log(`    古籍依据: ${requirement.ancient_basis}`);
    console.log(`    现代标准: ${requirement.modern_legal || requirement.modern_context || requirement.modern_medical}`);
  });
  
  // 3. 当前年龄验证
  console.log('\n🔍 当前年龄验证结果:');
  
  const validationResults = {};
  Object.entries(ancientAgeRequirements).forEach(([eventType, requirement]) => {
    const isValid = currentAge >= requirement.min_age;
    const isRecommended = currentAge >= requirement.recommended_min;
    const yearsToWait = Math.max(0, requirement.recommended_min - currentAge);
    const earliestYear = currentDate.getFullYear() + yearsToWait;
    
    validationResults[eventType] = {
      valid: isValid,
      recommended: isRecommended,
      yearsToWait: yearsToWait,
      earliestYear: earliestYear,
      currentAge: currentAge,
      minAge: requirement.min_age,
      recommendedAge: requirement.recommended_min
    };
    
    console.log(`  ${eventType}:`);
    console.log(`    当前${currentAge}岁 vs 最低${requirement.min_age}岁: ${isValid ? '✅ 符合' : '❌ 不符合'}`);
    console.log(`    当前${currentAge}岁 vs 推荐${requirement.recommended_min}岁: ${isRecommended ? '✅ 符合' : '❌ 不符合'}`);
    if (!isRecommended) {
      console.log(`    需要等待: ${yearsToWait}年`);
      console.log(`    最早年份: ${earliestYear}年`);
    }
  });
  
  // 4. 能量阈值模型时间范围分析
  console.log('\n⏰ 能量阈值模型时间范围分析:');
  
  const timingRangeAnalysis = {
    short_term: {
      range: '1-3年',
      applicable_events: ['wealth'],
      description: '短期财运变化，基于流年影响'
    },
    medium_term: {
      range: '3-10年',
      applicable_events: ['promotion'],
      description: '中期事业发展，基于大运配合'
    },
    long_term: {
      range: '10-30年',
      applicable_events: ['marriage', 'childbirth'],
      description: '长期人生规划，基于命格特征'
    },
    life_cycle: {
      range: '全生命周期',
      applicable_events: ['marriage', 'promotion', 'childbirth', 'wealth'],
      description: '完整生命周期分析，分阶段预测'
    }
  };
  
  console.log('  时间范围分类:');
  Object.entries(timingRangeAnalysis).forEach(([category, analysis]) => {
    console.log(`    ${category}: ${analysis.range}`);
    console.log(`      适用事件: ${analysis.applicable_events.join('、')}`);
    console.log(`      说明: ${analysis.description}`);
  });
  
  // 5. 修正后的预测结果
  console.log('\n🔧 修正后的预测结果:');
  
  const correctedPredictions = {};
  Object.entries(validationResults).forEach(([eventType, validation]) => {
    if (validation.recommended) {
      // 年龄符合，可以进行预测
      correctedPredictions[eventType] = {
        status: 'can_predict',
        message: `当前${validation.currentAge}岁，符合${eventType}分析要求`,
        prediction_range: `${currentDate.getFullYear() + 1}-${currentDate.getFullYear() + 5}年`,
        confidence: 'high'
      };
    } else {
      // 年龄不符，需要等待
      correctedPredictions[eventType] = {
        status: 'age_not_met',
        message: `当前${validation.currentAge}岁，需要${validation.recommendedAge}岁以上才能进行${eventType}分析`,
        earliest_analysis_year: validation.earliestYear,
        years_to_wait: validation.yearsToWait,
        confidence: 'pending'
      };
    }
  });
  
  console.log('  修正后的预测:');
  Object.entries(correctedPredictions).forEach(([eventType, prediction]) => {
    console.log(`    ${eventType}:`);
    console.log(`      状态: ${prediction.status}`);
    console.log(`      说明: ${prediction.message}`);
    if (prediction.status === 'can_predict') {
      console.log(`      预测范围: ${prediction.prediction_range}`);
    } else {
      console.log(`      最早分析年份: ${prediction.earliest_analysis_year}年`);
      console.log(`      需要等待: ${prediction.years_to_wait}年`);
    }
  });
  
  // 6. 古籍规则符合性检查
  console.log('\n📖 古籍规则符合性检查:');
  
  const ancientRulesCompliance = {
    age_based_analysis: {
      rule: '《滴天髓》：命理分析需要考虑年龄阶段',
      compliance: validationResults.marriage.recommended ? '符合' : '不符合',
      reason: validationResults.marriage.recommended ? 
        '当前年龄适合进行完整命理分析' : 
        '当前年龄过小，不适合进行婚姻生育分析'
    },
    life_stage_theory: {
      rule: '《三命通会》：人生分阶段，各有所宜',
      compliance: currentAge >= 16 ? '部分符合' : '不符合',
      reason: currentAge >= 16 ? 
        '可以进行基础财运和学业分析' : 
        '年龄过小，建议专注学业和健康'
    },
    timing_prediction: {
      rule: '《渊海子平》：应期预测需要合理时间范围',
      compliance: 'needs_correction',
      reason: '当前预测时间范围不合理，需要根据年龄调整'
    }
  };
  
  Object.entries(ancientRulesCompliance).forEach(([aspect, check]) => {
    console.log(`  ${aspect}:`);
    console.log(`    规则: ${check.rule}`);
    console.log(`    符合性: ${check.compliance}`);
    console.log(`    原因: ${check.reason}`);
  });
  
  // 7. 系统修复建议
  console.log('\n🔧 系统修复建议:');
  
  const fixRecommendations = [
    {
      priority: 'critical',
      issue: '年龄验证缺失',
      solution: '在应期分析前增加严格的年龄验证',
      implementation: 'validateAgeForEvent函数需要修复'
    },
    {
      priority: 'critical',
      issue: '不合理的预测时间',
      solution: '根据年龄调整预测时间范围',
      implementation: '7岁孩子不应该预测婚姻生育'
    },
    {
      priority: 'high',
      issue: '古籍规则违背',
      solution: '严格按照古籍年龄要求执行',
      implementation: '参考《礼记》《黄帝内经》等权威古籍'
    },
    {
      priority: 'medium',
      issue: '用户体验问题',
      solution: '为年龄不符的情况提供合理说明',
      implementation: '显示"年龄阶段分析中"而非错误预测'
    }
  ];
  
  fixRecommendations.forEach((rec, index) => {
    console.log(`  ${index + 1}. ${rec.issue} (${rec.priority})`);
    console.log(`     解决方案: ${rec.solution}`);
    console.log(`     实现方式: ${rec.implementation}`);
  });
  
  // 8. 最终结论
  console.log('\n🎯 最终结论:');
  
  const hasAgeIssues = Object.values(validationResults).some(v => !v.recommended);
  const needsSystemFix = hasAgeIssues || currentAge < 16;
  
  console.log(`  年龄验证问题: ${hasAgeIssues ? '❌ 存在' : '✅ 无问题'}`);
  console.log(`  系统需要修复: ${needsSystemFix ? '❌ 是的' : '✅ 不需要'}`);
  
  if (needsSystemFix) {
    console.log('\n🚨 紧急修复要求:');
    console.log('  1. 立即修复年龄验证逻辑');
    console.log('  2. 禁止为未成年人预测婚姻生育');
    console.log('  3. 严格遵循古籍年龄要求');
    console.log('  4. 提供合理的年龄阶段说明');
    
    console.log('\n📱 用户应该看到的正确信息:');
    console.log('  "当前7岁，处于学习成长阶段"');
    console.log('  "婚姻分析需要18岁以上"');
    console.log('  "生育分析需要18岁以上"');
    console.log('  "升职分析需要16岁以上"');
    console.log('  "可以进行基础五行分析和性格分析"');
  }
  
  return {
    currentAge: currentAge,
    hasAgeIssues: hasAgeIssues,
    needsSystemFix: needsSystemFix,
    validationResults: validationResults,
    correctedPredictions: correctedPredictions,
    ancientRulesCompliance: ancientRulesCompliance
  };
}

// 运行年龄验证和应期时间范围分析
ageValidationAndTimingAnalysis();
