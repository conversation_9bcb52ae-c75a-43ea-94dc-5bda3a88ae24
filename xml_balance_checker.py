#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import sys
from collections import defaultdict

def check_xml_balance(file_path):
    """检查XML文件的标签平衡"""
    print(f"🔍 检查XML标签平衡: {file_path}")
    print("=" * 60)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False
    
    # 统计标签
    tag_stack = []
    tag_counts = defaultdict(int)
    line_number = 1
    errors = []
    
    # 匹配所有标签的正则表达式
    tag_pattern = r'<(/?)([a-zA-Z][a-zA-Z0-9-]*)[^>]*?(/?)>'
    
    lines = content.split('\n')
    
    for line_num, line in enumerate(lines, 1):
        matches = re.finditer(tag_pattern, line)
        
        for match in matches:
            is_closing = match.group(1) == '/'
            tag_name = match.group(2)
            is_self_closing = match.group(3) == '/'
            
            # 跳过自闭合标签
            if is_self_closing:
                continue
                
            # 跳过注释和特殊标签
            if tag_name in ['!--', '?xml']:
                continue
            
            if is_closing:
                # 闭合标签
                if not tag_stack:
                    errors.append(f"第{line_num}行: 多余的闭合标签 </{tag_name}>")
                elif tag_stack[-1][0] != tag_name:
                    errors.append(f"第{line_num}行: 标签不匹配，期望 </{tag_stack[-1][0]}>，实际 </{tag_name}>")
                else:
                    tag_stack.pop()
                tag_counts[tag_name] -= 1
            else:
                # 开始标签
                tag_stack.append((tag_name, line_num))
                tag_counts[tag_name] += 1
    
    # 检查未闭合的标签
    for tag_name, line_num in tag_stack:
        errors.append(f"第{line_num}行: 未闭合的标签 <{tag_name}>")
    
    # 输出结果
    print(f"📊 标签统计:")
    for tag_name, count in sorted(tag_counts.items()):
        status = "✅" if count == 0 else "❌"
        print(f"  {status} {tag_name}: {count}")
    
    print(f"\n📋 检查结果:")
    if errors:
        print(f"❌ 发现 {len(errors)} 个问题:")
        for error in errors:
            print(f"  {error}")
        return False
    else:
        print("✅ XML标签完全平衡！")
        return True

def main():
    file_path = "pages/bazi-result/index.wxml"
    
    print("🔍 微信小程序WXML文件XML标签平衡检查")
    print("=" * 60)
    
    success = check_xml_balance(file_path)
    
    if success:
        print("\n🎉 检查通过！WXML文件标签平衡正确。")
        sys.exit(0)
    else:
        print("\n❌ 检查失败！请修复标签平衡问题。")
        sys.exit(1)

if __name__ == "__main__":
    main()
