/**
 * 全面深度检查应期分析4个模块算法
 * 验证每个模块是否正确使用真实数据计算
 */

// 模拟真实的五行数据
const testElementEnergies = {
  金: 45.2,
  木: 23.8,
  水: 67.1,
  火: 34.6,
  土: 29.3
};

// 模拟阈值配置
const testThresholdConfig = {
  spouse_star_threshold: 0.30,      // 30%
  official_seal_threshold: 0.50,    // 50%
  food_injury_threshold: 0.25,      // 25%
  wealth_star_threshold: 0.30,      // 30%
  ancient_basis: '《滴天髓》应期理论'
};

// 模拟4个算法
function calculateMarriageEnergy(elementEnergies, thresholdConfig) {
  const { 金, 木, 水, 火, 土 } = elementEnergies;

  // 配偶星能量（财官星）
  const spouseStarEnergy = (金 + 火) * 0.6; // 官星+财星
  const palaceEnergy = 水 * 0.4; // 夫妻宫能量

  const totalEnergy = spouseStarEnergy + palaceEnergy;
  const percentage = Math.min((totalEnergy / 100) * 100, 100);
  const met = totalEnergy >= (thresholdConfig.spouse_star_threshold * 100);

  return {
    marriage_energy: {
      actual: totalEnergy,
      percentage: percentage.toFixed(1) + '%',
      required: thresholdConfig.spouse_star_threshold * 100,
      met: met,
      completion_ratio: `${percentage.toFixed(1)}% / ${(thresholdConfig.spouse_star_threshold * 100).toFixed(1)}%`
    }
  };
}

function calculatePromotionEnergy(elementEnergies, thresholdConfig) {
  const { 金, 木, 水, 火, 土 } = elementEnergies;

  // 统一后的升职能量计算
  const officialPower = (金 + 水) * 0.6; // 官星力量
  const sealPower = (水 + 木) * 0.4; // 印星力量
  const synergyBonus = Math.min(officialPower, sealPower) * 0.3; // 相生加成

  const totalEnergy = officialPower + sealPower + synergyBonus;
  const percentage = Math.min((totalEnergy / 100) * 100, 100);
  const met = totalEnergy >= (thresholdConfig.official_seal_threshold * 100);

  return {
    promotion_energy: {
      actual: totalEnergy,
      percentage: percentage.toFixed(1) + '%',
      required: thresholdConfig.official_seal_threshold * 100,
      met: met,
      completion_ratio: `${percentage.toFixed(1)}% / ${(thresholdConfig.official_seal_threshold * 100).toFixed(1)}%`
    }
  };
}

function calculateChildbirthEnergy(elementEnergies, thresholdConfig) {
  const { 金, 木, 水, 火, 土 } = elementEnergies;

  // 食伤通关水木能量
  const foodInjuryEnergy = (水 + 木) * 0.7; // 食伤星
  const childrenPalaceEnergy = 火 * 0.3; // 子女宫

  const totalEnergy = foodInjuryEnergy + childrenPalaceEnergy;
  const percentage = Math.min((totalEnergy / 100) * 100, 100);
  const met = totalEnergy >= (thresholdConfig.food_injury_threshold * 100);

  return {
    childbirth_energy: {
      actual: totalEnergy,
      percentage: percentage.toFixed(1) + '%',
      required: thresholdConfig.food_injury_threshold * 100,
      met: met,
      completion_ratio: `${percentage.toFixed(1)}% / ${(thresholdConfig.food_injury_threshold * 100).toFixed(1)}%`
    }
  };
}

function calculateWealthEnergy(elementEnergies, thresholdConfig) {
  const { 金, 木, 水, 火, 土 } = elementEnergies;

  // 统一后的财运能量计算
  const directWealthPower = (木 + 火) * 0.5; // 财星力量
  const treasuryPower = 土 * 0.3; // 财库力量

  // 官贵带财加成（体现升职财运关联）
  const officialPower = (金 + 水) * 0.6;
  const sealPower = (水 + 木) * 0.4;
  const officialBonus = Math.min((officialPower + sealPower) * 0.2, 30); // 最大30点加成

  const totalEnergy = directWealthPower + treasuryPower + officialBonus;
  const percentage = Math.min((totalEnergy / 100) * 100, 100);
  const met = totalEnergy >= (thresholdConfig.wealth_star_threshold * 100);

  return {
    wealth_energy: {
      actual: totalEnergy,
      percentage: percentage.toFixed(1) + '%',
      required: thresholdConfig.wealth_star_threshold * 100,
      met: met,
      completion_ratio: `${percentage.toFixed(1)}% / ${(thresholdConfig.wealth_star_threshold * 100).toFixed(1)}%`
    }
  };
}

// 模拟综合应期计算
function calculateComprehensiveTimingPrediction(energyAnalysis, currentYear = 2024) {
  // 获取能量阈值结果
  const energyKey = Object.keys(energyAnalysis.threshold_results)[0];
  const energyResult = energyAnalysis.threshold_results[energyKey];

  if (!energyResult.met) {
    return {
      threshold_status: 'not_met',
      message: '当前能量阈值未达标，时机尚未成熟',
      confidence: 0.3,
      energy_deficit: energyResult,
      estimated_year: null
    };
  }

  // 能量达标，计算最佳应期
  const bestYear = currentYear + Math.floor(Math.random() * 3) + 1;
  const bestMonth = Math.floor(Math.random() * 12) + 1;

  return {
    threshold_status: 'met',
    best_year: `${bestYear}年${bestMonth}月`,
    best_year_numeric: `${bestYear}.${bestMonth.toString().padStart(2, '0')}`,
    confidence: 0.65,
    energy_analysis: energyResult
  };
}

// 测试函数
function comprehensiveTimingModulesCheck() {
  console.log('🧪 ===== 应期分析4个模块全面检查 =====\n');
  
  console.log('📋 测试数据:');
  console.log('  五行能量:', testElementEnergies);
  console.log('  阈值配置:', testThresholdConfig);
  
  const modules = [
    { name: '婚姻模块', func: calculateMarriageEnergy, eventType: 'marriage' },
    { name: '升职模块', func: calculatePromotionEnergy, eventType: 'promotion' },
    { name: '生育模块', func: calculateChildbirthEnergy, eventType: 'childbirth' },
    { name: '财运模块', func: calculateWealthEnergy, eventType: 'wealth' }
  ];
  
  const moduleResults = [];
  
  modules.forEach((module, index) => {
    console.log(`\n🧪 测试${index + 1}: ${module.name}`);
    
    // 执行算法
    const algorithmResult = module.func(testElementEnergies, testThresholdConfig);
    console.log('  算法输出:', algorithmResult);
    
    // 模拟能量分析结构
    const energyAnalysis = {
      event_type: module.eventType,
      element_energies: testElementEnergies,
      threshold_results: algorithmResult,
      ancient_basis: testThresholdConfig.ancient_basis
    };
    
    // 执行综合应期计算
    const timingResult = calculateComprehensiveTimingPrediction(energyAnalysis);
    console.log('  综合应期:', timingResult);
    
    // 分析结果
    const energyKey = Object.keys(algorithmResult)[0];
    const energyData = algorithmResult[energyKey];
    
    const analysis = {
      module: module.name,
      eventType: module.eventType,
      algorithmResult,
      timingResult,
      energyData,
      
      // 检查项目
      usesRealData: energyData.actual > 0,
      hasCorrectStructure: !!(energyData.actual && energyData.percentage && energyData.required),
      hasReasonableValues: energyData.actual >= 0 && energyData.actual <= 500,
      hasCorrectThreshold: energyData.required === testThresholdConfig[`${module.eventType === 'marriage' ? 'spouse_star' : 
                                                                      module.eventType === 'promotion' ? 'official_seal' :
                                                                      module.eventType === 'childbirth' ? 'food_injury' : 'wealth_star'}_threshold`] * 100,
      timingDataComplete: !!(timingResult.threshold_status && (timingResult.energy_deficit || timingResult.energy_analysis))
    };
    
    moduleResults.push(analysis);
    
    console.log('  ✅ 检查结果:');
    console.log(`    使用真实数据: ${analysis.usesRealData ? '✅' : '❌'}`);
    console.log(`    数据结构正确: ${analysis.hasCorrectStructure ? '✅' : '❌'}`);
    console.log(`    数值合理性: ${analysis.hasReasonableValues ? '✅' : '❌'}`);
    console.log(`    阈值正确: ${analysis.hasCorrectThreshold ? '✅' : '❌'}`);
    console.log(`    应期数据完整: ${analysis.timingDataComplete ? '✅' : '❌'}`);
  });
  
  console.log('\n📊 模块检查汇总:');
  moduleResults.forEach((result, index) => {
    const successCount = [
      result.usesRealData,
      result.hasCorrectStructure,
      result.hasReasonableValues,
      result.hasCorrectThreshold,
      result.timingDataComplete
    ].filter(Boolean).length;
    
    console.log(`  ${result.module}: ${successCount}/5 ${successCount >= 4 ? '✅' : '❌'}`);
  });
  
  console.log('\n🔍 数据流程分析:');
  
  // 验证1: 所有模块都使用真实数据
  const allUseRealData = moduleResults.every(r => r.usesRealData);
  console.log('  所有模块使用真实数据:', allUseRealData ? '✅ 成功' : '❌ 失败');
  
  // 验证2: 数据结构一致性
  const allHaveCorrectStructure = moduleResults.every(r => r.hasCorrectStructure);
  console.log('  数据结构一致性:', allHaveCorrectStructure ? '✅ 成功' : '❌ 失败');
  
  // 验证3: 阈值配置正确
  const allHaveCorrectThresholds = moduleResults.every(r => r.hasCorrectThreshold);
  console.log('  阈值配置正确:', allHaveCorrectThresholds ? '✅ 成功' : '❌ 失败');
  
  // 验证4: 应期计算完整
  const allHaveCompleteTimingData = moduleResults.every(r => r.timingDataComplete);
  console.log('  应期计算完整:', allHaveCompleteTimingData ? '✅ 成功' : '❌ 失败');
  
  // 验证5: 结果个性化
  const energyValues = moduleResults.map(r => r.energyData.actual);
  const uniqueValues = [...new Set(energyValues)];
  const resultsPersonalized = uniqueValues.length > 1;
  console.log('  结果个性化:', resultsPersonalized ? '✅ 成功' : '❌ 失败');
  console.log(`    能量值分布: [${energyValues.map(v => v.toFixed(1)).join(', ')}]`);
  
  console.log('\n🎯 关键问题识别:');
  
  // 检查数据提取兼容性
  moduleResults.forEach(result => {
    const energyData = result.energyData;
    
    console.log(`\n  ${result.module}数据提取兼容性:`);
    console.log(`    算法返回字段: actual=${energyData.actual}, required=${energyData.required}, met=${energyData.met}`);
    
    // 检查前端提取逻辑期望的字段
    const hasExpectedFields = !!(energyData.actual !== undefined && energyData.required !== undefined && energyData.met !== undefined);
    console.log(`    前端兼容性: ${hasExpectedFields ? '✅ 兼容' : '❌ 不兼容'}`);
    
    if (!hasExpectedFields) {
      console.log(`    ⚠️ 问题: 前端提取逻辑期望 actual/required/met 字段，但算法返回了不同的结构`);
    }
  });
  
  console.log('\n🎉 检查总结:');
  const totalSuccessCount = [
    allUseRealData,
    allHaveCorrectStructure,
    allHaveCorrectThresholds,
    allHaveCompleteTimingData,
    resultsPersonalized
  ].filter(Boolean).length;
  
  console.log(`  验证通过: ${totalSuccessCount}/5`);
  console.log(`  模块状态: ${totalSuccessCount >= 4 ? '✅ 算法正常' : '❌ 需要修复'}`);
  
  if (totalSuccessCount >= 4) {
    console.log('\n✅ 4个应期分析模块算法检查通过！');
    console.log('💡 所有模块都：');
    console.log('   - 正确使用真实的五行数据');
    console.log('   - 产生个性化的计算结果');
    console.log('   - 具有正确的数据结构');
    console.log('   - 配置了合理的阈值');
  } else {
    console.log('\n❌ 发现算法问题，需要修复：');
    if (!allUseRealData) console.log('   - 某些模块未使用真实数据');
    if (!allHaveCorrectStructure) console.log('   - 数据结构不一致');
    if (!allHaveCorrectThresholds) console.log('   - 阈值配置错误');
    if (!allHaveCompleteTimingData) console.log('   - 应期计算不完整');
    if (!resultsPersonalized) console.log('   - 结果缺乏个性化');
  }
  
  return {
    success: totalSuccessCount >= 4,
    moduleResults,
    details: {
      allUseRealData,
      allHaveCorrectStructure,
      allHaveCorrectThresholds,
      allHaveCompleteTimingData,
      resultsPersonalized
    }
  };
}

// 运行检查
comprehensiveTimingModulesCheck();
