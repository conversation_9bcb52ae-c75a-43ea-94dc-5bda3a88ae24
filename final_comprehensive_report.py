#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import json
from datetime import datetime

def generate_final_comprehensive_report():
    """生成最终的综合验证报告"""
    
    print("📊 八字结果页面数据显示验证 - 最终综合报告")
    print("=" * 70)
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 读取WXML文件
    with open('pages/bazi-result/index.wxml', 'r', encoding='utf-8') as f:
        wxml_content = f.read()
    
    # 基础统计
    total_lines = len(wxml_content.split('\n'))
    total_bindings = len(re.findall(r'\{\{([^}]+)\}\}', wxml_content))
    
    print("📈 基础统计信息")
    print("-" * 30)
    print(f"文件总行数: {total_lines}")
    print(f"数据绑定总数: {total_bindings}")
    
    # XML结构验证
    view_open = len(re.findall(r'<view[^>]*>', wxml_content))
    view_close = len(re.findall(r'</view>', wxml_content))
    scroll_view_open = len(re.findall(r'<scroll-view[^>]*>', wxml_content))
    scroll_view_close = len(re.findall(r'</scroll-view>', wxml_content))
    
    print(f"<view> 标签: {view_open} 开始, {view_close} 结束")
    print(f"<scroll-view> 标签: {scroll_view_open} 开始, {scroll_view_close} 结束")
    
    xml_balanced = (view_open == view_close) and (scroll_view_open == scroll_view_close)
    print(f"XML结构平衡: {'✅ 正常' if xml_balanced else '❌ 不平衡'}")
    
    # Tab页面检查
    print(f"\n🏷️ Tab页面完整性检查")
    print("-" * 30)
    
    expected_tabs = ['basic', 'paipan', 'advanced', 'fortune', 'professional', 'classical', 'timing', 'liuqin']
    found_tabs = []
    
    for tab in expected_tabs:
        tab_pattern = rf"currentTab === '{tab}'"
        if re.search(tab_pattern, wxml_content):
            found_tabs.append(tab)
    
    print(f"预期页面: {len(expected_tabs)} 个")
    print(f"找到页面: {len(found_tabs)} 个")
    print(f"页面列表: {', '.join(found_tabs)}")
    print(f"页面完整性: {'✅ 完整' if len(found_tabs) == len(expected_tabs) else '❌ 不完整'}")
    
    # 数据绑定分析
    print(f"\n🔗 数据绑定分析")
    print("-" * 30)
    
    bindings = re.findall(r'\{\{([^}]+)\}\}', wxml_content)
    
    binding_categories = {
        'baziData': len([b for b in bindings if 'baziData' in b]),
        'professionalAnalysis': len([b for b in bindings if 'professionalAnalysis' in b]),
        'timingAnalysis': len([b for b in bindings if 'timingAnalysis' in b]),
        'liuqinAnalysis': len([b for b in bindings if 'liuqinAnalysis' in b]),
        'classicalAnalysis': len([b for b in bindings if 'classicalAnalysis' in b]),
        'pageState': len([b for b in bindings if 'pageState' in b]),
        'currentTab': len([b for b in bindings if 'currentTab' in b]),
        'item': len([b for b in bindings if 'item.' in b]),
        'conditional': len([b for b in bindings if any(op in b for op in ['?', '===', '||', '&&'])]),
    }
    
    for category, count in binding_categories.items():
        if count > 0:
            print(f"{category}: {count} 个")
    
    # 问题检查
    print(f"\n⚠️ 潜在问题检查")
    print("-" * 30)
    
    issues = []
    
    # 检查废弃的数据绑定
    deprecated_bindings = [b for b in bindings if b.strip().startswith('unifiedData.')]
    if deprecated_bindings:
        issues.append(f"发现 {len(deprecated_bindings)} 个废弃的 unifiedData 绑定")
    
    # 检查直接访问字段
    direct_access = [b for b in bindings if any(b.strip().startswith(prefix) for prefix in ['userInfo.', 'birthInfo.', 'baziInfo.']) and not b.strip().startswith('baziData.')]
    if direct_access:
        issues.append(f"发现 {len(direct_access)} 个直接访问字段（应通过 baziData 访问）")
    
    # 检查空数据绑定
    empty_bindings = [b for b in bindings if not b.strip()]
    if empty_bindings:
        issues.append(f"发现 {len(empty_bindings)} 个空数据绑定")
    
    if issues:
        for issue in issues:
            print(f"❌ {issue}")
    else:
        print("✅ 未发现明显问题")
    
    # 功能完整性评估
    print(f"\n🎯 功能完整性评估")
    print("-" * 30)
    
    # 检查关键功能组件
    key_components = {
        '加载状态': 'pageState.loading',
        '错误处理': 'pageState.error',
        'Tab切换': 'switchTab',
        '数据显示': 'baziData',
        '条件渲染': 'wx:if',
        '循环渲染': 'wx:for'
    }
    
    component_status = {}
    for component, pattern in key_components.items():
        if pattern in wxml_content:
            component_status[component] = '✅'
        else:
            component_status[component] = '❌'
    
    for component, status in component_status.items():
        print(f"{status} {component}")
    
    # 数据容错性检查
    print(f"\n🛡️ 数据容错性检查")
    print("-" * 30)
    
    fallback_patterns = len(re.findall(r'\|\|', wxml_content))
    default_values = len(re.findall(r"'[^']*'", wxml_content))
    conditional_rendering = len(re.findall(r'wx:if=', wxml_content))
    
    print(f"容错绑定 (||): {fallback_patterns} 个")
    print(f"默认值: {default_values} 个")
    print(f"条件渲染: {conditional_rendering} 个")
    
    # 性能优化检查
    print(f"\n⚡ 性能优化检查")
    print("-" * 30)
    
    # 检查是否有过深的嵌套
    max_nesting = check_max_nesting_depth(wxml_content)
    print(f"最大嵌套深度: {max_nesting} 层 {'✅' if max_nesting <= 10 else '⚠️ 过深'}")
    
    # 检查是否有重复的数据绑定
    unique_bindings = len(set(bindings))
    duplicate_bindings = len(bindings) - unique_bindings
    print(f"重复数据绑定: {duplicate_bindings} 个 {'✅' if duplicate_bindings < 10 else '⚠️ 较多'}")
    
    # 最终评估
    print(f"\n🏆 最终评估")
    print("=" * 30)
    
    score = 0
    max_score = 10
    
    # 评分标准
    if xml_balanced: score += 2
    if len(found_tabs) == len(expected_tabs): score += 2
    if not deprecated_bindings: score += 1
    if not direct_access: score += 1
    if not issues: score += 1
    if all(status == '✅' for status in component_status.values()): score += 2
    if max_nesting <= 10: score += 1
    
    percentage = (score / max_score) * 100
    
    if percentage >= 90:
        grade = "🥇 优秀"
        recommendation = "可以直接进行编译测试"
    elif percentage >= 80:
        grade = "🥈 良好"
        recommendation = "建议进行小幅优化后测试"
    elif percentage >= 70:
        grade = "🥉 及格"
        recommendation = "需要修复部分问题后测试"
    else:
        grade = "❌ 需要改进"
        recommendation = "需要大幅修复后再测试"
    
    print(f"综合评分: {score}/{max_score} ({percentage:.1f}%)")
    print(f"评估等级: {grade}")
    print(f"建议: {recommendation}")
    
    # 下一步行动建议
    print(f"\n🚀 下一步行动建议")
    print("-" * 30)
    
    if percentage >= 90:
        print("1. ✅ 在微信开发者工具中编译项目")
        print("2. ✅ 测试所有tab页面的数据显示")
        print("3. ✅ 验证用户交互功能")
        print("4. ✅ 进行真机调试测试")
    else:
        print("1. 🔧 修复发现的数据绑定问题")
        print("2. 🔧 完善缺失的功能组件")
        print("3. 🔧 优化性能和结构")
        print("4. 🔧 重新运行验证脚本")
    
    return {
        'score': score,
        'max_score': max_score,
        'percentage': percentage,
        'grade': grade,
        'issues': issues,
        'xml_balanced': xml_balanced,
        'tabs_complete': len(found_tabs) == len(expected_tabs)
    }

def check_max_nesting_depth(content):
    """检查最大嵌套深度"""
    lines = content.split('\n')
    max_depth = 0
    current_depth = 0
    
    for line in lines:
        # 计算缩进深度
        indent = len(line) - len(line.lstrip())
        if indent > 0:
            depth = indent // 2  # 假设每层缩进2个空格
            max_depth = max(max_depth, depth)
    
    return max_depth

if __name__ == "__main__":
    result = generate_final_comprehensive_report()
