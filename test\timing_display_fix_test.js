/**
 * 🔧 应期显示修复验证测试
 * 验证前端是否正确显示真实的算法计算结果，而不是硬编码的假数据
 */

const ProfessionalTimingEngine = require('../utils/professional_timing_engine.js');
const TimingPerformanceOptimizer = require('../utils/timing_performance_optimizer.js');
const { TimingAnalysisInterface } = require('../utils/timing_analysis_interfaces.js');

class TimingDisplayFixTest {
  constructor() {
    this.engine = new ProfessionalTimingEngine();
    this.optimizer = new TimingPerformanceOptimizer();
    this.testResults = [];
  }

  /**
   * 执行完整的修复验证测试
   */
  async runCompleteTest() {
    console.log('🔧 开始应期显示修复验证测试...\n');

    // 🆕 测试用例1：刚出生的婴儿（年龄问题测试）
    const babyBazi = {
      birth_year: 2024, // 刚出生
      year: { gan: '甲', zhi: '辰' },
      month: { gan: '丙', zhi: '寅' },
      day: { gan: '戊', zhi: '午' },
      hour: { gan: '壬', zhi: '戌' }
    };

    // 测试用例2：成年人八字
    const adultBazi = {
      birth_year: 1990, // 34岁
      year: { gan: '庚', zhi: '午' },
      month: { gan: '丙', zhi: '寅' },
      day: { gan: '戊', zhi: '午' },
      hour: { gan: '壬', zhi: '戌' }
    };

    const testGender = 'male';
    const currentYear = 2024;

    // 1. 🆕 测试年龄先决条件（婴儿测试）
    await this.testAgeRequirements(babyBazi, testGender, currentYear);

    // 2. 测试算法是否返回真实数据（成年人测试）
    await this.testAlgorithmRealData(adultBazi, testGender, currentYear);

    // 3. 测试数据格式化是否正确
    await this.testDataFormatting(adultBazi, testGender, currentYear);

    // 4. 测试阈值判断逻辑
    await this.testThresholdLogic(adultBazi, testGender, currentYear);

    // 5. 测试前端数据结构
    await this.testFrontendDataStructure(adultBazi, testGender, currentYear);

    // 6. 测试年份格式化
    this.testYearFormatting();

    // 7. 🆕 测试硬编码数据清除
    await this.testHardcodedDataRemoval();

    // 输出测试结果
    this.outputTestResults();
  }

  /**
   * 🆕 测试年龄先决条件
   */
  async testAgeRequirements(bazi, gender, currentYear) {
    console.log('👶 测试1: 验证年龄先决条件...');

    try {
      const eventTypes = ['marriage', 'promotion', 'wealth'];
      let ageTestsPassed = 0;

      for (const eventType of eventTypes) {
        const result = this.engine.analyzeProfessionalTiming(
          bazi, eventType, gender, currentYear, 5
        );

        // 验证年龄不符的处理
        const isAgeNotMet = result.timing_prediction?.threshold_status === 'age_not_met';
        const hasAgeValidation = result.age_validation && !result.age_validation.valid;
        const hasProperMessage = result.timing_prediction?.message?.includes('岁') ||
                                 result.timing_prediction?.message?.includes('年龄');

        if (isAgeNotMet && hasAgeValidation && hasProperMessage) {
          ageTestsPassed++;
          console.log(`  ✅ ${eventType}: 正确识别年龄不符，当前${result.age_validation.current_age}岁`);
        } else {
          console.log(`  ❌ ${eventType}: 年龄验证失败`);
          console.log(`    - 阈值状态: ${result.timing_prediction?.threshold_status}`);
          console.log(`    - 年龄验证: ${result.age_validation?.valid}`);
          console.log(`    - 消息内容: ${result.timing_prediction?.message}`);
        }
      }

      this.addTestResult(
        '年龄先决条件验证',
        ageTestsPassed === eventTypes.length,
        `${ageTestsPassed}/${eventTypes.length} 事件类型正确处理年龄限制`
      );

    } catch (error) {
      this.addTestResult('年龄先决条件验证', false, `年龄测试失败: ${error.message}`);
    }
  }

  /**
   * 测试算法是否返回真实数据
   */
  async testAlgorithmRealData(bazi, gender, currentYear) {
    console.log('📊 测试1: 验证算法返回真实数据...');

    try {
      const eventTypes = ['marriage', 'promotion', 'wealth'];
      
      for (const eventType of eventTypes) {
        const result = this.engine.analyzeProfessionalTiming(
          bazi, eventType, gender, currentYear, 5
        );

        // 验证结果不是空的或默认值
        const hasRealData = result && 
          result.timing_prediction && 
          result.confidence > 0 &&
          result.analysis_timestamp;

        const isNotHardcoded = !this.isHardcodedResult(result, eventType);

        this.addTestResult(
          `${eventType}算法真实数据`,
          hasRealData && isNotHardcoded,
          hasRealData ? '✅ 返回真实计算结果' : '❌ 返回空数据或默认值'
        );

        if (hasRealData) {
          console.log(`  ✅ ${eventType}: 置信度=${result.confidence}, 阈值状态=${result.timing_prediction?.threshold_status}`);
        }
      }
    } catch (error) {
      this.addTestResult('算法真实数据', false, `算法执行失败: ${error.message}`);
    }
  }

  /**
   * 测试数据格式化是否正确
   */
  async testDataFormatting(bazi, gender, currentYear) {
    console.log('\n📋 测试2: 验证数据格式化...');

    try {
      const result = this.engine.analyzeProfessionalTiming(
        bazi, 'marriage', gender, currentYear, 5
      );

      const formattedResult = TimingAnalysisInterface.formatResponse(result);

      // 验证格式化结果
      const hasCorrectStructure = formattedResult &&
        typeof formattedResult.confidence === 'number' &&
        formattedResult.threshold_status &&
        formattedResult.triggers;

      const hasCorrectPeriod = formattedResult.threshold_status === 'met' ? 
        formattedResult.period !== null : 
        formattedResult.period === null;

      this.addTestResult(
        '数据格式化结构',
        hasCorrectStructure,
        hasCorrectStructure ? '✅ 格式化结构正确' : '❌ 格式化结构错误'
      );

      this.addTestResult(
        '应期数据逻辑',
        hasCorrectPeriod,
        hasCorrectPeriod ? '✅ 应期数据逻辑正确' : '❌ 应期数据逻辑错误'
      );

      console.log(`  格式化结果: period=${formattedResult.period}, threshold=${formattedResult.threshold_status}`);

    } catch (error) {
      this.addTestResult('数据格式化', false, `格式化失败: ${error.message}`);
    }
  }

  /**
   * 测试阈值判断逻辑
   */
  async testThresholdLogic(bazi, gender, currentYear) {
    console.log('\n⚖️ 测试3: 验证阈值判断逻辑...');

    try {
      const eventTypes = ['marriage', 'promotion', 'wealth'];
      let thresholdTestsPassed = 0;

      for (const eventType of eventTypes) {
        const result = this.engine.analyzeProfessionalTiming(
          bazi, eventType, gender, currentYear, 5
        );

        const thresholdStatus = result.timing_prediction?.threshold_status;
        const hasSpecificYear = result.timing_prediction?.best_timing?.year;

        // 逻辑一致性检查
        const logicConsistent = 
          (thresholdStatus === 'met' && hasSpecificYear) ||
          (thresholdStatus === 'not_met' && !hasSpecificYear) ||
          (thresholdStatus === 'unknown');

        if (logicConsistent) {
          thresholdTestsPassed++;
          console.log(`  ✅ ${eventType}: 阈值=${thresholdStatus}, 逻辑一致`);
        } else {
          console.log(`  ❌ ${eventType}: 阈值=${thresholdStatus}, 年份=${hasSpecificYear}, 逻辑矛盾`);
        }
      }

      this.addTestResult(
        '阈值逻辑一致性',
        thresholdTestsPassed === eventTypes.length,
        `${thresholdTestsPassed}/${eventTypes.length} 事件类型逻辑一致`
      );

    } catch (error) {
      this.addTestResult('阈值判断逻辑', false, `阈值测试失败: ${error.message}`);
    }
  }

  /**
   * 测试前端数据结构
   */
  async testFrontendDataStructure(bazi, gender, currentYear) {
    console.log('\n🖥️ 测试4: 验证前端数据结构...');

    try {
      // 模拟前端数据处理流程
      const eventTypes = ['marriage', 'promotion', 'wealth'];
      const professionalResults = {};

      for (const eventType of eventTypes) {
        const analysisResult = this.engine.analyzeProfessionalTiming(
          bazi, eventType, gender, currentYear, 5
        );

        const thresholdStatus = analysisResult.timing_prediction?.threshold_status || 'unknown';

        if (thresholdStatus === 'not_met') {
          professionalResults[eventType] = {
            threshold_status: 'not_met',
            best_year: null,
            message: analysisResult.timing_prediction.message || '能量阈值未达标，时机尚未成熟'
          };
        } else if (thresholdStatus === 'met') {
          const formattedResult = TimingAnalysisInterface.formatResponse(analysisResult);
          professionalResults[eventType] = {
            threshold_status: 'met',
            best_year: formattedResult.period,
            confidence: analysisResult.confidence
          };
        } else {
          professionalResults[eventType] = {
            threshold_status: 'unknown',
            best_year: null,
            message: '正在计算中...'
          };
        }
      }

      // 验证前端数据结构
      const hasCorrectStructure = Object.keys(professionalResults).every(eventType => {
        const result = professionalResults[eventType];
        return result.threshold_status && 
               (result.threshold_status === 'met' ? result.best_year : result.message);
      });

      this.addTestResult(
        '前端数据结构',
        hasCorrectStructure,
        hasCorrectStructure ? '✅ 前端数据结构正确' : '❌ 前端数据结构错误'
      );

      console.log('  前端数据结构预览:');
      Object.keys(professionalResults).forEach(eventType => {
        const result = professionalResults[eventType];
        console.log(`    ${eventType}: ${result.threshold_status}, ${result.best_year || result.message}`);
      });

    } catch (error) {
      this.addTestResult('前端数据结构', false, `前端测试失败: ${error.message}`);
    }
  }

  /**
   * 测试年份格式化
   */
  testYearFormatting() {
    console.log('\n📅 测试5: 验证年份格式化...');

    try {
      // 测试年份转干支
      const testYears = [2024, 2025, 2026, 2027];
      let formatCorrect = true;

      testYears.forEach(year => {
        const ganZhi = this.getYearGanZhi(year);
        const formatted = `${year}年${ganZhi}`;
        
        // 验证格式
        const isValidFormat = /^\d{4}年[甲乙丙丁戊己庚辛壬癸][子丑寅卯辰巳午未申酉戌亥]$/.test(formatted);
        
        if (!isValidFormat) {
          formatCorrect = false;
        }
        
        console.log(`  ${year} -> ${formatted} ${isValidFormat ? '✅' : '❌'}`);
      });

      this.addTestResult(
        '年份格式化',
        formatCorrect,
        formatCorrect ? '✅ 年份格式化正确' : '❌ 年份格式化错误'
      );

    } catch (error) {
      this.addTestResult('年份格式化', false, `格式化测试失败: ${error.message}`);
    }
  }

  /**
   * 🆕 测试硬编码数据清除
   */
  async testHardcodedDataRemoval() {
    console.log('\n🧹 测试7: 验证硬编码数据清除...');

    try {
      // 检查前端WXML文件是否还有硬编码
      const fs = require('fs');
      const path = require('path');

      const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
      const wxmlContent = fs.readFileSync(wxmlPath, 'utf8');

      const hardcodedPatterns = [
        '2025年乙巳',
        '2026年丙午',
        '2027年丁未',
        '婚姻运旺',
        '升职运佳'
      ];

      let hardcodedFound = 0;
      hardcodedPatterns.forEach(pattern => {
        if (wxmlContent.includes(pattern)) {
          hardcodedFound++;
          console.log(`  ❌ 发现硬编码: ${pattern}`);
        }
      });

      const isClean = hardcodedFound === 0;

      this.addTestResult(
        '硬编码数据清除',
        isClean,
        isClean ? '✅ 前端已清除所有硬编码数据' : `❌ 仍有${hardcodedFound}个硬编码残留`
      );

    } catch (error) {
      this.addTestResult('硬编码数据清除', false, `检查失败: ${error.message}`);
    }
  }

  /**
   * 检查是否为硬编码结果
   */
  isHardcodedResult(result, eventType) {
    const hardcodedYears = ['2025年乙巳', '2026年丙午', '2027年丁未'];
    const bestYear = result.timing_prediction?.best_timing?.year;

    return hardcodedYears.includes(bestYear);
  }

  /**
   * 获取年份干支（复制前端逻辑）
   */
  getYearGanZhi(year) {
    const gan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    const zhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    
    const baseYear = 1984;
    const offset = year - baseYear;
    
    const ganIndex = (offset % 10 + 10) % 10;
    const zhiIndex = (offset % 12 + 12) % 12;
    
    return gan[ganIndex] + zhi[zhiIndex];
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, passed, details) {
    this.testResults.push({
      name: testName,
      passed: passed,
      details: details
    });
  }

  /**
   * 输出测试结果
   */
  outputTestResults() {
    console.log('\n' + '='.repeat(60));
    console.log('🔧 应期显示修复验证测试结果');
    console.log('='.repeat(60));

    const passedTests = this.testResults.filter(test => test.passed).length;
    const totalTests = this.testResults.length;

    this.testResults.forEach(test => {
      const status = test.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${test.name}: ${test.details}`);
    });

    console.log('\n' + '-'.repeat(60));
    console.log(`📊 总体结果: ${passedTests}/${totalTests} 测试通过 (${(passedTests/totalTests*100).toFixed(1)}%)`);
    
    if (passedTests === totalTests) {
      console.log('🎉 所有测试通过！应期显示修复成功！');
    } else {
      console.log('⚠️ 部分测试失败，需要进一步修复');
    }
    
    console.log('='.repeat(60));
  }
}

// 执行测试
if (require.main === module) {
  const test = new TimingDisplayFixTest();
  test.runCompleteTest().catch(console.error);
}

module.exports = TimingDisplayFixTest;
