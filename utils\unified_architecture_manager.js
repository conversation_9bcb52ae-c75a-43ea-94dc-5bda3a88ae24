// utils/unified_architecture_manager.js
// 🎯 统一架构管理器 - 防止前后端同时计算和数据源混乱

/**
 * 🚨 统一架构管理器
 * 
 * 核心职责：
 * 1. 确保只有一个计算引擎运行
 * 2. 防止数据源混乱
 * 3. 管理计算状态和结果缓存
 * 4. 提供架构一致性检查
 */
class UnifiedArchitectureManager {
  constructor() {
    this.isCalculating = false;
    this.calculationSource = null;
    this.lastCalculationResult = null;
    this.calculationHistory = [];
    this.architectureMode = 'unified_frontend'; // 强制前端模式
    
    console.log('🎯 统一架构管理器初始化完成');
    console.log('📊 架构模式:', this.architectureMode);
  }

  /**
   * 🔒 计算锁定机制 - 防止同时计算
   */
  acquireCalculationLock(source) {
    if (this.isCalculating) {
      console.warn(`⚠️ 计算正在进行中，来源: ${this.calculationSource}`);
      console.warn(`❌ 拒绝新的计算请求，来源: ${source}`);
      return false;
    }
    
    this.isCalculating = true;
    this.calculationSource = source;
    console.log(`🔒 计算锁定成功，来源: ${source}`);
    return true;
  }

  /**
   * 🔓 释放计算锁
   */
  releaseCalculationLock() {
    if (!this.isCalculating) {
      console.warn('⚠️ 尝试释放未锁定的计算锁');
      return;
    }
    
    console.log(`🔓 计算锁释放，来源: ${this.calculationSource}`);
    this.isCalculating = false;
    this.calculationSource = null;
  }

  /**
   * 🚨 架构一致性检查
   */
  validateArchitectureConsistency() {
    const issues = [];
    
    // 检查是否存在后端计算文件
    const backendFiles = [
      'utils/professional_timing_engine.js',
      'utils/timing_performance_optimizer.js'
    ];
    
    backendFiles.forEach(file => {
      try {
        require.resolve(file);
        issues.push(`❌ 发现后端计算文件: ${file}`);
      } catch (e) {
        // 文件不存在，这是好的
      }
    });
    
    // 检查架构模式
    if (this.architectureMode !== 'unified_frontend') {
      issues.push(`❌ 架构模式错误: ${this.architectureMode}，应该是 unified_frontend`);
    }
    
    // 检查计算历史中的数据源一致性
    const uniqueSources = [...new Set(this.calculationHistory.map(h => h.source))];
    if (uniqueSources.length > 1) {
      issues.push(`❌ 检测到多个计算源: ${uniqueSources.join(', ')}`);
    }
    
    return {
      isConsistent: issues.length === 0,
      issues: issues,
      recommendation: issues.length > 0 ? '需要清理架构不一致问题' : '架构一致性良好'
    };
  }

  /**
   * 🎯 统一计算入口 - 唯一的计算接口
   */
  executeUnifiedCalculation(calculationFunction, params, source = 'frontend') {
    // 🔒 获取计算锁
    if (!this.acquireCalculationLock(source)) {
      return {
        success: false,
        error: 'CALCULATION_IN_PROGRESS',
        message: '计算正在进行中，请稍后重试',
        current_source: this.calculationSource
      };
    }

    try {
      // 🚨 强制检查架构一致性
      const consistencyCheck = this.validateArchitectureConsistency();
      if (!consistencyCheck.isConsistent) {
        console.error('🚨 架构一致性检查失败:', consistencyCheck.issues);
        throw new Error(`架构不一致: ${consistencyCheck.issues.join('; ')}`);
      }

      // 📊 记录计算开始
      const calculationStart = Date.now();
      console.log(`🚀 开始统一计算，来源: ${source}`);

      // 🎯 执行实际计算
      const result = calculationFunction(params);

      // 📊 记录计算结果
      const calculationEnd = Date.now();
      const calculationRecord = {
        timestamp: calculationEnd,
        source: source,
        duration: calculationEnd - calculationStart,
        success: true,
        result_hash: this.generateResultHash(result)
      };

      this.calculationHistory.push(calculationRecord);
      this.lastCalculationResult = result;

      console.log(`✅ 统一计算完成，耗时: ${calculationRecord.duration}ms`);
      
      return {
        success: true,
        result: result,
        metadata: {
          source: source,
          architecture_mode: this.architectureMode,
          calculation_time: calculationRecord.duration,
          consistency_validated: true
        }
      };

    } catch (error) {
      console.error('❌ 统一计算失败:', error.message);
      
      // 记录失败的计算
      this.calculationHistory.push({
        timestamp: Date.now(),
        source: source,
        success: false,
        error: error.message
      });

      return {
        success: false,
        error: 'CALCULATION_FAILED',
        message: error.message,
        source: source
      };
      
    } finally {
      // 🔓 始终释放计算锁
      this.releaseCalculationLock();
    }
  }

  /**
   * 🔍 数据源一致性检查
   */
  checkDataSourceConsistency() {
    if (this.calculationHistory.length === 0) {
      return {
        consistent: true,
        message: '暂无计算历史'
      };
    }

    const sources = this.calculationHistory.map(h => h.source);
    const uniqueSources = [...new Set(sources)];
    
    if (uniqueSources.length === 1 && uniqueSources[0] === 'frontend') {
      return {
        consistent: true,
        message: '数据源一致，全部来自前端计算',
        source: 'frontend'
      };
    } else {
      return {
        consistent: false,
        message: `检测到多个数据源: ${uniqueSources.join(', ')}`,
        sources: uniqueSources,
        recommendation: '需要统一到前端计算'
      };
    }
  }

  /**
   * 🧹 清理不一致的计算历史
   */
  cleanupInconsistentHistory() {
    const beforeCount = this.calculationHistory.length;
    
    // 只保留前端计算的历史
    this.calculationHistory = this.calculationHistory.filter(h => h.source === 'frontend');
    
    const afterCount = this.calculationHistory.length;
    const cleanedCount = beforeCount - afterCount;
    
    console.log(`🧹 清理完成，移除 ${cleanedCount} 条非前端计算记录`);
    
    return {
      cleaned_records: cleanedCount,
      remaining_records: afterCount,
      message: `清理了 ${cleanedCount} 条不一致的计算记录`
    };
  }

  /**
   * 🔧 生成结果哈希（用于检测结果一致性）
   */
  generateResultHash(result) {
    const resultString = JSON.stringify(result, Object.keys(result).sort());
    let hash = 0;
    for (let i = 0; i < resultString.length; i++) {
      const char = resultString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(16);
  }

  /**
   * 📊 获取架构状态报告
   */
  getArchitectureStatusReport() {
    const consistencyCheck = this.validateArchitectureConsistency();
    const dataSourceCheck = this.checkDataSourceConsistency();
    
    return {
      architecture_mode: this.architectureMode,
      is_calculating: this.isCalculating,
      current_source: this.calculationSource,
      consistency: consistencyCheck,
      data_source_consistency: dataSourceCheck,
      calculation_history_count: this.calculationHistory.length,
      last_calculation: this.calculationHistory[this.calculationHistory.length - 1] || null,
      recommendations: this.generateRecommendations(consistencyCheck, dataSourceCheck)
    };
  }

  /**
   * 💡 生成架构优化建议
   */
  generateRecommendations(consistencyCheck, dataSourceCheck) {
    const recommendations = [];
    
    if (!consistencyCheck.isConsistent) {
      recommendations.push('🔧 清理后端计算文件，确保纯前端架构');
    }
    
    if (!dataSourceCheck.consistent) {
      recommendations.push('🎯 统一所有计算到前端引擎');
      recommendations.push('🧹 清理混合数据源的计算历史');
    }
    
    if (this.calculationHistory.length > 100) {
      recommendations.push('📊 清理过期的计算历史记录');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('✅ 架构状态良好，无需优化');
    }
    
    return recommendations;
  }
}

// 🎯 创建全局单例实例
const unifiedArchitectureManager = new UnifiedArchitectureManager();

module.exports = unifiedArchitectureManager;
