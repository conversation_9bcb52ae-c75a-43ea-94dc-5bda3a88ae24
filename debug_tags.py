#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def debug_tags(file_path):
    """调试标签问题"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除注释
    content_no_comments = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
    
    # 查找所有view相关的标签
    all_view_tags = re.findall(r'</?view[^>]*>', content_no_comments)
    
    print(f"📊 所有view标签统计:")
    print(f"  总共找到 {len(all_view_tags)} 个view相关标签")
    
    # 分类统计
    opens = 0
    closes = 0
    self_closing = 0
    
    for tag in all_view_tags:
        if tag.startswith('</'):
            closes += 1
        elif tag.endswith('/>'):
            self_closing += 1
        else:
            opens += 1
    
    print(f"  开始标签: {opens}")
    print(f"  结束标签: {closes}")
    print(f"  自闭合标签: {self_closing}")
    print(f"  差值: {opens - closes}")
    
    # 显示一些标签示例
    print(f"\n📋 前20个标签:")
    for i, tag in enumerate(all_view_tags[:20]):
        print(f"  {i+1}: {tag}")
    
    # 检查是否有跨行的标签
    lines = content.split('\n')
    print(f"\n🔍 检查跨行标签:")
    for line_num, line in enumerate(lines, 1):
        if '<view' in line and '>' not in line:
            print(f"  第{line_num}行可能有跨行标签: {line.strip()}")

if __name__ == "__main__":
    debug_tags("pages/bazi-result/index.wxml")
