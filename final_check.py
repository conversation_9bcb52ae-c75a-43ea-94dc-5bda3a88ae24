#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def final_check(file_path):
    """最终检查WXML文件的标签结构"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除注释
    content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
    
    # 计算标签数量
    view_opens = len(re.findall(r'<view(?:\s[^>]*)?(?<!/)>', content))
    view_closes = len(re.findall(r'</view>', content))
    
    scroll_opens = len(re.findall(r'<scroll-view(?:\s[^>]*)?(?<!/)>', content))
    scroll_closes = len(re.findall(r'</scroll-view>', content))
    
    print(f"📊 标签统计:")
    print(f"  <view> 开始标签: {view_opens}")
    print(f"  </view> 结束标签: {view_closes}")
    print(f"  <scroll-view> 开始标签: {scroll_opens}")
    print(f"  </scroll-view> 结束标签: {scroll_closes}")
    
    print(f"\n🔍 平衡检查:")
    if view_opens == view_closes:
        print(f"  ✅ view标签平衡: {view_opens} = {view_closes}")
    else:
        print(f"  ❌ view标签不平衡: {view_opens} ≠ {view_closes}")
    
    if scroll_opens == scroll_closes:
        print(f"  ✅ scroll-view标签平衡: {scroll_opens} = {scroll_closes}")
    else:
        print(f"  ❌ scroll-view标签不平衡: {scroll_opens} ≠ {scroll_closes}")
    
    # 总体结果
    if view_opens == view_closes and scroll_opens == scroll_closes:
        print(f"\n🎉 恭喜！WXML文件结构完全正确！")
        return True
    else:
        print(f"\n⚠️  WXML文件结构仍有问题需要修复。")
        return False

if __name__ == "__main__":
    final_check("pages/bazi-result/index.wxml")
