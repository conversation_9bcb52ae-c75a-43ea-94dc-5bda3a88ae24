#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def check_scroll_view(file_path):
    """检查scroll-view标签的结构"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除注释
    content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
    
    # 使用栈来跟踪scroll-view标签
    scroll_stack = []
    
    # 查找所有scroll-view标签（包括跨行的）
    scroll_pattern = r'<(/?scroll-view)(?:\s[^>]*)?>'
    matches = re.finditer(scroll_pattern, content, re.DOTALL)
    
    line_starts = [0]
    for i, char in enumerate(content):
        if char == '\n':
            line_starts.append(i + 1)
    
    def get_line_number(pos):
        for i, start in enumerate(line_starts):
            if i == len(line_starts) - 1 or pos < line_starts[i + 1]:
                return i + 1
        return len(line_starts)
    
    for match in matches:
        tag = match.group(1)
        pos = match.start()
        line_num = get_line_number(pos)
        
        if tag == 'scroll-view':
            # 开始标签
            scroll_stack.append(line_num)
            print(f"📍 第{line_num}行：<scroll-view> 开始标签")
        elif tag == '/scroll-view':
            # 结束标签
            if scroll_stack:
                start_line = scroll_stack.pop()
                print(f"📍 第{line_num}行：</scroll-view> 结束标签，闭合第{start_line}行的开始标签")
            else:
                print(f"❌ 第{line_num}行：多余的 </scroll-view> 标签")
    
    print(f"\n🔍 最终分析结果:")
    print(f"  未闭合的 <scroll-view> 标签数量: {len(scroll_stack)}")
    
    if scroll_stack:
        print(f"  未闭合标签的开始行号: {scroll_stack}")
    else:
        print("✅ 所有 <scroll-view> 标签都正确闭合！")

if __name__ == "__main__":
    check_scroll_view("pages/bazi-result/index.wxml")
