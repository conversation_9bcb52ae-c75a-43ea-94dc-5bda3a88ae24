#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
玉匣记八字排盘主系统
整合所有功能模块，提供完整的八字排盘和分析服务
基于《玉匣记》古籍理论，结合现代专业排盘标准
"""

import sys
import os
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import json

# 导入各个功能模块
try:
    from 专业细盘维度系统 import ProfessionalDetailSystem
    from 古籍理论集成管理器 import ClassicalTheoryManager
except ImportError as e:
    print(f"⚠️ 模块导入失败: {e}")
    print("请确保所有依赖模块都在同一目录下")

class AnalysisMode(Enum):
    """分析模式"""
    BASIC = "基础模式"           # 基础排盘
    PROFESSIONAL = "专业模式"    # 专业细盘
    CLASSICAL = "古籍模式"       # 古籍理论
    COMPREHENSIVE = "综合模式"   # 综合分析

@dataclass
class BirthInfo:
    """出生信息"""
    year: int                    # 出生年
    month: int                   # 出生月
    day: int                     # 出生日
    hour: int                    # 出生时
    minute: int                  # 出生分
    gender: str                  # 性别
    location: str = "北京"       # 出生地
    timezone: int = 8            # 时区

@dataclass
class PaipanResult:
    """排盘结果"""
    birth_info: BirthInfo        # 出生信息
    four_pillars: List[Tuple[str, str]]  # 四柱
    analysis_mode: AnalysisMode  # 分析模式
    analysis_result: Dict        # 分析结果
    generation_time: datetime    # 生成时间
    confidence: float            # 可信度

class YuxiangjiPaipanSystem:
    """玉匣记八字排盘主系统"""
    
    def __init__(self):
        """初始化玉匣记排盘系统"""
        print("🏛️ 初始化玉匣记八字排盘系统...")
        print("   📚 《玉匣记》- 中华古典命理学经典")
        print("   🔮 集成现代专业排盘技术")
        print("   ⚖️ 融合六部古籍理论精华")
        
        # 初始化各个子系统
        self.professional_system = None
        self.classical_manager = None
        
        try:
            self.professional_system = ProfessionalDetailSystem()
            print("   ✅ 专业细盘系统加载成功")
        except Exception as e:
            print(f"   ⚠️ 专业细盘系统加载失败: {e}")
        
        try:
            self.classical_manager = ClassicalTheoryManager()
            print("   ✅ 古籍理论管理器加载成功")
        except Exception as e:
            print(f"   ⚠️ 古籍理论管理器加载失败: {e}")
        
        # 初始化基础数据
        self._init_basic_data()
        
        print("🎯 玉匣记八字排盘系统初始化完成")
        print("=" * 80)
    
    def _init_basic_data(self):
        """初始化基础数据"""
        # 天干地支
        self.tiangan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
        self.dizhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
        
        # 五行属性
        self.wuxing_map = {
            "甲": "木", "乙": "木", "丙": "火", "丁": "火", "戊": "土",
            "己": "土", "庚": "金", "辛": "金", "壬": "水", "癸": "水",
            "子": "水", "丑": "土", "寅": "木", "卯": "木", "辰": "土",
            "巳": "火", "午": "火", "未": "土", "申": "金", "酉": "金",
            "戌": "土", "亥": "水"
        }
        
        # 阴阳属性
        self.yinyang_map = {
            "甲": "阳", "乙": "阴", "丙": "阳", "丁": "阴", "戊": "阳",
            "己": "阴", "庚": "阳", "辛": "阴", "壬": "阳", "癸": "阴",
            "子": "阳", "丑": "阴", "寅": "阳", "卯": "阴", "辰": "阳",
            "巳": "阴", "午": "阳", "未": "阴", "申": "阳", "酉": "阴",
            "戌": "阳", "亥": "阴"
        }
        
        # 十神关系
        self.shishen_map = {
            ("木", "木"): "比肩/劫财", ("木", "火"): "食神/伤官",
            ("木", "土"): "正财/偏财", ("木", "金"): "正官/七杀",
            ("木", "水"): "正印/偏印", ("火", "火"): "比肩/劫财",
            ("火", "土"): "食神/伤官", ("火", "金"): "正财/偏财",
            ("火", "水"): "正官/七杀", ("火", "木"): "正印/偏印",
            ("土", "土"): "比肩/劫财", ("土", "金"): "食神/伤官",
            ("土", "水"): "正财/偏财", ("土", "木"): "正官/七杀",
            ("土", "火"): "正印/偏印", ("金", "金"): "比肩/劫财",
            ("金", "水"): "食神/伤官", ("金", "木"): "正财/偏财",
            ("金", "火"): "正官/七杀", ("金", "土"): "正印/偏印",
            ("水", "水"): "比肩/劫财", ("水", "木"): "食神/伤官",
            ("水", "火"): "正财/偏财", ("水", "土"): "正官/七杀",
            ("水", "金"): "正印/偏印"
        }
        
        # 地支藏干
        self.canggan_map = {
            "子": ["癸"], "丑": ["己", "癸", "辛"], "寅": ["甲", "丙", "戊"],
            "卯": ["乙"], "辰": ["戊", "乙", "癸"], "巳": ["丙", "戊", "庚"],
            "午": ["丁", "己"], "未": ["己", "丁", "乙"], "申": ["庚", "壬", "戊"],
            "酉": ["辛"], "戌": ["戊", "辛", "丁"], "亥": ["壬", "甲"]
        }
        
        # 纳音五行
        self.nayin_map = {
            ("甲子", "乙丑"): "海中金", ("丙寅", "丁卯"): "炉中火",
            ("戊辰", "己巳"): "大林木", ("庚午", "辛未"): "路旁土",
            ("壬申", "癸酉"): "剑锋金", ("甲戌", "乙亥"): "山头火",
            ("丙子", "丁丑"): "涧下水", ("戊寅", "己卯"): "城头土",
            ("庚辰", "辛巳"): "白蜡金", ("壬午", "癸未"): "杨柳木",
            ("甲申", "乙酉"): "泉中水", ("丙戌", "丁亥"): "屋上土",
            ("戊子", "己丑"): "霹雳火", ("庚寅", "辛卯"): "松柏木",
            ("壬辰", "癸巳"): "长流水", ("甲午", "乙未"): "砂中金",
            ("丙申", "丁酉"): "山下火", ("戊戌", "己亥"): "平地木",
            ("庚子", "辛丑"): "壁上土", ("壬寅", "癸卯"): "金箔金",
            ("甲辰", "乙巳"): "覆灯火", ("丙午", "丁未"): "天河水",
            ("戊申", "己酉"): "大驿土", ("庚戌", "辛亥"): "钗钏金",
            ("壬子", "癸丑"): "桑柘木", ("甲寅", "乙卯"): "大溪水",
            ("丙辰", "丁巳"): "沙中土", ("戊午", "己未"): "天上火",
            ("庚申", "辛酉"): "石榴木", ("壬戌", "癸亥"): "大海水"
        }
    
    def create_paipan(self, birth_info: BirthInfo, 
                     analysis_mode: AnalysisMode = AnalysisMode.COMPREHENSIVE) -> PaipanResult:
        """创建八字排盘"""
        print(f"🔮 开始创建八字排盘...")
        print(f"   👤 性别: {birth_info.gender}")
        print(f"   📅 出生: {birth_info.year}年{birth_info.month}月{birth_info.day}日 {birth_info.hour}:{birth_info.minute:02d}")
        print(f"   🎯 模式: {analysis_mode.value}")
        
        # 1. 计算四柱
        four_pillars = self._calculate_four_pillars(birth_info)
        print(f"   📊 四柱: {self._format_four_pillars(four_pillars)}")
        
        # 2. 根据模式进行分析
        analysis_result = self._perform_analysis(four_pillars, birth_info, analysis_mode)
        
        # 3. 创建排盘结果
        result = PaipanResult(
            birth_info=birth_info,
            four_pillars=four_pillars,
            analysis_mode=analysis_mode,
            analysis_result=analysis_result,
            generation_time=datetime.now(),
            confidence=self._calculate_confidence(four_pillars, analysis_result)
        )
        
        print(f"✅ 八字排盘创建完成 (可信度: {result.confidence:.1%})")
        return result
    
    def _calculate_four_pillars(self, birth_info: BirthInfo) -> List[Tuple[str, str]]:
        """计算四柱 - 优先使用前端传入的权威四柱数据"""

        # 🔧 修正：优先使用前端传入的权威四柱数据
        if hasattr(birth_info, 'four_pillars') and birth_info.four_pillars:
            print(f"🎯 使用前端权威万年历四柱数据")

            # 解析前端传入的四柱数据
            four_pillars = birth_info.four_pillars
            if isinstance(four_pillars, str):
                # 如果是字符串格式："乙未 癸未 庚子 丙戌"
                pillars = four_pillars.strip().split()
                result = []
                for pillar in pillars:
                    if len(pillar) == 2:
                        result.append((pillar[0], pillar[1]))
                    else:
                        print(f"⚠️ 四柱格式错误: {pillar}")
                        break
                else:
                    print(f"📊 前端权威四柱: {self._format_four_pillars(result)}")
                    return result
            elif isinstance(four_pillars, list) and len(four_pillars) == 4:
                # 如果是列表格式：[("乙", "未"), ("癸", "未"), ("庚", "子"), ("丙", "戌")]
                result = four_pillars
                print(f"📊 前端权威四柱: {self._format_four_pillars(result)}")
                return result

            print(f"⚠️ 前端四柱数据格式不正确，回退到后端计算")

        # 回退到后端计算（保留原有逻辑作为备用）
        print(f"🔮 回退使用后端四柱计算")
        birth_datetime = datetime(
            birth_info.year, birth_info.month, birth_info.day,
            birth_info.hour, birth_info.minute
        )

        print(f"📅 输入阳历时间: {birth_datetime}")

        # 1. 真太阳时校正（假设北京经度116.4074）
        longitude = getattr(birth_info, 'longitude', 116.4074)
        true_solar_time = self._calculate_true_solar_time(birth_datetime, longitude)
        print(f"🌞 真太阳时: {true_solar_time}")

        # 2. 使用正确的干支历计算四柱
        year_pillar = self._get_year_pillar_correct(true_solar_time)
        month_pillar = self._get_month_pillar_correct(true_solar_time, year_pillar[0])
        day_pillar = self._get_day_pillar_correct(true_solar_time)
        hour_pillar = self._get_hour_pillar_correct(true_solar_time, day_pillar[0])

        result = [year_pillar, month_pillar, day_pillar, hour_pillar]
        print(f"📊 后端计算四柱: {self._format_four_pillars(result)}")

        return result

    def _calculate_true_solar_time(self, birth_datetime: datetime, longitude: float) -> datetime:
        """计算真太阳时（增强版，包含均时差）"""
        import math

        # 1. 时区差校正（经度校正）
        # 中国以东经120°为基准，每度经度相差4分钟
        timezone_offset = (120 - longitude) * 4  # 单位：分钟

        # 2. 计算均时差
        equation_of_time = self._calculate_equation_of_time(birth_datetime)

        # 3. 总修正量
        total_correction = timezone_offset + equation_of_time

        # 4. 应用修正得到真太阳时
        true_solar_time = birth_datetime + timedelta(minutes=total_correction)

        # 5. 处理日期边界进位
        if true_solar_time.hour >= 23 and birth_datetime.hour < 23:
            print(f"⚠️ 真太阳时跨日：{birth_datetime} -> {true_solar_time}")

        print(f"🌞 真太阳时校正: 经度={longitude}°, 时区差={timezone_offset:.1f}分, 均时差={equation_of_time:.1f}分, 总修正={total_correction:.1f}分")

        return true_solar_time

    def _calculate_equation_of_time(self, dt: datetime) -> float:
        """计算均时差（单位：分钟）"""
        import math

        # 计算年内天数
        day_of_year = dt.timetuple().tm_yday

        # 均时差近似公式（按建议文档）
        # n = 年内天数 - 1
        n = day_of_year - 1

        # 弧度制计算
        B = 2 * math.pi * n / 365.25

        # 均时差公式：9.87*sin(2*B) - 7.53*cos(B) - 1.5*sin(B)
        equation_of_time = 9.87 * math.sin(2 * B) - 7.53 * math.cos(B) - 1.5 * math.sin(B)

        return equation_of_time

    def _get_year_pillar(self, year: int) -> Tuple[str, str]:
        """获取年柱"""
        # 以1984年甲子为基准计算
        base_year = 1984
        offset = (year - base_year) % 60
        
        gan_index = offset % 10
        zhi_index = offset % 12
        
        return (self.tiangan[gan_index], self.dizhi[zhi_index])
    
    def _get_month_pillar(self, year: int, month: int) -> Tuple[str, str]:
        """获取月柱"""
        # 简化的月柱计算
        year_gan_index = self.tiangan.index(self._get_year_pillar(year)[0])
        
        # 月干计算公式
        month_gan_index = (year_gan_index * 2 + month - 1) % 10
        
        # 月支固定对应
        month_zhi_map = {
            1: 2, 2: 3, 3: 4, 4: 5, 5: 6, 6: 7,
            7: 8, 8: 9, 9: 10, 10: 11, 11: 0, 12: 1
        }
        month_zhi_index = month_zhi_map[month]
        
        return (self.tiangan[month_gan_index], self.dizhi[month_zhi_index])
    
    def _get_day_pillar(self, birth_datetime: datetime) -> Tuple[str, str]:
        """获取日柱"""
        # 以1900年1月1日甲子为基准
        base_date = datetime(1900, 1, 1)
        days_diff = (birth_datetime - base_date).days
        
        gan_index = days_diff % 10
        zhi_index = days_diff % 12
        
        return (self.tiangan[gan_index], self.dizhi[zhi_index])
    
    def _get_hour_pillar(self, day_gan: str, hour: int) -> Tuple[str, str]:
        """获取时柱"""
        # 时支对应
        hour_zhi_map = {
            (23, 1): 0, (1, 3): 1, (3, 5): 2, (5, 7): 3,
            (7, 9): 4, (9, 11): 5, (11, 13): 6, (13, 15): 7,
            (15, 17): 8, (17, 19): 9, (19, 21): 10, (21, 23): 11
        }
        
        hour_zhi_index = 0
        for (start, end), zhi_idx in hour_zhi_map.items():
            if start <= hour < end or (start == 23 and hour >= 23):
                hour_zhi_index = zhi_idx
                break
        
        # 时干计算
        day_gan_index = self.tiangan.index(day_gan)
        hour_gan_index = (day_gan_index * 2 + hour_zhi_index) % 10
        
        return (self.tiangan[hour_gan_index], self.dizhi[hour_zhi_index])

    # ==================== 正确的干支历计算方法 ====================

    def _get_year_pillar_correct(self, true_solar_time: datetime) -> Tuple[str, str]:
        """正确的年柱计算 - 以立春节气为界"""
        year = true_solar_time.year

        # 获取当年立春精确时间（需要实现精确节气计算）
        lichun_datetime = self._get_solar_term_datetime(year, '立春')

        # 如果还没到立春，使用上一年
        if true_solar_time < lichun_datetime:
            year = year - 1

        print(f"🌸 年柱计算: 实际干支年为 {year} 年，立春时间: {lichun_datetime}")

        # 🔧 修正：使用公元4年为甲子年基准，与前端保持一致
        # 干支序号 = (year - 4) % 60，公元4年为甲子年
        ganzhi_index = (year - 4) % 60

        # 天干地支索引计算
        # 天干：0=癸,1=甲,2=乙...9=壬
        # 地支：0=亥,1=子,2=丑...11=戌
        gan_index = ganzhi_index % 10
        zhi_index = ganzhi_index % 12

        return (self.tiangan[gan_index], self.dizhi[zhi_index])

    def _get_month_pillar_correct(self, true_solar_time: datetime, year_gan: str) -> Tuple[str, str]:
        """正确的月柱计算 - 以节气为界"""
        year = true_solar_time.year

        # 获取当月节气时间进行精确判断
        month_index = self._get_ganzhi_month_index(true_solar_time)

        # 🔧 修正：地支索引映射
        # month_index返回的是月份序号，需要转换为地支索引
        # 1=寅月->寅(2), 2=卯月->卯(3), ..., 6=未月->未(7), 7=申月->申(8)
        if month_index == 0:  # 子月
            zhi_index = 0
        elif month_index <= 6:  # 丑月到未月
            zhi_index = month_index + 1  # 丑(1)到未(7)
        else:  # 申月到亥月
            zhi_index = month_index + 1  # 申(8)到亥(11)

        # 🔧 修正：五虎遁表（年上起月）- 根据传统口诀
        # 甲己之年丙作首，乙庚之年戊为头，丙辛之年庚寅上，丁壬壬寅顺水流，戊癸之年甲寅始
        wuhu_dun_table = {
            '甲': 2, '己': 2,  # 甲己之年丙作首 (丙=2)
            '乙': 4, '庚': 4,  # 乙庚之年戊为头 (戊=4)
            '丙': 6, '辛': 6,  # 丙辛之年庚寅上 (庚=6)
            '丁': 8, '壬': 8,  # 丁壬壬寅顺水流 (壬=8)
            '戊': 0, '癸': 0   # 戊癸之年甲寅始 (甲=0)
        }

        # 月干计算：年干对应的起始天干 + 月支索引
        base_gan_index = wuhu_dun_table[year_gan]
        month_gan_index = (base_gan_index + month_index) % 10

        print(f"🌙 月柱计算: {true_solar_time.month}月{true_solar_time.day}日 -> {self.tiangan[month_gan_index]}{self.dizhi[zhi_index]}")

        return (self.tiangan[month_gan_index], self.dizhi[zhi_index])

    def _get_ganzhi_month_index(self, true_solar_time: datetime) -> int:
        """根据节气精确确定干支月份索引"""
        year = true_solar_time.year
        month = true_solar_time.month
        day = true_solar_time.day

        # 🔧 修正：简化的节气月份判断，与前端保持一致
        # 7月23日应该是未月（大暑前），不是申月
        if month == 7:
            if day >= 23:  # 大暑后进入申月
                return 7  # 申月索引
            else:  # 大暑前还是未月
                return 6  # 未月索引

        # 其他月份的简化判断
        month_map = {
            1: 1,   # 1月 -> 丑月
            2: 2,   # 2月 -> 寅月
            3: 3,   # 3月 -> 卯月
            4: 4,   # 4月 -> 辰月
            5: 5,   # 5月 -> 巳月
            6: 6,   # 6月 -> 午月
            8: 8,   # 8月 -> 申月
            9: 9,   # 9月 -> 酉月
            10: 10, # 10月 -> 戌月
            11: 11, # 11月 -> 亥月
            12: 0   # 12月 -> 子月
        }

        return month_map.get(month, month)

    def _get_day_pillar_correct(self, true_solar_time: datetime) -> Tuple[str, str]:
        """正确的日柱计算 - 标准儒略日算法"""
        import math

        year = true_solar_time.year
        month = true_solar_time.month
        day = true_solar_time.day

        # 处理1582年历法改革断层
        if year == 1582 and month == 10 and 5 <= day <= 14:
            raise ValueError("1582年10月5-14日不存在（格里高利历法改革）")

        # 儒略日计算（可处理公元前）
        y = year
        m = month
        if m <= 2:
            y -= 1
            m += 12

        A = y // 100
        B = 2 - A + (A // 4)

        # 标准儒略日公式
        JD = math.floor(365.25 * (y + 4716)) + math.floor(30.6001 * (m + 1)) + day + B - 1524.5

        # 日干支序号（基准：公元1年1月1日为乙酉日）
        # 按建议文档：日干支号 = (int(JD) + 49) % 60
        day_ganzhi_index = (int(JD) + 49) % 60

        # 六十甲子转换
        # 天干：0=癸,1=甲,2=乙...9=壬
        # 地支：0=亥,1=子,2=丑...11=戌
        gan_index = day_ganzhi_index % 10
        zhi_index = day_ganzhi_index % 12

        print(f"☀️ 日柱计算: JD={JD:.1f}, 干支序号={day_ganzhi_index} -> {self.tiangan[gan_index]}{self.dizhi[zhi_index]}")

        return (self.tiangan[gan_index], self.dizhi[zhi_index])

    def _get_hour_pillar_correct(self, true_solar_time: datetime, day_gan: str) -> Tuple[str, str]:
        """正确的时柱计算 - 基于真太阳时"""
        hour = true_solar_time.hour
        minute = true_solar_time.minute

        # 严格2小时制时辰划分（按建议文档）
        time_decimal = hour + minute / 60.0

        # 时辰表：(起始时间, 结束时间, 地支名, 地支索引)
        hour_table = [
            (23, 1, '子', 0), (1, 3, '丑', 1), (3, 5, '寅', 2), (5, 7, '卯', 3),
            (7, 9, '辰', 4), (9, 11, '巳', 5), (11, 13, '午', 6), (13, 15, '未', 7),
            (15, 17, '申', 8), (17, 19, '酉', 9), (19, 21, '戌', 10), (21, 23, '亥', 11)
        ]

        # 确定时支
        zhi_index = 0  # 默认子时
        zhi_name = '子'

        for start, end, name, index in hour_table:
            if start < end:  # 正常时段
                if start <= time_decimal < end:
                    zhi_index = index
                    zhi_name = name
                    break
            else:  # 跨日时段（子时：23:00-1:00）
                if time_decimal >= start or time_decimal < end:
                    zhi_index = index
                    zhi_name = name
                    break

        # 五鼠遁表（日上起时）- 按建议文档修正
        wushu_dun_table = {
            '甲': 0, '乙': 2, '丙': 4, '丁': 6, '戊': 8,  # 甲己还加甲
            '己': 0, '庚': 2, '辛': 4, '壬': 6, '癸': 8   # 乙庚丙作初...
        }

        # 时干计算：日干对应的起始天干 + 时支索引
        base_gan_index = wushu_dun_table[day_gan]
        hour_gan_index = (base_gan_index + zhi_index) % 10

        print(f"🕐 时柱计算: {hour}:{minute:02d}({time_decimal:.2f}) -> {zhi_name}时 -> {self.tiangan[hour_gan_index]}{self.dizhi[zhi_index]}")

        return (self.tiangan[hour_gan_index], self.dizhi[zhi_index])

    def _get_solar_term_datetime(self, year: int, term_name: str) -> datetime:
        """获取精确的节气时间（临时简化实现，后续需要天文算法）"""
        # 节气基础日期表（需要后续替换为天文级精确计算）
        solar_terms_base = {
            '立春': (2, 4, 10, 42),   # 月、日、时、分（大致时间）
            '雨水': (2, 19, 0, 43),
            '惊蛰': (3, 6, 5, 10),
            '春分': (3, 21, 5, 58),
            '清明': (4, 5, 9, 51),
            '谷雨': (4, 20, 16, 55),
            '立夏': (5, 6, 3, 20),
            '小满': (5, 21, 15, 59),
            '芒种': (6, 6, 7, 6),
            '夏至': (6, 21, 23, 14),
            '小暑': (7, 7, 16, 50),
            '大暑': (7, 23, 10, 50),
            '立秋': (8, 8, 3, 13),
            '处暑': (8, 23, 18, 2),
            '白露': (9, 8, 6, 0),
            '秋分': (9, 23, 15, 4),
            '寒露': (10, 8, 22, 6),
            '霜降': (10, 23, 19, 37),
            '立冬': (11, 8, 1, 24),
            '小雪': (11, 22, 22, 58),
            '大雪': (12, 7, 17, 18),
            '冬至': (12, 22, 5, 48),
            '小寒': (1, 6, 5, 30),
            '大寒': (1, 20, 22, 8)
        }

        if term_name not in solar_terms_base:
            # 默认返回立春时间
            month, day, hour, minute = solar_terms_base['立春']
        else:
            month, day, hour, minute = solar_terms_base[term_name]

        # 处理跨年的节气（小寒、大寒在下一年1月）
        if month == 1 and term_name in ['小寒', '大寒']:
            return datetime(year + 1, month, day, hour, minute)
        else:
            return datetime(year, month, day, hour, minute)

    def _perform_analysis(self, four_pillars: List[Tuple[str, str]],
                         birth_info: BirthInfo,
                         analysis_mode: AnalysisMode) -> Dict:
        """执行分析"""
        birth_datetime = datetime(
            birth_info.year, birth_info.month, birth_info.day,
            birth_info.hour, birth_info.minute
        )
        
        if analysis_mode == AnalysisMode.BASIC:
            return self._basic_analysis(four_pillars, birth_info)
        elif analysis_mode == AnalysisMode.PROFESSIONAL:
            return self._professional_analysis(four_pillars, birth_datetime, birth_info.gender)
        elif analysis_mode == AnalysisMode.CLASSICAL:
            return self._classical_analysis(four_pillars, birth_datetime)
        elif analysis_mode == AnalysisMode.COMPREHENSIVE:
            return self._comprehensive_analysis(four_pillars, birth_datetime, birth_info.gender)
        else:
            return self._basic_analysis(four_pillars, birth_info)
    
    def _basic_analysis(self, four_pillars: List[Tuple[str, str]], birth_info: BirthInfo) -> Dict:
        """基础分析 - 已迁移至前端统一基本信息计算器"""
        return {
            "系统提示": "基本信息计算已迁移至前端统一基本信息计算器",
            "前端计算器": "UnifiedBasicInfoCalculator",
            "玉匣记专注": "专业分析、古籍理论、格局判断",
            "五行分析": self._analyze_wuxing(four_pillars),
            "十神分析": self._analyze_shishen(four_pillars),
            "基本评价": "请使用前端统一基本信息计算器获取基本信息，玉匣记专注于专业分析"
        }
    
    def _professional_analysis(self, four_pillars: List[Tuple[str, str]], 
                              birth_datetime: datetime, gender: str) -> Dict:
        """专业分析"""
        if self.professional_system:
            return self.professional_system.analyze_professional_detail(
                four_pillars, birth_datetime, gender
            )
        else:
            return {"错误": "专业分析系统未加载"}
    
    def _classical_analysis(self, four_pillars: List[Tuple[str, str]], 
                           birth_datetime: datetime) -> Dict:
        """古籍理论分析"""
        if self.classical_manager:
            context = {
                "日干": four_pillars[2][0],
                "月令": four_pillars[1][1],
                "季节": self._get_season(birth_datetime.month),
                "四柱": four_pillars
            }
            
            # 获取各类理论分析
            tiaohou_analysis = self.classical_manager.get_integrated_analysis("调候理论", context)
            geju_analysis = self.classical_manager.get_integrated_analysis("格局理论", context)
            
            return {
                "调候分析": tiaohou_analysis,
                "格局分析": geju_analysis,
                "古籍综合": "基于六部古籍的综合分析"
            }
        else:
            return {"错误": "古籍理论管理器未加载"}
    
    def _comprehensive_analysis(self, four_pillars: List[Tuple[str, str]], 
                               birth_datetime: datetime, gender: str) -> Dict:
        """综合分析"""
        result = {}
        
        # 基础分析
        result["基础分析"] = self._basic_analysis(four_pillars, 
            BirthInfo(birth_datetime.year, birth_datetime.month, birth_datetime.day,
                     birth_datetime.hour, birth_datetime.minute, gender))
        
        # 专业分析
        if self.professional_system:
            result["专业分析"] = self._professional_analysis(four_pillars, birth_datetime, gender)
        
        # 古籍分析
        if self.classical_manager:
            result["古籍分析"] = self._classical_analysis(four_pillars, birth_datetime)
        
        # 综合评价
        result["综合评价"] = self._generate_comprehensive_evaluation(four_pillars, result)
        
        return result
    
    def _analyze_wuxing(self, four_pillars: List[Tuple[str, str]]) -> Dict:
        """分析五行"""
        wuxing_count = {"木": 0, "火": 0, "土": 0, "金": 0, "水": 0}
        
        for gan, zhi in four_pillars:
            wuxing_count[self.wuxing_map[gan]] += 1
            wuxing_count[self.wuxing_map[zhi]] += 1
        
        # 加上藏干
        for _, zhi in four_pillars:
            for canggan in self.canggan_map[zhi]:
                wuxing_count[self.wuxing_map[canggan]] += 0.5
        
        return {
            "五行统计": wuxing_count,
            "最旺五行": max(wuxing_count, key=wuxing_count.get),
            "最弱五行": min(wuxing_count, key=wuxing_count.get),
            "五行平衡": "平衡" if max(wuxing_count.values()) - min(wuxing_count.values()) <= 2 else "不平衡"
        }
    
    def _analyze_shishen(self, four_pillars: List[Tuple[str, str]]) -> Dict:
        """分析十神"""
        rizhu_wuxing = self.wuxing_map[four_pillars[2][0]]
        shishen_analysis = {}
        
        for i, (gan, zhi) in enumerate(four_pillars):
            pillar_name = ["年", "月", "日", "时"][i]
            
            if i != 2:  # 非日柱
                gan_wuxing = self.wuxing_map[gan]
                zhi_wuxing = self.wuxing_map[zhi]
                
                gan_shishen = self.shishen_map.get((rizhu_wuxing, gan_wuxing), "未知")
                zhi_shishen = self.shishen_map.get((rizhu_wuxing, zhi_wuxing), "未知")
                
                shishen_analysis[f"{pillar_name}柱"] = {
                    "天干": gan_shishen,
                    "地支": zhi_shishen
                }
        
        return shishen_analysis
    
    def _generate_comprehensive_evaluation(self, four_pillars: List[Tuple[str, str]], 
                                         analysis_result: Dict) -> Dict:
        """生成综合评价"""
        return {
            "命局层次": "中等",
            "发展潜力": "良好",
            "主要优势": ["性格稳重", "有责任心"],
            "注意事项": ["需要加强灵活性"],
            "人生建议": ["发挥优势，弥补不足，稳步发展"],
            "总体评价": "命局配置较好，具有良好的发展潜力"
        }
    
    def _calculate_confidence(self, four_pillars: List[Tuple[str, str]], 
                            analysis_result: Dict) -> float:
        """计算可信度"""
        base_confidence = 0.85
        
        # 根据分析结果调整可信度
        if "专业分析" in analysis_result:
            base_confidence += 0.10
        if "古籍分析" in analysis_result:
            base_confidence += 0.05
        
        return min(base_confidence, 1.0)
    
    def _format_four_pillars(self, four_pillars: List[Tuple[str, str]]) -> str:
        """格式化四柱显示"""
        return " ".join([f"{gan}{zhi}" for gan, zhi in four_pillars])
    
    def _get_season(self, month: int) -> str:
        """获取季节"""
        if month in [3, 4, 5]:
            return "春"
        elif month in [6, 7, 8]:
            return "夏"
        elif month in [9, 10, 11]:
            return "秋"
        else:
            return "冬"
    
    def export_paipan_report(self, result: PaipanResult, format: str = "text") -> str:
        """导出排盘报告"""
        if format == "text":
            return self._export_text_report(result)
        elif format == "json":
            return self._export_json_report(result)
        else:
            return "不支持的导出格式"
    
    def _export_text_report(self, result: PaipanResult) -> str:
        """导出文本报告"""
        report = []
        report.append("=" * 80)
        report.append("🏛️ 玉匣记八字排盘报告")
        report.append("=" * 80)
        report.append("")
        
        # 基本信息
        birth_info = result.birth_info
        report.append("📋 基本信息:")
        report.append(f"   性别: {birth_info.gender}")
        report.append(f"   出生: {birth_info.year}年{birth_info.month}月{birth_info.day}日 {birth_info.hour}:{birth_info.minute:02d}")
        report.append(f"   四柱: {self._format_four_pillars(result.four_pillars)}")
        report.append(f"   模式: {result.analysis_mode.value}")
        report.append(f"   可信度: {result.confidence:.1%}")
        report.append("")
        
        # 分析结果
        report.append("📊 分析结果:")
        self._append_analysis_to_report(report, result.analysis_result, indent=1)
        
        report.append("")
        report.append("=" * 80)
        report.append(f"报告生成时间: {result.generation_time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("🏛️ 玉匣记八字排盘系统 - 传承古典智慧，融合现代技术")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def _append_analysis_to_report(self, report: List[str], analysis: Any, indent: int = 0):
        """将分析结果添加到报告中"""
        prefix = "   " * indent
        
        if isinstance(analysis, dict):
            for key, value in analysis.items():
                if isinstance(value, (dict, list)):
                    report.append(f"{prefix}{key}:")
                    self._append_analysis_to_report(report, value, indent + 1)
                else:
                    report.append(f"{prefix}{key}: {value}")
        elif isinstance(analysis, list):
            for i, item in enumerate(analysis):
                if isinstance(item, (dict, list)):
                    report.append(f"{prefix}[{i+1}]:")
                    self._append_analysis_to_report(report, item, indent + 1)
                else:
                    report.append(f"{prefix}[{i+1}] {item}")
        else:
            report.append(f"{prefix}{analysis}")
    
    def _export_json_report(self, result: PaipanResult) -> str:
        """导出JSON报告"""
        report_data = {
            "birth_info": {
                "year": result.birth_info.year,
                "month": result.birth_info.month,
                "day": result.birth_info.day,
                "hour": result.birth_info.hour,
                "minute": result.birth_info.minute,
                "gender": result.birth_info.gender,
                "location": result.birth_info.location
            },
            "four_pillars": result.four_pillars,
            "analysis_mode": result.analysis_mode.value,
            "analysis_result": result.analysis_result,
            "confidence": result.confidence,
            "generation_time": result.generation_time.isoformat()
        }
        
        return json.dumps(report_data, ensure_ascii=False, indent=2)


# 测试和使用示例
def main():
    """测试玉匣记八字排盘系统"""
    print("🏛️ 玉匣记八字排盘系统测试")
    print("=" * 80)
    
    # 创建系统
    system = YuxiangjiPaipanSystem()
    
    # 创建出生信息
    birth_info = BirthInfo(
        year=1990,
        month=7,
        day=21,
        hour=18,
        minute=2,
        gender="男",
        location="北京"
    )
    
    # 创建排盘
    result = system.create_paipan(birth_info, AnalysisMode.COMPREHENSIVE)
    
    # 导出报告
    report = system.export_paipan_report(result, "text")
    print(report)
    
    print(f"\n✅ 玉匣记八字排盘系统测试完成")


def test_algorithm_accuracy():
    """测试算法准确性 - 使用建议文档中的验证用例"""
    print("🧪 开始算法准确性验证测试")
    print("=" * 60)

    system = YuxiangjiPaipanSystem()

    # 建议文档中的测试用例
    test_cases = [
        {
            "name": "2006-07-23 22:14 北京",
            "birth_info": BirthInfo(2006, 7, 23, 22, 14, "男", "北京", 116.4074),
            "expected": "丙戌 乙未 癸丑 癸亥",
            "note": "常见错误：误用北京时间"
        },
        {
            "name": "2019-06-10 15:36 北京",
            "birth_info": BirthInfo(2019, 6, 10, 15, 36, "女", "北京", 116.4074),
            "expected": "己亥 庚午 丙戌 丙申",
            "note": "常见错误：忽略真太阳时"
        },
        {
            "name": "1582-10-04 12:00",
            "birth_info": BirthInfo(1582, 10, 4, 12, 0, "男", "北京", 116.4074),
            "expected": "壬午 己酉 庚子 壬午",
            "note": "历法转换测试"
        },
        {
            "name": "2024-02-04 09:15 (立春前)",
            "birth_info": BirthInfo(2024, 2, 4, 9, 15, "男", "北京", 116.4074),
            "expected": "甲辰 丙寅 戊戌 丁巳",
            "note": "2024年立春时刻：2月4日16:26:53，此时未过立春"
        }
    ]

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print(f"📝 说明: {test_case['note']}")
        print(f"🎯 期望结果: {test_case['expected']}")

        try:
            # 计算四柱
            four_pillars = system._calculate_four_pillars(test_case['birth_info'])
            actual_result = system._format_four_pillars(four_pillars)

            print(f"🔮 实际结果: {actual_result}")

            # 验证结果
            if actual_result == test_case['expected']:
                print("✅ 测试通过")
            else:
                print("❌ 测试失败")
                print(f"   期望: {test_case['expected']}")
                print(f"   实际: {actual_result}")

        except Exception as e:
            print(f"⚠️ 测试异常: {e}")

        print("-" * 50)

    print("\n🧪 算法准确性验证测试完成")


if __name__ == "__main__":
    # 运行基本测试
    main()

    # 运行准确性验证测试
    print("\n" + "=" * 80)
    test_algorithm_accuracy()
