 申柱藏干强度: (3) ["旺", "中", "弱"]
index.js? [sm]:1223 ✅ 八字数据加载完成
index.js? [sm]:1224 ✅ 统一数据结构: {userInfo: {…}, baziInfo: {…}, fiveElements: {…}, mingliDetails: {…}, birthInfo: {…}, …}
index.js? [sm]:1225 ✅ 用户信息: {name: "一套", gender: "男", birthDate: "2018年6月4日", birthTime: "下午3:19", location: "北京", …}
index.js? [sm]:1226 ✅ 八字信息: {yearPillar: {…}, monthPillar: {…}, dayPillar: {…}, timePillar: {…}}
index.js? [sm]:1227 ✅ 五行信息: {wood: 0, fire: 0, earth: 0, metal: 0, water: 0}
index.js? [sm]:1228 ✅ 命理详情: {birth_solar_term: "出生于小满后14天4小时，芒种前1天10小时", kong_wang: "戌亥", ming_gua: "离卦 (东四命)", constellation: "双子座", star_mansion: "壁水貐", …}
index.js? [sm]:1340 🔍 验证数据完整性...
index.js? [sm]:1365 ✅ 数据完整性验证通过
index.js? [sm]:4602 🌟 开始计算天体位置: {name: "一套", gender: "男", birthDate: "2018年6月4日", birthTime: "下午3:19", location: "北京", …}
index.js? [sm]:4659 🔧 数据源检查: {baziData: true, userInfo: true, birthInfo: true}
index.js? [sm]:4660 🔧 提取的时间: {year: 2018, month: 6, day: 4, hour: 15, minute: 19}
index.js? [sm]:4662 🔢 天体计算参数: {year: 2018, month: 6, day: 4, hour: 15, minute: 19, …}
index.js? [sm]:4665 ✅ 天体位置计算完成: {input: {…}, julianDay: 2458274.1381944446, positions: {…}, houses: Array(12), aspects: Array(5), …}
index.js? [sm]:4686 🌟 天体图数据已更新，使用真实计算结果
index.js? [sm]:4687 🔧 天体数据预览: (7) [{…}, {…}, {…}, {…}, {…}, {…}, {…}]
index.js? [sm]:4820 🔮 开始加载专业应期分析...
index.js? [sm]:4878 🎯 开始统一前端应期分析计算...
index.js? [sm]:7147 🔧 构建兼容性八字数据结构完成
index.js? [sm]:7148 📊 日主: 丁
index.js? [sm]:7149 📊 性别: 男
index.js? [sm]:7027 🌏 构建文化语境信息...
unified_architecture_manager.js? [sm]:37 🔒 计算锁定成功，来源: frontend_timing_analysis
unified_architecture_manager.js? [sm]:118 🚀 开始统一计算，来源: frontend_timing_analysis
index.js? [sm]:4943 🔧 开始统一前端应期分析...
index.js? [sm]:4975 🔍 分析marriage事件...
index.js? [sm]:5091 ⚡ 计算marriage能量阈值...
index.js? [sm]:5125 🔍 提取真实五行能量数据...
index.js? [sm]:5133 ✅ 从页面数据获取五行: {wood: 80, fire: 255, earth: 456, metal: 48, water: 18}
index.js? [sm]:5176 🎯 最终五行能量: {金: 48, 木: 80, 水: 18, 火: 255, 土: 456}
index.js? [sm]:5263 🔧 marriage统一计算: 加权158.3 → 标准化158.3% → 平衡调整-10.0% → 最终100.0%
index.js? [sm]:5310 🔍 病药平衡分析: marriage, 性别: 男
index.js? [sm]:5125 🔍 提取真实五行能量数据...
index.js? [sm]:5133 ✅ 从页面数据获取五行: {wood: 80, fire: 255, earth: 456, metal: 48, water: 18}
index.js? [sm]:5176 🎯 最终五行能量: {金: 48, 木: 80, 水: 18, 火: 255, 土: 456}
index.js? [sm]:5572 🔍 三重引动机制分析: marriage, 当前年份: 2025
index.js? [sm]:6041 🔍 动态分析引擎: marriage, 当前年份: 2025
index.js? [sm]:4975 🔍 分析promotion事件...
index.js? [sm]:5091 ⚡ 计算promotion能量阈值...
index.js? [sm]:5125 🔍 提取真实五行能量数据...
index.js? [sm]:5133 ✅ 从页面数据获取五行: {wood: 80, fire: 255, earth: 456, metal: 48, water: 18}
index.js? [sm]:5176 🎯 最终五行能量: {金: 48, 木: 80, 水: 18, 火: 255, 土: 456}
index.js? [sm]:5263 🔧 promotion统一计算: 加权73.0 → 标准化73.0% → 平衡调整-10.0% → 最终63.0%
index.js? [sm]:5310 🔍 病药平衡分析: promotion, 性别: 男
index.js? [sm]:5125 🔍 提取真实五行能量数据...
index.js? [sm]:5133 ✅ 从页面数据获取五行: {wood: 80, fire: 255, earth: 456, metal: 48, water: 18}
index.js? [sm]:5176 🎯 最终五行能量: {金: 48, 木: 80, 水: 18, 火: 255, 土: 456}
index.js? [sm]:5572 🔍 三重引动机制分析: promotion, 当前年份: 2025
index.js? [sm]:6041 🔍 动态分析引擎: promotion, 当前年份: 2025
index.js? [sm]:4975 🔍 分析childbirth事件...
index.js? [sm]:5091 ⚡ 计算childbirth能量阈值...
index.js? [sm]:5125 🔍 提取真实五行能量数据...
index.js? [sm]:5133 ✅ 从页面数据获取五行: {wood: 80, fire: 255, earth: 456, metal: 48, water: 18}
index.js? [sm]:5176 🎯 最终五行能量: {金: 48, 木: 80, 水: 18, 火: 255, 土: 456}
index.js? [sm]:5263 🔧 childbirth统一计算: 加权98.6 → 标准化98.6% → 平衡调整-10.0% → 最终88.6%
index.js? [sm]:5310 🔍 病药平衡分析: childbirth, 性别: 男
index.js? [sm]:5125 🔍 提取真实五行能量数据...
index.js? [sm]:5133 ✅ 从页面数据获取五行: {wood: 80, fire: 255, earth: 456, metal: 48, water: 18}
index.js? [sm]:5176 🎯 最终五行能量: {金: 48, 木: 80, 水: 18, 火: 255, 土: 456}
index.js? [sm]:5572 🔍 三重引动机制分析: childbirth, 当前年份: 2025
index.js? [sm]:6041 🔍 动态分析引擎: childbirth, 当前年份: 2025
index.js? [sm]:4975 🔍 分析wealth事件...
index.js? [sm]:5091 ⚡ 计算wealth能量阈值...
index.js? [sm]:5125 🔍 提取真实五行能量数据...
index.js? [sm]:5133 ✅ 从页面数据获取五行: {wood: 80, fire: 255, earth: 456, metal: 48, water: 18}
index.js? [sm]:5176 🎯 最终五行能量: {金: 48, 木: 80, 水: 18, 火: 255, 土: 456}
index.js? [sm]:5263 🔧 wealth统一计算: 加权192.8 → 标准化192.8% → 平衡调整-10.0% → 最终100.0%
index.js? [sm]:5310 🔍 病药平衡分析: wealth, 性别: 男
index.js? [sm]:5125 🔍 提取真实五行能量数据...
index.js? [sm]:5133 ✅ 从页面数据获取五行: {wood: 80, fire: 255, earth: 456, metal: 48, water: 18}
index.js? [sm]:5176 🎯 最终五行能量: {金: 48, 木: 80, 水: 18, 火: 255, 土: 456}
index.js? [sm]:5572 🔍 三重引动机制分析: wealth, 当前年份: 2025
index.js? [sm]:6041 🔍 动态分析引擎: wealth, 当前年份: 2025
index.js? [sm]:10909 🌍 用户出生地: 北京
index.js? [sm]:10910 🔍 数据源检查: {birthInfo_location: undefined, birthInfo_location2: undefined, userInfo_location: undefined, baziData_location: undefined}
index.js? [sm]:10924 ✅ 识别地域: 华北地区 (基于关键词: 北京)
index.js? [sm]:9725 🔍 应期分析数据结构调试:
index.js? [sm]:9726   - professionalResults类型: object
index.js? [sm]:9727   - professionalResults键: (4) ["marriage", "promotion", "childbirth", "wealth"]
index.js? [sm]:9731   - marriage数据结构: {type: "object", keys: Array(8), hasEnergyDeficit: false, hasEnergyAnalysis: true, hasRawAnalysis: false}
index.js? [sm]:9731   - promotion数据结构: {type: "object", keys: Array(8), hasEnergyDeficit: false, hasEnergyAnalysis: true, hasRawAnalysis: false}
index.js? [sm]:9731   - childbirth数据结构: {type: "object", keys: Array(8), hasEnergyDeficit: false, hasEnergyAnalysis: true, hasRawAnalysis: false}
index.js? [sm]:9731   - wealth数据结构: {type: "object", keys: Array(8), hasEnergyDeficit: false, hasEnergyAnalysis: true, hasRawAnalysis: false}
index.js? [sm]:9793 ✅ marriage energy_analysis数据: 用户100% / 所需45% = 达标
index.js? [sm]:9839 🔧 marriage最终数据: 用户100% / 阈值45% = 达标
index.js? [sm]:9793 ✅ promotion energy_analysis数据: 用户63.05% / 所需55.00000000000001% = 达标
index.js? [sm]:9839 🔧 promotion最终数据: 用户63.1% / 阈值55% = 达标
index.js? [sm]:9793 ✅ childbirth energy_analysis数据: 用户88.64999999999999% / 所需40% = 达标
index.js? [sm]:9839 🔧 childbirth最终数据: 用户88.6% / 阈值40% = 达标
index.js? [sm]:9793 ✅ wealth energy_analysis数据: 用户100% / 所需50% = 达标
index.js? [sm]:9839 🔧 wealth最终数据: 用户100% / 阈值50% = 达标
index.js? [sm]:9860 🔍 三重引动数据结构调试:
index.js? [sm]:9861   - professionalResults: {marriage: {…}, promotion: {…}, childbirth: {…}, wealth: {…}}
index.js? [sm]:9895 🔄 提取三重引动数据...
index.js? [sm]:9896 📥 输入数据结构: {
  "marriage": {
    "threshold_status": "met",
    "best_year": "2026年9月",
    "best_year_numeric": "2026.09",
    "timing_basis": "undefined，[object Object]",
    "energy_analysis": {
      "actual": 100,
      "percentage": "100.0%",
      "required": 45,
      "met": true,
      "completion_ratio": "100.0% / 45.0%"
    },
    "activation_analysis": {
      "star_activation": {
        "triggered": false,
        "description": "财星、官星星未透，星动不足",
        "strength": 0.25,
        "ancient_basis": "《三命通会》：十神透干，其力倍增"
      },
      "palace_activation": {
        "triggered": false,
        "description": "巳卯无明显地支作用关系",
        "combination_type": "无明显作用",
        "strength": 0.1
      },
      "god_activation": {
        "triggered": true,
        "active_gods": [
          "咸池"
        ],
        "description": "咸池入命，神煞助力",
        "strength": 0.3
      },
      "priority_assessment": {
        "priority_level": "引动力量微弱",
        "priority_score": 0.06,
        "dominant_mechanism": "星动为主"
      },
      "overall_activation": {
        "activation_strength": 0.081
      },
      "calculation_method": "《渊海子平·动静论》三重引动机制"
    },
    "dynamic_analysis": {
      "three_point_analysis": {
        "ancient_basis": "《滴天髓》：三点一线，应期立现"
      },
      "spacetime_force": {
        "formula_used": "初始值 × e^(-0.1×运程年数)",
        "ancient_basis": "《渊海子平》：大运流年，时空作用"
      },
      "turning_points": {},
      "energy_connection": {
        "is_connected": false,
        "connection_type": "微弱能量联系",
        "connection_strength": 0.44999999999999996,
        "pathway_description": "病神(0.50) → 药神(0.50) → 引动(0.50)"
      },
      "overall_dynamic_score": 0.47500000000000003,
      "calculation_method": "《滴天髓·应期章》动态分析引擎"
    }
  },
  "promotion": {
    "threshold_status": "met",
    "best_year": "2027年3月",
    "best_year_numeric": "2027.03",
    "timing_basis": "undefined，[object Object]",
    "energy_analysis": {
      "actual": 63.05,
      "percentage": "63.0%",
      "required": 55.00000000000001,
      "met": true,
      "completion_ratio": "63.0% / 55.0%"
    },
    "activation_analysis": {
      "star_activation": {
        "triggered": false,
        "description": "官星、印星星未透，星动不足",
        "strength": 0.25,
        "ancient_basis": "《三命通会》：十神透干，其力倍增"
      },
      "palace_activation": {
        "triggered": false,
        "description": "巳卯无明显地支作用关系",
        "combination_type": "无明显作用",
        "strength": 0.1
      },
      "god_activation": {
        "triggered": true,
        "active_gods": [
          "金匮",
          "文昌"
        ],
        "description": "金匮、文昌入命，神煞助力",
        "strength": 0.6
      },
      "priority_assessment": {
        "priority_level": "引动力量微弱",
        "priority_score": 0.12,
        "dominant_mechanism": "星动为主"
      },
      "overall_activation": {
        "activation_strength": 0.162
      },
      "calculation_method": "《渊海子平·动静论》三重引动机制"
    },
    "dynamic_analysis": {
      "three_point_analysis": {
        "ancient_basis": "《滴天髓》：三点一线，应期立现"
      },
      "spacetime_force": {
        "formula_used": "初始值 × e^(-0.1×运程年数)",
        "ancient_basis": "《渊海子平》：大运流年，时空作用"
      },
      "turning_points": {},
      "energy_connection": {
        "is_connected": false,
        "connection_type": "微弱能量联系",
        "connection_strength": 0.44999999999999996,
        "pathway_description": "病神(0.50) → 药神(0.50) → 引动(0.50)"
      },
      "overall_dynamic_score": 0.47500000000000003,
      "calculation_method": "《滴天髓·应期章》动态分析引擎"
    }
  },
  "childbirth": {
    "threshold_status": "met",
    "best_year": "2026年5月",
    "best_year_numeric": "2026.05",
    "timing_basis": "undefined，[object Object]",
    "energy_analysis": {
      "actual": 88.64999999999999,
      "percentage": "88.6%",
      "required": 40,
      "met": true,
      "completion_ratio": "88.6% / 40.0%"
    },
    "activation_analysis": {
      "star_activation": {
        "triggered": false,
        "description": "食神、伤官星未透，星动不足",
        "strength": 0.25,
        "ancient_basis": "《三命通会》：十神透干，其力倍增"
      },
      "palace_activation": {
        "triggered": false,
        "description": "巳卯无明显地支作用关系",
        "combination_type": "无明显作用",
        "strength": 0.1
      },
      "god_activation": {
        "triggered": false,
        "active_gods": [],
        "description": "相关神煞未激活",
        "strength": 0
      },
      "priority_assessment": {
        "priority_level": "引动力量微弱",
        "priority_score": 0,
        "dominant_mechanism": "星动为主"
      },
      "overall_activation": {
        "activation_strength": 0
      },
      "calculation_method": "《渊海子平·动静论》三重引动机制"
    },
    "dynamic_analysis": {
      "three_point_analysis": {
        "ancient_basis": "《滴天髓》：三点一线，应期立现"
      },
      "spacetime_force": {
        "formula_used": "初始值 × e^(-0.1×运程年数)",
        "ancient_basis": "《渊海子平》：大运流年，时空作用"
      },
      "turning_points": {},
      "energy_connection": {
        "is_connected": false,
        "connection_type": "微弱能量联系",
        "connection_strength": 0.44999999999999996,
        "pathway_description": "病神(0.50) → 药神(0.50) → 引动(0.50)"
      },
      "overall_dynamic_score": 0.47500000000000003,
      "calculation_method": "《滴天髓·应期章》动态分析引擎"
    }
  },
  "wealth": {
    "threshold_status": "met",
    "best_year": "2026年11月",
    "best_year_numeric": "2026.11",
    "timing_basis": "undefined，[object Object]",
    "energy_analysis": {
      "actual": 100,
      "percentage": "100.0%",
      "required": 50,
      "met": true,
      "completion_ratio": "100.0% / 50.0%"
    },
    "activation_analysis": {
      "star_activation": {
        "triggered": false,
        "description": "财星、禄神星未透，星动不足",
        "strength": 0.25,
        "ancient_basis": "《三命通会》：十神透干，其力倍增"
      },
      "palace_activation": {
        "triggered": false,
        "description": "巳卯无明显地支作用关系",
        "combination_type": "无明显作用",
        "strength": 0.1
      },
      "god_activation": {
        "triggered": true,
        "active_gods": [
          "禄神",
          "天财",
          "金匮"
        ],
        "description": "禄神、天财、金匮入命，神煞助力",
        "strength": 0.8999999999999999
      },
      "priority_assessment": {
        "priority_level": "引动力量微弱",
        "priority_score": 0.18,
        "dominant_mechanism": "星动为主"
      },
      "overall_activation": {
        "activation_strength": 0.24299999999999997
      },
      "calculation_method": "《渊海子平·动静论》三重引动机制"
    },
    "dynamic_analysis": {
      "three_point_analysis": {
        "ancient_basis": "《滴天髓》：三点一线，应期立现"
      },
      "spacetime_force": {
        "formula_used": "初始值 × e^(-0.1×运程年数)",
        "ancient_basis": "《渊海子平》：大运流年，时空作用"
      },
      "turning_points": {},
      "energy_connection": {
        "is_connected": false,
        "connection_type": "微弱能量联系",
        "connection_strength": 0.44999999999999996,
        "pathway_description": "病神(0.50) → 药神(0.50) → 引动(0.50)"
      },
      "overall_dynamic_score": 0.47500000000000003,
      "calculation_method": "《滴天髓·应期章》动态分析引擎"
    }
  }
}
index.js? [sm]:9904 🔍 处理事件 marriage: {threshold_status: "met", best_year: "2026年9月", best_year_numeric: "2026.09", confidence: undefined, timing_basis: "undefined，[object Object]", …}
index.js? [sm]:9926 ✅ marriage 置信度: 75%
index.js? [sm]:9904 🔍 处理事件 promotion: {threshold_status: "met", best_year: "2027年3月", best_year_numeric: "2027.03", confidence: undefined, timing_basis: "undefined，[object Object]", …}
index.js? [sm]:9926 ✅ promotion 置信度: 68%
index.js? [sm]:9904 🔍 处理事件 childbirth: {threshold_status: "met", best_year: "2026年5月", best_year_numeric: "2026.05", confidence: undefined, timing_basis: "undefined，[object Object]", …}
index.js? [sm]:9926 ✅ childbirth 置信度: 65%
index.js? [sm]:9904 🔍 处理事件 wealth: {threshold_status: "met", best_year: "2026年11月", best_year_numeric: "2026.11", confidence: undefined, timing_basis: "undefined，[object Object]", …}
index.js? [sm]:9926 ✅ wealth 置信度: 72%
index.js? [sm]:9948 🎯 基于最高置信度事件 marriage (75%) 设置描述
index.js? [sm]:9955 ✅ 三重引动描述已设置: {star_activation: "红鸾天喜入命，主婚姻喜事 (强度: 60%)", palace_activation: "夫妻宫得力，配偶缘分深厚 (强度: 68%)", shensha_activation: "天乙贵人护佑，婚姻和谐美满 (强度: 53%)"}
index.js? [sm]:9967 🎯 最终三重引动数据: {star_activation: "红鸾天喜入命，主婚姻喜事 (强度: 60%)", palace_activation: "夫妻宫得力，配偶缘分深厚 (强度: 68%)", shensha_activation: "天乙贵人护佑，婚姻和谐美满 (强度: 53%)", marriage_confidence: 75, promotion_confidence: 68, …}
index.js? [sm]:10221 🔍 开始提取动态分析数据...
index.js? [sm]:10230 🔍 发现marriage的动态分析数据: {three_point_analysis: {…}, spacetime_force: {…}, turning_points: {…}, energy_connection: {…}, overall_dynamic_score: 0.47500000000000003, …}
index.js? [sm]:10230 🔍 发现promotion的动态分析数据: {three_point_analysis: {…}, spacetime_force: {…}, turning_points: {…}, energy_connection: {…}, overall_dynamic_score: 0.47500000000000003, …}
index.js? [sm]:10230 🔍 发现childbirth的动态分析数据: {three_point_analysis: {…}, spacetime_force: {…}, turning_points: {…}, energy_connection: {…}, overall_dynamic_score: 0.47500000000000003, …}
index.js? [sm]:10230 🔍 发现wealth的动态分析数据: {three_point_analysis: {…}, spacetime_force: {…}, turning_points: {…}, energy_connection: {…}, overall_dynamic_score: 0.47500000000000003, …}
index.js? [sm]:10241 ✅ 使用marriage的动态分析数据
index.js? [sm]:10311 🔍 开始重新计算转折点识别...
index.js? [sm]:10337 🔍 使用八字数据重新计算转折点: {day: {…}, year: {…}, month: {…}, hour: {…}}
index.js? [sm]:10386 ⚠️ 未识别到转折点
index.js? [sm]:10393 🎯 格式化后的动态分析数据: {three_point_rule: "《滴天髓》：三点一线，应期立现", spacetime_force: "计算公式: 初始值 × e^(-0.1×运程年数) | 《渊海子平》：大运流年，时空作用 (强度: 47.5%)", turning_points: "未来5年内暂无明显转折点，建议关注流年变化"}
index.js? [sm]:10142 🔍 动态病药平衡计算...
index.js? [sm]:10164 🎯 病药平衡计算: 基础50 - 病神15.0 + 药神20.0 + 配合0.0 = 55分
index.js? [sm]:10412 🔍 生成算法验证数据...
unified_architecture_manager.js? [sm]:136 ✅ 统一计算完成，耗时: 94ms
unified_architecture_manager.js? [sm]:50 🔓 计算锁释放，来源: frontend_timing_analysis
index.js? [sm]:4912 ✅ 统一前端计算成功，架构模式: unified_frontend
index.js? [sm]:4855 ✅ 专业应期分析加载完成: {analysis_mode: "unified_frontend", status: "completed", event_analyses: {…}, comprehensive_report: {…}, cultural_adaptation: {…}, …}
index.js? [sm]:4856 📊 兼容性应期分析: {currentYear: 2025, currentYearGanZhi: {…}, currentYearDesc: "乙巳年，运势平稳，宜谨慎行事", overallLuck: "平稳发展", yearSummary: "本年度运势运势平稳，宜稳健发展，积累实力。", …}
index.js? [sm]:7586 👨‍👩‍👧‍👦 开始加载六亲分析...
index.js? [sm]:7613 ✅ 六亲分析加载完成: {spouse: {…}, children: {…}, siblings: {…}, parents: {…}, summary: {…}}
index.js? [sm]:1243 🔮 开始计算小运数据...
index.js? [sm]:8426 🔮 开始计算小运数据...
minor_fortune_calculator.js? [sm]:22 🎯 小运计算系统初始化完成 - 基于《三命通会·卷八》
index.js? [sm]:8459 👶 当前年龄: 7岁
minor_fortune_calculator.js? [sm]:77 🔄 开始批量计算1-10岁小运序列...
minor_fortune_calculator.js? [sm]:32 🔮 开始计算1岁小运...
minor_fortune_calculator.js? [sm]:50 📅 年干戊，阳年
minor_fortune_calculator.js? [sm]:54 🧭 男性阳年 → 顺行
minor_fortune_calculator.js? [sm]:189 🎯 从时柱戊申开始，顺推1步
minor_fortune_calculator.js? [sm]:213    计算过程: 戊(4) + 0 = 戊(4)
minor_fortune_calculator.js? [sm]:214    计算过程: 申(8) + 0 = 申(8)
minor_fortune_calculator.js? [sm]:215    结果: 戊申
minor_fortune_calculator.js? [sm]:67 ✅ 1岁小运计算完成: 戊申
minor_fortune_calculator.js? [sm]:32 🔮 开始计算2岁小运...
minor_fortune_calculator.js? [sm]:50 📅 年干戊，阳年
minor_fortune_calculator.js? [sm]:54 🧭 男性阳年 → 顺行
minor_fortune_calculator.js? [sm]:189 🎯 从时柱戊申开始，顺推2步
minor_fortune_calculator.js? [sm]:213    计算过程: 戊(4) + 1 = 己(5)
minor_fortune_calculator.js? [sm]:214    计算过程: 申(8) + 1 = 酉(9)
minor_fortune_calculator.js? [sm]:215    结果: 己酉
minor_fortune_calculator.js? [sm]:67 ✅ 2岁小运计算完成: 己酉
minor_fortune_calculator.js? [sm]:32 🔮 开始计算3岁小运...
minor_fortune_calculator.js? [sm]:50 📅 年干戊，阳年
minor_fortune_calculator.js? [sm]:54 🧭 男性阳年 → 顺行
minor_fortune_calculator.js? [sm]:189 🎯 从时柱戊申开始，顺推3步
minor_fortune_calculator.js? [sm]:213    计算过程: 戊(4) + 2 = 庚(6)
minor_fortune_calculator.js? [sm]:214    计算过程: 申(8) + 2 = 戌(10)
minor_fortune_calculator.js? [sm]:215    结果: 庚戌
minor_fortune_calculator.js? [sm]:67 ✅ 3岁小运计算完成: 庚戌
minor_fortune_calculator.js? [sm]:32 🔮 开始计算4岁小运...
minor_fortune_calculator.js? [sm]:50 📅 年干戊，阳年
minor_fortune_calculator.js? [sm]:54 🧭 男性阳年 → 顺行
minor_fortune_calculator.js? [sm]:189 🎯 从时柱戊申开始，顺推4步
minor_fortune_calculator.js? [sm]:213    计算过程: 戊(4) + 3 = 辛(7)
minor_fortune_calculator.js? [sm]:214    计算过程: 申(8) + 3 = 亥(11)
minor_fortune_calculator.js? [sm]:215    结果: 辛亥
minor_fortune_calculator.js? [sm]:67 ✅ 4岁小运计算完成: 辛亥
minor_fortune_calculator.js? [sm]:32 🔮 开始计算5岁小运...
minor_fortune_calculator.js? [sm]:50 📅 年干戊，阳年
minor_fortune_calculator.js? [sm]:54 🧭 男性阳年 → 顺行
minor_fortune_calculator.js? [sm]:189 🎯 从时柱戊申开始，顺推5步
minor_fortune_calculator.js? [sm]:213    计算过程: 戊(4) + 4 = 壬(8)
minor_fortune_calculator.js? [sm]:214    计算过程: 申(8) + 4 = 子(0)
minor_fortune_calculator.js? [sm]:215    结果: 壬子
minor_fortune_calculator.js? [sm]:67 ✅ 5岁小运计算完成: 壬子
minor_fortune_calculator.js? [sm]:32 🔮 开始计算6岁小运...
minor_fortune_calculator.js? [sm]:50 📅 年干戊，阳年
minor_fortune_calculator.js? [sm]:54 🧭 男性阳年 → 顺行
minor_fortune_calculator.js? [sm]:189 🎯 从时柱戊申开始，顺推6步
minor_fortune_calculator.js? [sm]:213    计算过程: 戊(4) + 5 = 癸(9)
minor_fortune_calculator.js? [sm]:214    计算过程: 申(8) + 5 = 丑(1)
minor_fortune_calculator.js? [sm]:215    结果: 癸丑
minor_fortune_calculator.js? [sm]:67 ✅ 6岁小运计算完成: 癸丑
minor_fortune_calculator.js? [sm]:32 🔮 开始计算7岁小运...
minor_fortune_calculator.js? [sm]:50 📅 年干戊，阳年
minor_fortune_calculator.js? [sm]:54 🧭 男性阳年 → 顺行
minor_fortune_calculator.js? [sm]:189 🎯 从时柱戊申开始，顺推7步
minor_fortune_calculator.js? [sm]:213    计算过程: 戊(4) + 6 = 甲(0)
minor_fortune_calculator.js? [sm]:214    计算过程: 申(8) + 6 = 寅(2)
minor_fortune_calculator.js? [sm]:215    结果: 甲寅
minor_fortune_calculator.js? [sm]:67 ✅ 7岁小运计算完成: 甲寅
minor_fortune_calculator.js? [sm]:32 🔮 开始计算8岁小运...
minor_fortune_calculator.js? [sm]:50 📅 年干戊，阳年
minor_fortune_calculator.js? [sm]:54 🧭 男性阳年 → 顺行
minor_fortune_calculator.js? [sm]:189 🎯 从时柱戊申开始，顺推8步
minor_fortune_calculator.js? [sm]:213    计算过程: 戊(4) + 7 = 乙(1)
minor_fortune_calculator.js? [sm]:214    计算过程: 申(8) + 7 = 卯(3)
minor_fortune_calculator.js? [sm]:215    结果: 乙卯
minor_fortune_calculator.js? [sm]:67 ✅ 8岁小运计算完成: 乙卯
minor_fortune_calculator.js? [sm]:32 🔮 开始计算9岁小运...
minor_fortune_calculator.js? [sm]:50 📅 年干戊，阳年
minor_fortune_calculator.js? [sm]:54 🧭 男性阳年 → 顺行
minor_fortune_calculator.js? [sm]:189 🎯 从时柱戊申开始，顺推9步
minor_fortune_calculator.js? [sm]:213    计算过程: 戊(4) + 8 = 丙(2)
minor_fortune_calculator.js? [sm]:214    计算过程: 申(8) + 8 = 辰(4)
minor_fortune_calculator.js? [sm]:215    结果: 丙辰
minor_fortune_calculator.js? [sm]:67 ✅ 9岁小运计算完成: 丙辰
minor_fortune_calculator.js? [sm]:32 🔮 开始计算10岁小运...
minor_fortune_calculator.js? [sm]:50 📅 年干戊，阳年
minor_fortune_calculator.js? [sm]:54 🧭 男性阳年 → 顺行
minor_fortune_calculator.js? [sm]:189 🎯 从时柱戊申开始，顺推10步
minor_fortune_calculator.js? [sm]:213    计算过程: 戊(4) + 9 = 丁(3)
minor_fortune_calculator.js? [sm]:214    计算过程: 申(8) + 9 = 巳(5)
minor_fortune_calculator.js? [sm]:215    结果: 丁巳
minor_fortune_calculator.js? [sm]:67 ✅ 10岁小运计算完成: 丁巳
minor_fortune_calculator.js? [sm]:88 ✅ 小运序列计算完成，共10步
minor_fortune_calculator.js? [sm]:32 🔮 开始计算7岁小运...
minor_fortune_calculator.js? [sm]:50 📅 年干戊，阳年
minor_fortune_calculator.js? [sm]:54 🧭 男性阳年 → 顺行
minor_fortune_calculator.js? [sm]:189 🎯 从时柱戊申开始，顺推7步
minor_fortune_calculator.js? [sm]:213    计算过程: 戊(4) + 6 = 甲(0)
minor_fortune_calculator.js? [sm]:214    计算过程: 申(8) + 6 = 寅(2)
minor_fortune_calculator.js? [sm]:215    结果: 甲寅
minor_fortune_calculator.js? [sm]:67 ✅ 7岁小运计算完成: 甲寅
index.js? [sm]:8479 ✅ 小运计算完成
index.js? [sm]:1248 ✅ 小运数据计算完成: {applicable: true, currentAge: 7, currentMinorFortune: {…}, allMinorFortunes: Array(10), basis: "《三命通会·卷八》小运起法", …}
index.js? [sm]:1251 🌟 开始计算专业级大运数据...
index.js? [sm]:8910 🌟 开始计算专业级大运数据...
precise_solar_terms_engine.js? [sm]:42 🌸 精确节气计算引擎初始化完成
precise_solar_terms_engine.js? [sm]:43 📊 支持年份范围: 1900-2025年
precise_solar_terms_engine.js? [sm]:44 🎯 数据精度: 分钟级
true_solar_time_corrector.js? [sm]:72 🌅 真太阳时修正系统初始化完成
true_solar_time_corrector.js? [sm]:73 📍 基准时区: 东经120°
true_solar_time_corrector.js? [sm]:74 🗺️ 集成完整城市坐标数据库: 306个城市
true_solar_time_corrector.js? [sm]:75 🔄 向后兼容: 34个主要城市
professional_dayun_calculator.js? [sm]:37 🔮 专业级大运计算器初始化完成
professional_dayun_calculator.js? [sm]:38 📅 集成精确节气计算引擎
professional_dayun_calculator.js? [sm]:39 🌅 集成真太阳时修正系统
professional_dayun_calculator.js? [sm]:50 🔮 开始专业级大运计算...
true_solar_time_corrector.js? [sm]:124 🌅 真太阳时修正完成: 2018-06-04 15:19:00 → 2018-06-04 15:04:37
true_solar_time_corrector.js? [sm]:125 📍 经度修正: 116.4074° (西退14.4分钟)
true_solar_time_corrector.js? [sm]:163 🏙️ 城市修正: 北京 (东经116.4074°)
professional_dayun_calculator.js? [sm]:67 🌅 时间修正: 2018-06-04 15:19:00 → 2018-06-04 15:04:37
professional_dayun_calculator.js? [sm]:158 🧭 大运方向: 男命阳年生，顺行大运
professional_dayun_calculator.js? [sm]:169 📅 计算精确起运时间...
precise_solar_terms_engine.js? [sm]:103 ✅ 成功加载权威节气数据（Node.js环境）
权威节气数据_前端就绪版.js:1499 🌸 使用关键年份权威数据 - 2018年 (完整精度)
precise_solar_terms_engine.js? [sm]:76 🌸 获取2018年权威节气数据成功 (24个节气)
precise_solar_terms_engine.js? [sm]:315 🎯 大运目标节气: 芒种 (顺行)
professional_dayun_calculator.js? [sm]:211 📅 起运计算: 1.43天 → 0岁5个月22天0小时
professional_dayun_calculator.js? [sm]:212 🎯 目标节气: 芒种 (2018-06-06 01:29)
professional_dayun_calculator.js? [sm]:255 🔄 生成12步大运序列 (顺行)
professional_dayun_calculator.js? [sm]:115 ✅ 专业级大运计算完成
professional_dayun_calculator.js? [sm]:116 🎯 起运时间: 0岁5个月
professional_dayun_calculator.js? [sm]:117 🔄 大运方向: 男命阳年生，顺行大运
index.js? [sm]:8987 🔄 转换大运数据结构...
index.js? [sm]:1256 ✅ 专业级大运数据计算完成: {success: true, data: {…}, basis: "《渊海子平》《协纪辨方书》专业算法", features: Array(4), note: "基于权威天文数据的专业级大运计算系统"}
index.js? [sm]:1259 🌟 开始计算专业级流年数据...
index.js? [sm]:1262 🔧 流年计算前五行数据检查: {wood: 0, fire: 0, earth: 0, metal: 0, water: 0}
index.js? [sm]:1264 ⚠️ 检测到五行数据为0，尝试使用专业级计算结果...
index.js? [sm]:1267 ✅ 已更新五行数据: {wood: 80, fire: 255, earth: 456, metal: 48, water: 18}
index.js? [sm]:8506 🌟 开始计算专业级流年数据...
true_solar_time_corrector.js? [sm]:72 🌅 真太阳时修正系统初始化完成
true_solar_time_corrector.js? [sm]:73 📍 基准时区: 东经120°
true_solar_time_corrector.js? [sm]:74 🗺️ 集成完整城市坐标数据库: 306个城市
true_solar_time_corrector.js? [sm]:75 🔄 向后兼容: 34个主要城市
professional_liunian_calculator.js? [sm]:60 🌟 专业级流年计算系统初始化完成
professional_liunian_calculator.js? [sm]:61 🌅 集成真太阳时修正系统（306个城市支持）
professional_liunian_calculator.js? [sm]:62 🌸 集成权威节气数据表（1900-2025年，分钟级精度）
professional_liunian_calculator.js? [sm]:276 🔮 开始计算2025年起5年流年分析...
professional_liunian_calculator.js? [sm]:331 ✅ 流年分析计算完成，共5年
professional_liunian_calculator.js? [sm]:276 🔮 开始计算2025年起1年流年分析...
professional_liunian_calculator.js? [sm]:331 ✅ 流年分析计算完成，共1年
index.js? [sm]:8618 🔍 验证流年数据完整性... {isCalculationSuccess: true, hasData: true}
index.js? [sm]:8688 ✅ 流年数据验证完成 {needsRepair: false, success: true}
index.js? [sm]:8602 ✅ 专业级流年计算完成，数据已验证: {success: true, currentLiunian: {…}, liunianList: Array(5), summary: {…}, basis: "《三命通会·流年章》黄帝纪元法", …}
index.js? [sm]:1284 📊 设置流年数据到页面: {success: true, currentLiunian: {…}, liunianList: Array(5), summary: {…}, basis: "《三命通会·流年章》黄帝纪元法", …}
index.js? [sm]:8796 🔄 转换流年数据为前端显示格式...
index.js? [sm]:1294 ✅ 专业级流年数据计算完成: {success: true, currentLiunian: {…}, liunianList: Array(5), summary: {…}, basis: "《三命通会·流年章》黄帝纪元法", …}
index.js? [sm]:1295 📋 页面数据验证: {hasSummary: true, summaryContent: {…}, loadingState: false}
index.js? [sm]:1302 🔍 前端调试信息:
index.js? [sm]:1303   professionalLiunianData.success: true
index.js? [sm]:1304   professionalLiunianData.summary: {totalYears: 5, averageScore: 59, averageScore_display: 59, bestYear: {…}, worstYear: {…}}
index.js? [sm]:1305   liunianData.length: 5
index.js? [sm]:1306   loadingStates.liunian: false
[Perf][pages/bazi-result/index] Page.onLoad took 541ms
index.js? [sm]:2525 ✅ 八字分析结果页面显示
app.js? [sm]:56 正在修复路由问题: pages/bazi-result/index
app.js? [sm]:80 已尝试全局定义__route__变量: pages/bazi-result/index
index.js? [sm]:2529 ✅ 八字分析结果页面准备完成
[system] Launch Time: 7342 ms
app.js? [sm]:56 正在修复路由问题: pages/bazi-result/index
app.js? [sm]:80 已尝试全局定义__route__变量: pages/bazi-result/index
index.js? [sm]:9074 🏛️ 开始历史名人验证分析...
index.js? [sm]:9364 🎯 用户八字信息: {bazi: {…}, pattern: {…}}
celebrity_database_api.js? [sm]:318 🎯 性别匹配: 用户男，筛选出194位同性别名人
celebrity_database_api.js? [sm]:361 ✅ 相似度匹配完成: 找到2位相似名人，平均相似度47%
index.js? [sm]:9158 🔧 构建历史验证数据，输入数据: {similarCelebrities: Array(2), stats: {…}}
index.js? [sm]:9170 🔍 处理名人数据: {name: "曾国藩", dynasty: "清朝", similarity: 48}
index.js? [sm]:9170 🔍 处理名人数据: {name: "王安石", dynasty: "北宋", similarity: 46}
index.js? [sm]:9151 🔧 已同步历史验证数据到应期分析结果
index.js? [sm]:9127 ✅ 历史名人验证完成，找到 2 位相似名人
index.js? [sm]:2538 切换到标签页: timing