<!--pages/bazi-result/index.wxml-->
<!-- 八字分析结果页面 - 重构优化版本 -->
<view class="tianggong-container">

  <!-- 加载状态 -->
  <view wx:if="{{pageState.loading}}" class="tianggong-loading-state">
    <view class="loading-bg-pattern"></view>
    <view class="loading-content">
      <view class="tianggong-logo">🏮</view>
      <text class="loading-text">天公师父正在为您分析八字...</text>
      <view class="loading-progress">
        <view class="progress-fill" style="width: {{pageState.progress}}%"></view>
      </view>
      <text class="loading-tip">{{pageState.loadingTip}}</text>
    </view>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{pageState.error}}" class="tianggong-error-state">
    <view class="error-bg-pattern"></view>
    <view class="error-content">
      <view class="error-icon">⚠️</view>
      <text class="error-title">分析失败</text>
      <text class="error-message">{{pageState.errorMessage}}</text>
      <button class="retry-btn" bindtap="retryAnalysis">重新分析</button>
    </view>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="tianggong-main-content">

    <!-- 天公师父风格标签页导航 -->
    <view class="tianggong-tab-nav">
      <view class="tab-bg-pattern"></view>
      <view class="tab-items">
        <view class="tab-item {{currentTab === 'basic' ? 'active' : ''}}" bindtap="switchTab" data-tab="basic">
          <view class="tab-icon">🏠</view>
          <text class="tab-text">基本信息</text>
        </view>
        <view class="tab-item {{currentTab === 'paipan' ? 'active' : ''}}" bindtap="switchTab" data-tab="paipan">
          <view class="tab-icon">🔮</view>
          <text class="tab-text">四柱排盘</text>
        </view>
        <view class="tab-item {{currentTab === 'advanced' ? 'active' : ''}}" bindtap="switchTab" data-tab="advanced">
          <view class="tab-icon">⭐</view>
          <text class="tab-text">神煞五行</text>
        </view>
        <view class="tab-item {{currentTab === 'fortune' ? 'active' : ''}}" bindtap="switchTab" data-tab="fortune">
          <view class="tab-icon">🌟</view>
          <text class="tab-text">大运流年</text>
        </view>
        <view class="tab-item {{currentTab === 'professional' ? 'active' : ''}}" bindtap="switchTab" data-tab="professional">
          <view class="tab-icon">🎯</view>
          <text class="tab-text">格局用神</text>
        </view>
        <view class="tab-item {{currentTab === 'classical' ? 'active' : ''}}" bindtap="switchTab" data-tab="classical">
          <view class="tab-icon">📜</view>
          <text class="tab-text">古籍分析</text>
        </view>
        <view class="tab-item {{currentTab === 'timing' ? 'active' : ''}}" bindtap="switchTab" data-tab="timing">
          <view class="tab-icon">⏰</view>
          <text class="tab-text">应期分析</text>
        </view>
        <view class="tab-item {{currentTab === 'liuqin' ? 'active' : ''}}" bindtap="switchTab" data-tab="liuqin">
          <view class="tab-icon">👨‍👩‍👧‍👦</view>
          <text class="tab-text">六亲分析</text>
        </view>
      </view>
    </view>

    <!-- 标签页内容区域 -->
    <scroll-view class="tianggong-tab-content" scroll-y="true" enhanced="true" show-scrollbar="false">

      <!-- 基本信息页面 -->
      <view wx:if="{{currentTab === 'basic'}}" class="tab-panel basic-panel">

        <!-- 用户基本信息卡片 -->
        <view class="tianggong-card user-info-card">
          <view class="card-header">
            <text class="header-icon">👤</text>
            <text class="card-title">基本信息</text>
          </view>
          <view class="card-content">
            <view class="info-grid">
              <view class="info-item">
                <text class="info-label">姓名</text>
                <text class="info-value">{{baziData.name || baziData.userInfo.name || '用户'}}</text>
              </view>
              <view class="info-item">
                <text class="info-label">性别</text>
                <text class="info-value">{{baziData.gender || baziData.userInfo.gender || '未知'}}</text>
              </view>
              <view class="info-item">
                <text class="info-label">出生日期</text>
                <text class="info-value">{{baziData.birthDate || baziData.userInfo.birthDate || '未知'}}</text>
              </view>
              <view class="info-item">
                <text class="info-label">出生时间</text>
                <text class="info-value">{{baziData.birthTime || baziData.userInfo.birthTime || '未知'}}</text>
              </view>
              <view class="info-item">
                <text class="info-label">出生地点</text>
                <text class="info-value">{{baziData.location || baziData.userInfo.location || '未知'}}</text>
              </view>
              <view class="info-item">
                <text class="info-label">农历日期</text>
                <text class="info-value">{{baziData.lunar_time || baziData.userInfo.lunar_time || '未知'}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 八字概览卡片 -->
        <view class="tianggong-card bazi-overview-card">
          <view class="card-header">
            <text class="header-icon">�</text>
            <text class="card-title">八字概览</text>
          </view>
          <view class="card-content">
            <view class="overview-summary">
              <text class="summary-text">{{baziData.basic_summary || '您的八字分析正在计算中，请稍候...'}}</text>
            </view>
            <view class="overview-highlights">
              <view class="highlight-item">
                <text class="highlight-label">日主</text>
                <text class="highlight-value">{{baziData.day_gan || '计算中'}}</text>
              </view>
              <view class="highlight-item">
                <text class="highlight-label">格局</text>
                <text class="highlight-value">{{baziData.pattern_type || '分析中'}}</text>
              </view>
              <view class="highlight-item">
                <text class="highlight-label">用神</text>
                <text class="highlight-value">{{baziData.yongshen || '计算中'}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 天体图卡片 -->
        <view class="tianggong-card celestial-chart-card">
          <view class="card-header">
            <text class="header-icon">🌟</text>
            <text class="card-title">出生天体图</text>
          </view>
          <view class="card-content">
            <view class="celestial-chart-container">
              <view class="celestial-chart">
                <!-- 宫位分割线 -->
                <view class="house-lines">
                  <view class="house-line" wx:for="{{12}}" wx:key="*this" style="transform: rotate({{index * 30}}deg)"></view>
                </view>
                <!-- 黄道十二宫背景 -->
                <view class="zodiac-circle">
                  <view class="zodiac-sign" wx:for="{{['白羊','金牛','双子','巨蟹','狮子','处女','天秤','天蝎','射手','摩羯','水瓶','双鱼']}}" wx:key="*this" style="transform: rotate({{index * 30}}deg)">
                    <text class="zodiac-text">{{item}}</text>
                  </view>
                </view>
                <!-- 宫位数字 -->
                <view class="house-numbers">
                  <view class="house-number" wx:for="{{12}}" wx:key="*this" style="transform: rotate({{index * 30 + 15}}deg) translateY(-160rpx) rotate(-{{index * 30 + 15}}deg)">
                    <text class="house-text">{{index + 1}}</text>
                  </view>
                </view>
                <!-- 天体位置 -->
                <view class="planets-container">
                  <view class="planet" wx:for="{{celestialLegend}}" wx:key="planet" wx:if="{{item.planet !== 'ascendant' && item.planet !== 'midheaven'}}" style="transform: rotate({{item.angle}}deg) translateY(-120rpx) rotate(-{{item.angle}}deg)">
                    <text class="planet-symbol">{{item.symbol}}</text>
                    <text class="planet-degree">{{item.position.split(' ')[1]}}</text>
                  </view>
                </view>
                <!-- 中心点 -->
                <view class="chart-center">
                  <text class="center-symbol">⊕</text>
                  <text class="center-text">出生图</text>
                  <text class="birth-info">{{baziData.birthDate || baziData.userInfo.birthDate || birthInfo.birth_date}}</text>
                </view>
              </view>
            </view>
            <!-- 天体图例 -->
            <view class="celestial-legend">
              <view class="legend-header">
                <text class="legend-title">天体图例</text>
              </view>
              <view class="legend-items">
                <view class="legend-item" wx:for="{{celestialLegend}}" wx:key="planet">
                  <text class="legend-symbol">{{item.symbol}}</text>
                  <text class="legend-name">{{item.name}}</text>
                  <text class="legend-position">{{item.position}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

      </view>

      <!-- 四柱排盘页面 -->
      <view wx:elif="{{currentTab === 'paipan'}}" class="tab-panel paipan-panel">

        <!-- 四柱排盘主卡片 -->
        <view class="tianggong-card paipan-card">
          <view class="card-header">
            <text class="header-icon">🔮</text>
            <text class="card-title">四柱排盘</text>
          </view>
          <view class="card-content">
            <view class="paipan-table">
              <!-- 表头 -->
              <view class="paipan-header">
                <text class="header-cell">柱位</text>
                <text class="header-cell">天干</text>
                <text class="header-cell">地支</text>
                <text class="header-cell">纳音</text>
                <text class="header-cell">藏干</text>
              </view>
              <!-- 年柱 -->
              <view class="paipan-row">
                <text class="cell-label">年柱</text>
                <text class="cell-tiangan">{{baziData.year_gan || baziData.baziInfo.yearPillar.heavenly}}</text>
                <text class="cell-dizhi">{{baziData.year_zhi || baziData.baziInfo.yearPillar.earthly}}</text>
                <text class="cell-nayin">{{baziData.nayin.year_pillar || baziData.baziInfo.yearPillar.nayin}}</text>
                <text class="cell-canggan">{{baziData.year_canggan || '计算中'}}</text>
              </view>
              <!-- 月柱 -->
              <view class="paipan-row">
                <text class="cell-label">月柱</text>
                <text class="cell-tiangan">{{baziData.month_gan || baziData.baziInfo.monthPillar.heavenly}}</text>
                <text class="cell-dizhi">{{baziData.month_zhi || baziData.baziInfo.monthPillar.earthly}}</text>
                <text class="cell-nayin">{{baziData.nayin.month_pillar || baziData.baziInfo.monthPillar.nayin}}</text>
                <text class="cell-canggan">{{baziData.month_canggan || '计算中'}}</text>
              </view>
              <!-- 日柱 -->
              <view class="paipan-row">
                <text class="cell-label">日柱</text>
                <text class="cell-tiangan">{{baziData.day_gan || baziData.baziInfo.dayPillar.heavenly}}</text>
                <text class="cell-dizhi">{{baziData.day_zhi || baziData.baziInfo.dayPillar.earthly}}</text>
                <text class="cell-nayin">{{baziData.nayin.day_pillar || baziData.baziInfo.dayPillar.nayin}}</text>
                <text class="cell-canggan">{{baziData.day_canggan || '计算中'}}</text>
              </view>
              <!-- 时柱 -->
              <view class="paipan-row">
                <text class="cell-label">时柱</text>
                <text class="cell-tiangan">{{baziData.hour_gan || baziData.baziInfo.timePillar.heavenly}}</text>
                <text class="cell-dizhi">{{baziData.hour_zhi || baziData.baziInfo.timePillar.earthly}}</text>
                <text class="cell-nayin">{{baziData.nayin.hour_pillar || baziData.baziInfo.timePillar.nayin}}</text>
                <text class="cell-canggan">{{baziData.hour_canggan || '计算中'}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 十神分析卡片 -->
        <view class="tianggong-card shishen-card">
          <view class="card-header">
            <text class="header-icon">⚖️</text>
            <text class="card-title">十神分析</text>
          </view>
          <view class="card-content">
            <view class="shishen-grid">
              <view class="shishen-item" wx:for="{{baziData.shishen_analysis || baziData.tenGods || []}}" wx:key="name">
                <text class="shishen-name">{{item.name || item.god}}</text>
                <text class="shishen-count">{{item.count || item.quantity || 0}}</text>
                <text class="shishen-strength">{{item.strength || item.power || '中等'}}</text>
              </view>
            </view>
            <view class="shishen-summary">
              <text class="summary-title">十神特点</text>
              <text class="summary-content">{{baziData.shishen_summary || baziData.tenGodsAnalysis || '十神分析计算中...'}}</text>
            </view>
          </view>
        </view>

        <!-- 藏干分析卡片 -->
        <view class="tianggong-card canggan-card">
          <view class="card-header">
            <text class="header-icon">🔍</text>
            <text class="card-title">藏干分析</text>
          </view>
          <view class="card-content">
            <view class="canggan-analysis">
              <view class="canggan-item">
                <text class="canggan-label">年支藏干</text>
                <text class="canggan-value">{{baziData.year_canggan || '计算中'}}</text>
              </view>
              <view class="canggan-item">
                <text class="canggan-label">月支藏干</text>
                <text class="canggan-value">{{baziData.month_canggan || '计算中'}}</text>
              </view>
              <view class="canggan-item">
                <text class="canggan-label">日支藏干</text>
                <text class="canggan-value">{{baziData.day_canggan || '计算中'}}</text>
              </view>
              <view class="canggan-item">
                <text class="canggan-label">时支藏干</text>
                <text class="canggan-value">{{baziData.hour_canggan || '计算中'}}</text>
              </view>
            </view>
            <view class="canggan-summary">
              <text class="summary-text">{{baziData.canggan_summary || '藏干分析计算中...'}}</text>
            </view>
          </view>
        </view>

      </view>

      <!-- 神煞五行页面 -->
      <view wx:elif="{{currentTab === 'advanced'}}" class="tab-panel advanced-panel">

        <!-- ========== 五行分析模块区域 ========== -->

        <!-- 🔥 专业级五行分析卡片 -->
        <view class="tianggong-card professional-wuxing-card">
          <view class="card-header">
            <text class="header-icon">🔥</text>
            <text class="card-title">专业级五行分析</text>
          </view>
          <view class="card-content">
            <view class="wuxing-professional">
              <!-- 五行力量分布 -->
              <view class="power-distribution">
                <text class="dist-title">五行力量分布</text>
                <view class="power-bars">
                  <view class="power-item" wx:for="{{baziData.wuxing_analysis || []}}" wx:key="element">
                    <text class="element-name">{{item.element}}</text>
                    <view class="power-bar">
                      <view class="power-fill {{item.element === '木' ? 'wood' : item.element === '火' ? 'fire' : item.element === '土' ? 'earth' : item.element === '金' ? 'metal' : 'water'}}" style="width: {{item.percentage || 0}}%;"></view>
                    </view>
                    <text class="power-value">{{item.percentage || 0}}%</text>
                  </view>
                </view>
              </view>

              <!-- 平衡指数 -->
              <view class="balance-analysis">
                <text class="balance-title">平衡指数分析</text>
                <view class="balance-stats">
                  <view class="balance-item">
                    <text class="balance-label">总力量值</text>
                    <text class="balance-value">{{professionalWuxingData.totalStrength || baziData.totalStrength || '300'}}</text>
                  </view>
                  <view class="balance-item">
                    <text class="balance-label">平衡指数</text>
                    <text class="balance-value">{{baziData.balanceIndex || '75'}}</text>
                  </view>
                  <view class="balance-item">
                    <text class="balance-label">平衡状态</text>
                    <text class="balance-status">{{baziData.balanceStatus || '偏旺'}}</text>
                  </view>
                </view>
                <view class="strongest-weakest">
                  <text class="strong-label">最强: {{baziData.strongest || '火'}}</text>
                  <text class="weak-label">最弱: {{baziData.weakest || '水'}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- ⚡ 五行强弱分析卡片 -->
        <view class="tianggong-card wuxing-strength-card">
          <view class="card-header">
            <text class="header-icon">⚡</text>
            <text class="card-title">五行强弱等级</text>
          </view>
          <view class="card-content">
            <view class="strength-chart">
              <view class="strength-item" wx:for="{{baziData.wuxing_strength || []}}" wx:key="element">
                <text class="element-name">{{item.element}}</text>
                <view class="strength-bar">
                  <view class="bar-fill" style="width: {{item.percentage || 0}}%; background-color: {{item.color}};"></view>
                </view>
                <text class="strength-level">{{item.level}}</text>
              </view>
            </view>
            <view class="wuxing-balance">
              <text class="balance-title">五行平衡度</text>
              <text class="balance-score">{{baziData.balanceIndex || '50'}}</text>
              <text class="balance-desc">{{baziData.balanceStatus || '平衡'}}</text>
            </view>
            <view class="wuxing-summary">
              <text class="summary-text">{{baziData.wuxing_summary || '五行分析计算中...'}}</text>
            </view>
          </view>
        </view>

        <!-- 🔄 五行动态交互分析卡片 -->
        <view class="tianggong-card wuxing-interaction-card">
          <view class="card-header">
            <text class="header-icon">🔄</text>
            <text class="card-title">五行动态交互</text>
          </view>
          <view class="card-content">
            <view class="interaction-analysis">
              <!-- 三会局 -->
              <view class="interaction-section" wx:if="{{baziData.wuxing_interactions && baziData.wuxing_interactions.sanhui && baziData.wuxing_interactions.sanhui.length > 0}}">
                <text class="section-title">三会局</text>
                <view class="interaction-list">
                  <view class="interaction-item sanhui" wx:for="{{baziData.wuxing_interactions.sanhui}}" wx:key="type">
                    <text class="interaction-type">{{item.type}}</text>
                    <text class="interaction-desc">{{item.description}}</text>
                    <text class="interaction-effect">{{item.effect}}</text>
                  </view>
                </view>
              </view>

              <!-- 三合局 -->
              <view class="interaction-section" wx:if="{{baziData.wuxing_interactions && baziData.wuxing_interactions.sanhe && baziData.wuxing_interactions.sanhe.length > 0}}">
                <text class="section-title">三合局</text>
                <view class="interaction-list">
                  <view class="interaction-item sanhe" wx:for="{{baziData.wuxing_interactions.sanhe}}" wx:key="type">
                    <text class="interaction-type">{{item.type}}</text>
                    <text class="interaction-desc">{{item.description}}</text>
                    <text class="interaction-effect">{{item.effect}}</text>
                  </view>
                </view>
              </view>

              <!-- 六合 -->
              <view class="interaction-section" wx:if="{{baziData.wuxing_interactions && baziData.wuxing_interactions.liuhe && baziData.wuxing_interactions.liuhe.length > 0}}">
                <text class="section-title">六合</text>
                <view class="interaction-list">
                  <view class="interaction-item liuhe" wx:for="{{baziData.wuxing_interactions.liuhe}}" wx:key="type">
                    <text class="interaction-type">{{item.type}}</text>
                    <text class="interaction-desc">{{item.description}}</text>
                    <text class="interaction-effect">{{item.effect}}</text>
                  </view>
                </view>
              </view>

              <!-- 六冲 -->
              <view class="interaction-section" wx:if="{{baziData.wuxing_interactions && baziData.wuxing_interactions.liuchong && baziData.wuxing_interactions.liuchong.length > 0}}">
                <text class="section-title">六冲</text>
                <view class="interaction-list">
                  <view class="interaction-item liuchong" wx:for="{{baziData.wuxing_interactions.liuchong}}" wx:key="type">
                    <text class="interaction-type">{{item.type}}</text>
                    <text class="interaction-desc">{{item.description}}</text>
                    <text class="interaction-effect">{{item.effect}}</text>
                  </view>
                </view>
              </view>

              <!-- 无特殊交互 -->
              <view class="no-interaction" wx:if="{{!hasWuxingInteractions}}">
                <text class="no-interaction-text">命局中无明显的五行动态交互组合</text>
                <text class="no-interaction-desc">五行力量主要来自天干地支的基础配置和月令调节</text>
              </view>
            </view>
          </view>
        </view>

        <!-- ========== 神煞分析模块区域 ========== -->

        <!-- ⭐ 吉星神煞卡片 -->
        <view class="tianggong-card auspicious-stars-card">
          <view class="card-header">
            <text class="header-icon">⭐</text>
            <text class="card-title">吉星神煞</text>
          </view>
          <view class="card-content">
            <view class="stars-grid" wx:if="{{auspiciousStars && auspiciousStars.length > 0}}">
              <view class="star-item auspicious" wx:for="{{auspiciousStars}}" wx:key="name">
                <text class="star-name">{{item.name}}</text>
                <text class="star-position">{{item.position}}</text>
                <text class="star-desc">{{item.desc}}</text>
              </view>
            </view>
            <view wx:else class="no-stars">
              <text class="no-stars-text">暂无特殊吉星</text>
            </view>
          </view>
        </view>

        <!-- 💀 凶星神煞卡片 -->
        <view class="tianggong-card inauspicious-stars-card">
          <view class="card-header">
            <text class="header-icon">💀</text>
            <text class="card-title">凶星神煞</text>
          </view>
          <view class="card-content">
            <view class="stars-grid" wx:if="{{inauspiciousStars && inauspiciousStars.length > 0}}">
              <view class="star-item inauspicious" wx:for="{{inauspiciousStars}}" wx:key="name">
                <text class="star-name">{{item.name}}</text>
                <text class="star-position">{{item.position}}</text>
                <text class="star-desc">{{item.desc}}</text>
              </view>
            </view>
            <view wx:else class="no-stars">
              <text class="no-stars-text">暂无特殊凶星</text>
            </view>
          </view>
        </view>

        <!-- 📊 神煞综合分析卡片 -->
        <view class="tianggong-card shensha-summary-card">
          <view class="card-header">
            <text class="header-icon">📊</text>
            <text class="card-title">神煞综合分析</text>
          </view>
          <view class="card-content">
            <view class="shensha-stats">
              <view class="stat-item">
                <text class="stat-label">吉星数量</text>
                <text class="stat-value">{{auspiciousStars.length || 0}}</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">凶星数量</text>
                <text class="stat-value">{{inauspiciousStars.length || 0}}</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">总体评价</text>
                <text class="stat-value">{{baziData.shensha_overall || '平衡'}}</text>
              </view>
            </view>
            <view class="shensha-conclusion">
              <text class="conclusion-text">{{baziData.shensha_summary || '神煞分析计算中...'}}</text>
            </view>
          </view>
        </view>

      </view>

      <!-- 大运流年页面 -->
      <view wx:elif="{{currentTab === 'fortune'}}" class="tab-panel fortune-panel">

        <!-- 当前大运卡片 -->
        <view class="tianggong-card current-dayun-card">
          <view class="card-header">
            <text class="header-icon">🌊</text>
            <text class="card-title">当前大运</text>
          </view>
          <view class="card-content">
            <view class="current-dayun-info">
              <view class="dayun-pillars-large">
                <text class="large-tiangan">{{currentDayun.heavenly_stem}}</text>
                <text class="large-dizhi">{{currentDayun.earthly_branch}}</text>
              </view>
              <view class="dayun-details">
                <text class="dayun-period">{{currentDayun.age_range}} ({{currentDayun.year_range}})</text>
                <text class="dayun-level">运势：{{currentDayun.fortune_level}}</text>
                <text class="dayun-description">{{currentDayun.description}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 流年运势卡片 -->
        <view class="tianggong-card liunian-card">
          <view class="card-header">
            <text class="header-icon">📅</text>
            <text class="card-title">近期流年</text>
          </view>
          <view class="card-content">
            <view class="liunian-list">
              <view class="liunian-item" wx:for="{{recentLiunian}}" wx:key="year">
                <view class="liunian-year">
                  <text class="year-number">{{item.year}}</text>
                  <text class="year-ganzhi">{{item.heavenly_stem}}{{item.earthly_branch}}</text>
                </view>
                <view class="liunian-fortune">
                  <text class="fortune-level">{{item.fortune_level}}</text>
                  <text class="fortune-desc">{{item.description}}</text>
                </view>
                <view class="liunian-events" wx:if="{{item.key_events && item.key_events.length > 0}}">
                  <text class="events-title">关键事件</text>
                  <text class="events-list" wx:for="{{item.key_events}}" wx:for-item="event" wx:key="*this">{{event}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 十年大运总览卡片 -->
        <view class="tianggong-card dayun-overview-card">
          <view class="card-header">
            <text class="header-icon">🔄</text>
            <text class="card-title">十年大运总览</text>
          </view>
          <view class="card-content">
            <view class="dayun-timeline-overview">
              <view class="timeline-item" wx:for="{{allDayun}}" wx:key="age_range">
                <view class="timeline-period">
                  <text class="period-age">{{item.age_range}}</text>
                  <text class="period-years">{{item.year_range}}</text>
                </view>
                <view class="timeline-content">
                  <view class="dayun-ganzhi">
                    <text class="ganzhi-text">{{item.heavenly_stem}}{{item.earthly_branch}}</text>
                  </view>
                  <view class="fortune-indicator {{item.fortune_level}}">
                    <text class="indicator-text">{{item.fortune_level}}</text>
                  </view>
                  <text class="timeline-desc">{{item.brief_desc}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

      </view>

      <!-- 格局用神页面 -->
      <view wx:elif="{{currentTab === 'professional'}}" class="tab-panel professional-panel">

        <!-- 数字化分析总览卡片 -->
        <view class="tianggong-card digital-overview-card">
          <view class="card-header">
            <text class="header-icon">�</text>
            <text class="card-title">数字化分析总览</text>
            <view class="enhanced-badge">
              <text class="badge-text">增强算法</text>
            </view>
          </view>
          <view class="card-content">
            <view class="digital-stats">
              <view class="stat-item">
                <text class="stat-label">分析规则数</text>
                <text class="stat-value">{{professionalAnalysis.layeredResults.totalMatched || '1148'}}</text>
                <text class="stat-unit">条</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">综合置信度</text>
                <text class="stat-value">{{professionalAnalysis.layeredResults.confidencePercent || '85.0'}}</text>
                <text class="stat-unit">%</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">算法版本</text>
                <text class="stat-value">{{professionalWuxingData.professionalData.version || 'v3.0'}}</text>
                <text class="stat-unit"></text>
              </view>
            </view>
            <view class="confidence-bar">
              <text class="bar-label">分析可信度</text>
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{professionalAnalysis.layeredResults.confidencePercent || '85'}}%;"></view>
              </view>
              <text class="bar-value">{{professionalAnalysis.layeredResults.confidencePercent || '85'}}%</text>
            </view>
          </view>
        </view>

        <!-- 增强格局分析卡片 -->
        <view class="tianggong-card enhanced-pattern-card">
          <view class="card-header">
            <text class="header-icon">👑</text>
            <text class="card-title">增强格局分析</text>
          </view>
          <view class="card-content">
            <view class="pattern-analysis">
              <view class="main-pattern">
                <text class="pattern-label">主格局</text>
                <text class="pattern-name">{{enhancedPatternResult.pattern_name || professionalAnalysis.pattern || '正财格'}}</text>
                <text class="pattern-confidence">置信度: {{enhancedPatternResult.confidence_percent || '90'}}%</text>
              </view>
              <view class="pattern-level">
                <text class="level-label">格局层次</text>
                <text class="level-value">{{enhancedPatternResult.pattern_level || '中上'}}</text>
                <text class="level-score">评分: {{enhancedPatternResult.pattern_score || '85'}}/100</text>
              </view>
              <view class="pattern-rules">
                <text class="rules-title">匹配规则 ({{professionalAnalysis.enhancedRules.length || '2'}}条)</text>
                <view class="rule-list">
                  <view class="rule-item" wx:for="{{professionalAnalysis.enhancedRules || []}}" wx:key="pattern_name">
                    <view class="rule-header">
                      <text class="rule-name">{{item.pattern_name}}</text>
                      <text class="rule-confidence">{{item.confidencePercent}}%</text>
                    </view>
                    <text class="rule-desc">{{item.interpretations}}</text>
                    <view class="rule-scores">
                      <text class="match-score">匹配度: {{item.matchScorePercent}}%</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 增强用神计算卡片 -->
        <view class="tianggong-card enhanced-yongshen-card">
          <view class="card-header">
            <text class="header-icon">🎯</text>
            <text class="card-title">增强用神计算</text>
          </view>
          <view class="card-content">
            <view class="yongshen-calculation">
              <!-- 用神计算结果 -->
              <view class="calculation-result">
                <text class="result-title">计算结果</text>
                <view class="god-list">
                  <view class="god-item primary" wx:for="{{enhancedYongshenResult.favorable_gods || []}}" wx:key="name" wx:if="{{index < 3}}">
                    <view class="god-header">
                      <text class="god-name">{{item.name}}</text>
                      <text class="god-priority">优先级{{index + 1}}</text>
                    </view>
                    <view class="god-details">
                      <text class="god-strength">强度: {{item.strength || '85'}}%</text>
                      <text class="god-effect">效果: {{item.effect || '扶助日主'}}</text>
                    </view>
                    <text class="god-reason">{{item.reason || '调候急救，优先级最高'}}</text>
                  </view>
                </view>
              </view>

              <!-- 三级优先级算法展示 -->
              <view class="priority-algorithm">
                <text class="algorithm-title">三级优先级算法</text>
                <view class="priority-levels">
                  <view class="priority-item level-1">
                    <text class="level-number">1</text>
                    <text class="level-name">调候用神</text>
                    <text class="level-god">{{enhancedYongshenResult.climate_adjustment || '火'}}</text>
                    <text class="level-score">{{enhancedYongshenResult.climate_score || '95'}}分</text>
                  </view>
                  <view class="priority-item level-2">
                    <text class="level-number">2</text>
                    <text class="level-name">格局用神</text>
                    <text class="level-god">{{enhancedYongshenResult.pattern_support || '印'}}</text>
                    <text class="level-score">{{enhancedYongshenResult.pattern_score || '85'}}分</text>
                  </view>
                  <view class="priority-item level-3">
                    <text class="level-number">3</text>
                    <text class="level-name">五行制衡</text>
                    <text class="level-god">{{enhancedYongshenResult.balance_adjustment || '水'}}</text>
                    <text class="level-score">{{enhancedYongshenResult.balance_score || '75'}}分</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 用神喜忌卡片 -->
        <view class="tianggong-card yongshen-card">
          <view class="card-header">
            <text class="header-icon">🎯</text>
            <text class="card-title">用神喜忌</text>
          </view>
          <view class="card-content">
            <view class="yongshen-analysis">
              <view class="yongshen-section">
                <text class="section-title">用神</text>
                <text class="yongshen-element">{{baziData.yongshen.primary || baziData.primaryGod || professionalAnalysis.yongshen.primary || enhancedYongshenResult.primary_god || '计算中'}}</text>
                <text class="yongshen-desc">{{baziData.yongshen.primary_desc || baziData.primaryGodDesc || professionalAnalysis.yongshen.primary_effect || '分析中...'}}</text>
              </view>
              <view class="yongshen-section">
                <text class="section-title">喜神</text>
                <text class="yongshen-element">{{baziData.yongshen.favorable || baziData.favorableGod || professionalAnalysis.yongshen.favorable || enhancedYongshenResult.favorable_god || '计算中'}}</text>
                <text class="yongshen-desc">{{baziData.yongshen.favorable_desc || baziData.favorableGodDesc || professionalAnalysis.yongshen.favorable_effect || '分析中...'}}</text>
              </view>
              <view class="yongshen-section">
                <text class="section-title">忌神</text>
                <text class="yongshen-element">{{baziData.yongshen.unfavorable || baziData.unfavorableGod || professionalAnalysis.yongshen.unfavorable || enhancedYongshenResult.unfavorable_god || '计算中'}}</text>
                <text class="yongshen-desc">{{baziData.yongshen.unfavorable_desc || baziData.unfavorableGodDesc || professionalAnalysis.yongshen.unfavorable_effect || '分析中...'}}</text>
              </view>
            </view>
          </view>
        </view>



        <!-- 清浊评估数字化分析卡片 -->
        <view class="tianggong-card digital-clarity-card">
          <view class="card-header">
            <text class="header-icon">⚖️</text>
            <text class="card-title">清浊评估数字化分析</text>
          </view>
          <view class="card-content">
            <view class="clarity-digital">
              <!-- 评估公式 -->
              <view class="formula-display">
                <text class="formula-title">数字化评估公式</text>
                <text class="formula-content">清浊分 = 十神纯度×0.4 + 五行平衡×0.3 + 干支配合×0.3 - 刑冲数×0.05</text>
              </view>

              <!-- 分项计算 -->
              <view class="calculation-breakdown">
                <view class="calc-item">
                  <view class="calc-header">
                    <text class="calc-name">十神纯度</text>
                    <text class="calc-weight">权重40%</text>
                  </view>
                  <view class="calc-details">
                    <text class="calc-score">{{enhancedPatternResult.ten_gods_purity || '85'}}分</text>
                    <text class="calc-contribution">贡献: {{enhancedPatternResult.ten_gods_contribution || '34'}}分</text>
                  </view>
                </view>
                <view class="calc-item">
                  <view class="calc-header">
                    <text class="calc-name">五行平衡</text>
                    <text class="calc-weight">权重30%</text>
                  </view>
                  <view class="calc-details">
                    <text class="calc-score">{{professionalWuxingData.professionalData.balanceIndex || '75'}}分</text>
                    <text class="calc-contribution">贡献: {{enhancedPatternResult.wuxing_contribution || '22.5'}}分</text>
                  </view>
                </view>
                <view class="calc-item">
                  <view class="calc-header">
                    <text class="calc-name">干支配合</text>
                    <text class="calc-weight">权重30%</text>
                  </view>
                  <view class="calc-details">
                    <text class="calc-score">{{enhancedPatternResult.ganzhi_harmony || '80'}}分</text>
                    <text class="calc-contribution">贡献: {{enhancedPatternResult.ganzhi_contribution || '24'}}分</text>
                  </view>
                </view>
                <view class="calc-item negative">
                  <view class="calc-header">
                    <text class="calc-name">刑冲扣分</text>
                    <text class="calc-weight">扣减5%</text>
                  </view>
                  <view class="calc-details">
                    <text class="calc-score">{{enhancedPatternResult.clash_count || '1'}}处</text>
                    <text class="calc-contribution">扣减: -{{enhancedPatternResult.clash_penalty || '5'}}分</text>
                  </view>
                </view>
              </view>

              <!-- 最终结果 -->
              <view class="final-clarity">
                <view class="clarity-score-display">
                  <text class="score-label">最终清浊分</text>
                  <text class="score-value">{{enhancedPatternResult.final_clarity_score || '85.5'}}</text>
                  <text class="score-unit">分</text>
                </view>
                <view class="clarity-level-display">
                  <text class="level-label">清浊等级</text>
                  <text class="level-value">{{enhancedPatternResult.clarity_level || '清格'}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 动态影响分析卡片 -->
        <view class="tianggong-card dynamic-analysis-card">
          <view class="card-header">
            <text class="header-icon">�</text>
            <text class="card-title">大运流年影响</text>
          </view>
          <view class="card-content">
            <view class="dynamic-impact">
              <view class="current-decade">
                <text class="decade-title">当前大运</text>
                <text class="decade-ganzhi">{{professionalAnalysis.current_decade.ganzhi || baziData.currentDayun.ganzhi || '甲辰'}}</text>
                <text class="decade-impact">{{professionalAnalysis.current_decade.impact || '印星透干，利升学'}}</text>
              </view>
              <view class="energy-curve">
                <text class="curve-title">能量衰减曲线</text>
                <text class="curve-desc">{{professionalAnalysis.energy_curve.description || '大运前期力量强，后期渐弱'}}</text>
              </view>
              <view class="turning-points">
                <text class="points-title">关键转折点</text>
                <view class="point-list">
                  <view class="point-item" wx:for="{{professionalAnalysis.turning_points || []}}" wx:key="year">
                    <text class="point-year">{{item.year}}年</text>
                    <text class="point-event">{{item.event}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 置信度分析卡片 -->
        <view class="tianggong-card confidence-analysis-card">
          <view class="card-header">
            <text class="header-icon">📊</text>
            <text class="card-title">置信度分析</text>
          </view>
          <view class="card-content">
            <view class="confidence-analysis">
              <!-- 综合置信度 -->
              <view class="overall-confidence">
                <text class="confidence-title">综合分析置信度</text>
                <view class="confidence-score">
                  <text class="score-value">{{confidenceScores.comprehensive_analysis * 100 || '89'}}%</text>
                  <text class="score-desc">基于{{professionalAnalysis.layeredResults.totalMatched || '1148'}}条规则匹配</text>
                </view>
              </view>

              <!-- 分项置信度 -->
              <view class="detailed-confidence">
                <text class="detail-title">分项置信度评估</text>
                <view class="confidence-items">
                  <view class="confidence-item">
                    <text class="item-name">排盘准确度</text>
                    <view class="confidence-bar">
                      <view class="confidence-fill" style="width: {{confidenceScores.paipan_accuracy * 100 || '92'}}%;"></view>
                    </view>
                    <text class="item-score">{{confidenceScores.paipan_accuracy * 100 || '92'}}%</text>
                  </view>
                  <view class="confidence-item">
                    <text class="item-name">神煞分析</text>
                    <view class="confidence-bar">
                      <view class="confidence-fill" style="width: {{confidenceScores.shensha_analysis * 100 || '88'}}%;"></view>
                    </view>
                    <text class="item-score">{{confidenceScores.shensha_analysis * 100 || '88'}}%</text>
                  </view>
                  <view class="confidence-item">
                    <text class="item-name">纳音分析</text>
                    <view class="confidence-bar">
                      <view class="confidence-fill" style="width: {{confidenceScores.nayin_analysis * 100 || '85'}}%;"></view>
                    </view>
                    <text class="item-score">{{confidenceScores.nayin_analysis * 100 || '85'}}%</text>
                  </view>
                  <view class="confidence-item">
                    <text class="item-name">大运分析</text>
                    <view class="confidence-bar">
                      <view class="confidence-fill" style="width: {{confidenceScores.dayun_analysis * 100 || '90'}}%;"></view>
                    </view>
                    <text class="item-score">{{confidenceScores.dayun_analysis * 100 || '90'}}%</text>
                  </view>
                  <view class="confidence-item">
                    <text class="item-name">长生分析</text>
                    <view class="confidence-bar">
                      <view class="confidence-fill" style="width: {{confidenceScores.changsheng_analysis * 100 || '87'}}%;"></view>
                    </view>
                    <text class="item-score">{{confidenceScores.changsheng_analysis * 100 || '87'}}%</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 增强专业建议卡片 -->
        <view class="tianggong-card enhanced-advice-card">
          <view class="card-header">
            <text class="header-icon">💼</text>
            <text class="card-title">增强专业建议</text>
          </view>
          <view class="card-content">
            <view class="enhanced-recommendations">
              <!-- 职业匹配度分析 -->
              <view class="career-matching">
                <text class="matching-title">职业匹配度分析</text>
                <view class="career-scores">
                  <view class="career-item" wx:for="{{enhancedAdviceResult.career_recommendations || []}}" wx:key="field" wx:if="{{index < 5}}">
                    <view class="career-header">
                      <text class="career-name">{{item.field}}</text>
                      <text class="career-score">{{item.match_score}}%</text>
                    </view>
                    <view class="career-bar">
                      <view class="career-fill" style="width: {{item.match_score}}%;"></view>
                    </view>
                    <text class="career-reason">{{item.reason}}</text>
                  </view>
                </view>
              </view>

              <!-- 时机分析 -->
              <view class="timing-analysis">
                <text class="timing-title">最佳发展时机</text>
                <view class="timing-periods">
                  <view class="period-item" wx:for="{{enhancedAdviceResult.optimal_periods || []}}" wx:key="period">
                    <view class="period-header">
                      <text class="period-name">{{item.period}}</text>
                      <text class="period-score">{{item.favorability}}%</text>
                    </view>
                    <text class="period-desc">{{item.description}}</text>
                    <view class="period-events">
                      <text class="event-tag" wx:for="{{item.suitable_events || []}}" wx:key="*this">{{item}}</text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 个性化修正 -->
              <view class="personalized-adjustment">
                <text class="adjust-title">个性化修正建议</text>
                <view class="adjust-factors">
                  <view class="factor-item">
                    <text class="factor-label">性别修正</text>
                    <text class="factor-value">{{enhancedAdviceResult.gender_adjustment.factor || '+5%'}}</text>
                    <text class="factor-desc">{{enhancedAdviceResult.gender_adjustment.description || '女性印星更利文职'}}</text>
                  </view>
                  <view class="factor-item">
                    <text class="factor-label">年龄修正</text>
                    <text class="factor-value">{{enhancedAdviceResult.age_adjustment.factor || '+3%'}}</text>
                    <text class="factor-desc">{{enhancedAdviceResult.age_adjustment.description || '青年期适合积累经验'}}</text>
                  </view>
                  <view class="factor-item">
                    <text class="factor-label">地域修正</text>
                    <text class="factor-value">{{enhancedAdviceResult.location_adjustment.factor || '+2%'}}</text>
                    <text class="factor-desc">{{enhancedAdviceResult.location_adjustment.description || '南方火旺利发展'}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

      </view>

      <!-- 古籍分析页面 -->
      <view wx:elif="{{currentTab === 'classical'}}" class="tab-panel classical-panel">

        <!-- 古籍命理卡片 -->
        <view class="tianggong-card classical-analysis-card">
          <view class="card-header">
            <text class="header-icon">📜</text>
            <text class="card-title">古籍命理</text>
          </view>
          <view class="card-content">
            <view class="classical-quotes">
              <view class="quote-item" wx:for="{{classicalAnalysis.quotes}}" wx:key="source">
                <text class="quote-source">《{{item.source}}》</text>
                <text class="quote-content">{{item.content}}</text>
                <text class="quote-interpretation">释义：{{item.interpretation}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 古法断语卡片 -->
        <view class="tianggong-card classical-judgment-card">
          <view class="card-header">
            <text class="header-icon">⚖️</text>
            <text class="card-title">古法断语</text>
          </view>
          <view class="card-content">
            <view class="judgment-sections">
              <view class="judgment-section">
                <text class="section-title">性格断语</text>
                <text class="judgment-content">{{classicalAnalysis.personality_judgment}}</text>
              </view>
              <view class="judgment-section">
                <text class="section-title">事业断语</text>
                <text class="judgment-content">{{classicalAnalysis.career_judgment}}</text>
              </view>
              <view class="judgment-section">
                <text class="section-title">财运断语</text>
                <text class="judgment-content">{{classicalAnalysis.wealth_judgment}}</text>
              </view>
              <view class="judgment-section">
                <text class="section-title">婚姻断语</text>
                <text class="judgment-content">{{classicalAnalysis.marriage_judgment}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 古籍格局卡片 -->
        <view class="tianggong-card classical-pattern-card">
          <view class="card-header">
            <text class="header-icon">🔮</text>
            <text class="card-title">古籍格局</text>
          </view>
          <view class="card-content">
            <view class="pattern-analysis">
              <view class="pattern-name">
                <text class="pattern-title">格局名称</text>
                <text class="pattern-value">{{classicalAnalysis.pattern.name}}</text>
              </view>
              <view class="pattern-description">
                <text class="desc-title">格局描述</text>
                <text class="desc-content">{{classicalAnalysis.pattern.description}}</text>
              </view>
              <view class="pattern-characteristics">
                <text class="char-title">格局特征</text>
                <view class="char-list">
                  <text class="char-item" wx:for="{{classicalAnalysis.pattern.characteristics}}" wx:key="*this">• {{item}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 古法建议卡片 -->
        <view class="tianggong-card classical-advice-card">
          <view class="card-header">
            <text class="header-icon">💡</text>
            <text class="card-title">古法建议</text>
          </view>
          <view class="card-content">
            <view class="advice-sections">
              <view class="advice-section">
                <text class="advice-title">修身养性</text>
                <text class="advice-content">{{classicalAnalysis.advice.self_cultivation}}</text>
              </view>
              <view class="advice-section">
                <text class="advice-title">趋吉避凶</text>
                <text class="advice-content">{{classicalAnalysis.advice.fortune_guidance}}</text>
              </view>
              <view class="advice-section">
                <text class="advice-title">人生规划</text>
                <text class="advice-content">{{classicalAnalysis.advice.life_planning}}</text>
              </view>
            </view>
          </view>
        </view>

      </view>

      <!-- 应期分析 -->
      <view wx:elif="{{currentTab === 'timing'}}" class="tab-panel timing-panel">
        <!-- 病药平衡法则卡片 -->
        <view class="timing-card">
          <view class="card-header">
            <text class="card-title">病药平衡法则</text>
            <text class="card-subtitle">《滴天髓·病药章》核心理论</text>
          </view>
          <view class="card-content">
            <view class="balance-analysis">
              <view class="disease-section">
                <text class="section-title">命局之病</text>
                <text class="disease-text" wx:if="{{professionalTimingAnalysis.disease_medicine.disease_analysis}}">
                  {{professionalTimingAnalysis.disease_medicine.disease_analysis}}
                </text>
                <text class="disease-text calculating" wx:else>
                  正在分析命局病神...
                </text>
              </view>
              <view class="medicine-section">
                <text class="section-title">大运之药</text>
                <text class="medicine-text" wx:if="{{professionalTimingAnalysis.disease_medicine.medicine_recommendation}}">
                  {{professionalTimingAnalysis.disease_medicine.medicine_recommendation}}
                </text>
                <text class="medicine-text calculating" wx:else>
                  正在推荐药神方案...
                </text>
              </view>
              <view class="balance-result">
                <text class="result-title">平衡评分</text>
                <view class="balance-score-display">
                  <text class="score-number" wx:if="{{professionalTimingAnalysis.disease_medicine.balance_score}}">
                    {{professionalTimingAnalysis.disease_medicine.balance_score}}分
                  </text>
                  <text class="score-number calculating" wx:else>
                    计算中...
                  </text>
                  <text class="score-status" wx:if="{{professionalTimingAnalysis.disease_medicine.balance_status}}">
                    ({{professionalTimingAnalysis.disease_medicine.balance_status}})
                  </text>
                </view>
              </view>
              <view class="period-prediction">
                <text class="result-title">应期预测</text>
                <!-- 🆕 基于能量阈值的统一显示逻辑 -->
                <view class="unified-timing-results">
                  <view class="timing-event-item">
                    <text class="event-label">婚姻应期：</text>
                    <text class="event-result" wx:if="{{professionalTimingAnalysis.event_analyses.marriage.threshold_status === 'met'}}">
                      {{professionalTimingAnalysis.event_analyses.marriage.best_year_display || professionalTimingAnalysis.event_analyses.marriage.best_year}}
                    </text>
                    <text class="event-result age-not-met" wx:elif="{{professionalTimingAnalysis.event_analyses.marriage.threshold_status === 'age_not_met'}}">
                      {{professionalTimingAnalysis.event_analyses.marriage.message}}
                    </text>
                    <text class="event-result threshold-not-met" wx:elif="{{professionalTimingAnalysis.event_analyses.marriage.threshold_status === 'not_met'}}">
                      {{professionalTimingAnalysis.event_analyses.marriage.message}}
                    </text>
                    <text class="event-result calculating" wx:else>
                      正在计算中...
                    </text>
                  </view>
                  <view class="timing-event-item">
                    <text class="event-label">升职应期：</text>
                    <text class="event-result" wx:if="{{professionalTimingAnalysis.event_analyses.promotion.threshold_status === 'met'}}">
                      {{professionalTimingAnalysis.event_analyses.promotion.best_year_display || professionalTimingAnalysis.event_analyses.promotion.best_year}}
                    </text>
                    <text class="event-result age-not-met" wx:elif="{{professionalTimingAnalysis.event_analyses.promotion.threshold_status === 'age_not_met'}}">
                      {{professionalTimingAnalysis.event_analyses.promotion.message}}
                    </text>
                    <text class="event-result threshold-not-met" wx:elif="{{professionalTimingAnalysis.event_analyses.promotion.threshold_status === 'not_met'}}">
                      {{professionalTimingAnalysis.event_analyses.promotion.message}}
                    </text>
                    <text class="event-result calculating" wx:else>
                      正在计算中...
                    </text>
                  </view>
                  <view class="timing-event-item">
                    <text class="event-label">生育应期：</text>
                    <text class="event-result" wx:if="{{professionalTimingAnalysis.event_analyses.childbirth.threshold_status === 'met'}}">
                      {{professionalTimingAnalysis.event_analyses.childbirth.best_year_display || professionalTimingAnalysis.event_analyses.childbirth.best_year}}
                    </text>
                    <text class="event-result age-not-met" wx:elif="{{professionalTimingAnalysis.event_analyses.childbirth.threshold_status === 'age_not_met'}}">
                      {{professionalTimingAnalysis.event_analyses.childbirth.message}}
                    </text>
                    <text class="event-result threshold-not-met" wx:elif="{{professionalTimingAnalysis.event_analyses.childbirth.threshold_status === 'not_met'}}">
                      {{professionalTimingAnalysis.event_analyses.childbirth.message}}
                    </text>
                    <text class="event-result calculating" wx:else>
                      正在计算中...
                    </text>
                  </view>
                  <view class="timing-event-item">
                    <text class="event-label">财运应期：</text>
                    <text class="event-result" wx:if="{{professionalTimingAnalysis.event_analyses.wealth.threshold_status === 'met'}}">
                      {{professionalTimingAnalysis.event_analyses.wealth.best_year_display || professionalTimingAnalysis.event_analyses.wealth.best_year}}
                    </text>
                    <text class="event-result age-not-met" wx:elif="{{professionalTimingAnalysis.event_analyses.wealth.threshold_status === 'age_not_met'}}">
                      {{professionalTimingAnalysis.event_analyses.wealth.message}}
                    </text>
                    <text class="event-result threshold-not-met" wx:elif="{{professionalTimingAnalysis.event_analyses.wealth.threshold_status === 'not_met'}}">
                      {{professionalTimingAnalysis.event_analyses.wealth.message}}
                    </text>
                    <text class="event-result calculating" wx:else>
                      正在计算中...
                    </text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 能量阈值模型卡片 -->
        <view class="timing-card">
          <view class="card-header">
            <text class="card-title">能量阈值五年模型</text>
            <text class="card-subtitle">《穷通宝鉴·月令章》量化标准</text>
          </view>
          <view class="card-content">
            <view class="threshold-grid">
              <view class="threshold-item">
                <text class="threshold-label">婚姻应期阈值</text>
                <view class="threshold-bar">
                  <view class="threshold-fill" style="width: {{professionalTimingAnalysis.energy_thresholds.marriage_met ? 100 : Math.min(100, professionalTimingAnalysis.energy_thresholds.marriage_current_energy / professionalTimingAnalysis.energy_thresholds.marriage_required_threshold * 100)}}%;"></view>
                </view>
                <text class="threshold-value">{{professionalTimingAnalysis.energy_thresholds.marriage_current_energy}}% / {{professionalTimingAnalysis.energy_thresholds.marriage_required_threshold}}%</text>
                <text class="threshold-status">{{professionalTimingAnalysis.energy_thresholds.marriage_met ? '✅ 达标' : '⚠️ 能量阈值未达标，预计' + professionalTimingAnalysis.energy_thresholds.marriage_estimated_year + '年达标'}}</text>
              </view>
              <view class="threshold-item">
                <text class="threshold-label">升职应期阈值</text>
                <view class="threshold-bar">
                  <view class="threshold-fill" style="width: {{professionalTimingAnalysis.energy_thresholds.promotion_met ? 100 : Math.min(100, professionalTimingAnalysis.energy_thresholds.promotion_current_energy / professionalTimingAnalysis.energy_thresholds.promotion_required_threshold * 100)}}%;"></view>
                </view>
                <text class="threshold-value">{{professionalTimingAnalysis.energy_thresholds.promotion_current_energy}}% / {{professionalTimingAnalysis.energy_thresholds.promotion_required_threshold}}%</text>
                <text class="threshold-status">{{professionalTimingAnalysis.energy_thresholds.promotion_met ? '✅ 达标' : '⚠️ 能量阈值未达标，预计' + professionalTimingAnalysis.energy_thresholds.promotion_estimated_year + '年达标'}}</text>
              </view>
              <view class="threshold-item">
                <text class="threshold-label">生育应期阈值</text>
                <view class="threshold-bar">
                  <view class="threshold-fill" style="width: {{professionalTimingAnalysis.energy_thresholds.childbirth_met ? 100 : Math.min(100, professionalTimingAnalysis.energy_thresholds.childbirth_current_energy / professionalTimingAnalysis.energy_thresholds.childbirth_required_threshold * 100)}}%;"></view>
                </view>
                <text class="threshold-value">{{professionalTimingAnalysis.energy_thresholds.childbirth_current_energy}}% / {{professionalTimingAnalysis.energy_thresholds.childbirth_required_threshold}}%</text>
                <text class="threshold-status">{{professionalTimingAnalysis.energy_thresholds.childbirth_met ? '✅ 达标' : '⚠️ 能量阈值未达标，预计' + professionalTimingAnalysis.energy_thresholds.childbirth_estimated_year + '年达标'}}</text>
              </view>
              <view class="threshold-item">
                <text class="threshold-label">财运应期阈值</text>
                <view class="threshold-bar">
                  <view class="threshold-fill" style="width: {{professionalTimingAnalysis.energy_thresholds.wealth_met ? 100 : Math.min(100, professionalTimingAnalysis.energy_thresholds.wealth_current_energy / professionalTimingAnalysis.energy_thresholds.wealth_required_threshold * 100)}}%;"></view>
                </view>
                <text class="threshold-value">{{professionalTimingAnalysis.energy_thresholds.wealth_current_energy}}% / {{professionalTimingAnalysis.energy_thresholds.wealth_required_threshold}}%</text>
                <text class="threshold-status">{{professionalTimingAnalysis.energy_thresholds.wealth_met ? '✅ 达标' : '⚠️ 能量阈值未达标，预计' + professionalTimingAnalysis.energy_thresholds.wealth_estimated_year + '年达标'}}</text>
              </view>
            </view>

          </view>
        </view>

        <!-- 三重引动机制卡片 -->
        <view class="timing-card">
          <view class="card-header">
            <text class="card-title">三重引动机制</text>
            <text class="card-subtitle">《渊海子平·动静论》应期引动三要素</text>
          </view>
          <view class="card-content">
            <view class="activation-grid">
              <view class="activation-item">
                <text class="item-title">星动</text>
                <text class="item-desc" wx:if="{{professionalTimingAnalysis.triple_activation.star_activation}}">
                  {{professionalTimingAnalysis.triple_activation.star_activation}}
                </text>
                <text class="item-desc calculating" wx:else>
                  正在分析星动情况...
                </text>
              </view>
              <view class="activation-item">
                <text class="item-title">宫动</text>
                <text class="item-desc" wx:if="{{professionalTimingAnalysis.triple_activation.palace_activation}}">
                  {{professionalTimingAnalysis.triple_activation.palace_activation}}
                </text>
                <text class="item-desc calculating" wx:else>
                  正在分析宫动情况...
                </text>
              </view>
              <view class="activation-item">
                <text class="item-title">神煞动</text>
                <text class="item-desc" wx:if="{{professionalTimingAnalysis.triple_activation.shensha_activation}}">
                  {{professionalTimingAnalysis.triple_activation.shensha_activation}}
                </text>
                <text class="item-desc calculating" wx:else>
                  正在分析神煞引动...
                </text>
              </view>
            </view>
            <view class="activation-summary">
              <text class="summary-title">引动综合评估</text>
              <view class="confidence-display">
                <text class="confidence-label">婚姻应期置信度</text>
                <text class="confidence-value" wx:if="{{professionalTimingAnalysis.triple_activation.marriage_confidence !== undefined}}">
                  {{professionalTimingAnalysis.triple_activation.marriage_confidence}}%
                </text>
                <text class="confidence-value calculating" wx:else>
                  计算中...
                </text>
              </view>
              <view class="confidence-display">
                <text class="confidence-label">升职应期置信度</text>
                <text class="confidence-value" wx:if="{{professionalTimingAnalysis.triple_activation.promotion_confidence !== undefined}}">
                  {{professionalTimingAnalysis.triple_activation.promotion_confidence}}%
                </text>
                <text class="confidence-value calculating" wx:else>
                  计算中...
                </text>
              </view>
              <view class="confidence-display">
                <text class="confidence-label">生育应期置信度</text>
                <text class="confidence-value" wx:if="{{professionalTimingAnalysis.triple_activation.childbirth_confidence !== undefined}}">
                  {{professionalTimingAnalysis.triple_activation.childbirth_confidence}}%
                </text>
                <text class="confidence-value calculating" wx:else>
                  计算中...
                </text>
              </view>
              <view class="confidence-display">
                <text class="confidence-label">财运应期置信度</text>
                <text class="confidence-value" wx:if="{{professionalTimingAnalysis.triple_activation.wealth_confidence !== undefined}}">
                  {{professionalTimingAnalysis.triple_activation.wealth_confidence}}%
                </text>
                <text class="confidence-value calculating" wx:else>
                  计算中...
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 动态分析引擎结果卡片 -->
        <view class="timing-card">
          <view class="card-header">
            <text class="card-title">动态分析引擎</text>
            <text class="card-subtitle">大运流年联动分析</text>
          </view>

          <view class="card-content">
            <view class="dynamic-analysis">
              <view class="analysis-item">
                <text class="item-title">三点一线法则</text>
                <text class="item-desc" wx:if="{{professionalTimingAnalysis.dynamic_analysis.three_point_rule}}">
                  {{professionalTimingAnalysis.dynamic_analysis.three_point_rule}}
                </text>
                <text class="item-desc calculating" wx:else>
                  正在分析三点一线法则...
                </text>
              </view>
              <view class="analysis-item">
                <text class="item-title">时空力量</text>
                <text class="item-desc" wx:if="{{professionalTimingAnalysis.dynamic_analysis.spacetime_force}}">
                  {{professionalTimingAnalysis.dynamic_analysis.spacetime_force}}
                </text>
                <text class="item-desc calculating" wx:else>
                  正在分析时空力量...
                </text>
              </view>
              <view class="analysis-item">
                <text class="item-title">转折点识别</text>
                <text class="item-desc" wx:if="{{professionalTimingAnalysis.dynamic_analysis.turning_points}}">
                  {{professionalTimingAnalysis.dynamic_analysis.turning_points}}
                </text>
                <text class="item-desc calculating" wx:else>
                  正在识别转折点...
                </text>
              </view>
            </view>
            <view class="cultural-adaptation">
              <text class="adaptation-title">地域文化适配</text>
              <view class="adaptation-info">
                <text class="adaptation-item" wx:if="{{professionalTimingAnalysis.cultural_adaptation.region_type}}">
                  地域类型：{{professionalTimingAnalysis.cultural_adaptation.region_type}}
                </text>
                <text class="adaptation-item calculating" wx:else>
                  正在分析地域类型...
                </text>

                <text class="adaptation-item" wx:if="{{professionalTimingAnalysis.cultural_adaptation.parameter_adjustment}}">
                  参数调整：{{professionalTimingAnalysis.cultural_adaptation.parameter_adjustment}}
                </text>
                <text class="adaptation-item calculating" wx:else>
                  正在计算参数调整...
                </text>

                <text class="adaptation-item" wx:if="{{professionalTimingAnalysis.cultural_adaptation.historical_basis}}">
                  历史依据：{{professionalTimingAnalysis.cultural_adaptation.historical_basis}}
                </text>
                <text class="adaptation-item calculating" wx:else>
                  正在查找历史依据...
                </text>
              </view>
            </view>

          </view>
        </view>

      </view>

      <!-- 六亲分析页面 -->
      <view wx:elif="{{currentTab === 'liuqin'}}" class="tab-panel liuqin-panel">

        <!-- 配偶分析卡片 -->
        <view class="tianggong-card spouse-analysis-card">
          <view class="card-header">
            <text class="header-icon">💑</text>
            <text class="card-title">配偶分析</text>
          </view>
          <view class="card-content">
            <view class="spouse-info">
              <view class="spouse-characteristics">
                <text class="char-title">配偶特征</text>
                <text class="char-content">{{liuqinAnalysis.spouse.characteristics}}</text>
              </view>
              <view class="spouse-relationship">
                <text class="rel-title">夫妻关系</text>
                <text class="rel-content">{{liuqinAnalysis.spouse.relationship}}</text>
              </view>
              <view class="spouse-timing">
                <text class="timing-title">结婚时机</text>
                <text class="timing-content">{{liuqinAnalysis.spouse.marriage_timing}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 子女分析卡片 -->
        <view class="tianggong-card children-analysis-card">
          <view class="card-header">
            <text class="header-icon">👶</text>
            <text class="card-title">子女分析</text>
          </view>
          <view class="card-content">
            <view class="children-info">
              <view class="children-fortune">
                <text class="fortune-title">子女运势</text>
                <text class="fortune-content">{{liuqinAnalysis.children.fortune}}</text>
              </view>
              <view class="children-count">
                <text class="count-title">子女数量</text>
                <text class="count-content">{{liuqinAnalysis.children.expected_count}}</text>
              </view>
              <view class="children-relationship">
                <text class="rel-title">亲子关系</text>
                <text class="rel-content">{{liuqinAnalysis.children.relationship}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 兄弟姐妹分析卡片 -->
        <view class="tianggong-card siblings-analysis-card">
          <view class="card-header">
            <text class="header-icon">👫</text>
            <text class="card-title">兄弟姐妹</text>
          </view>
          <view class="card-content">
            <view class="siblings-info">
              <view class="siblings-relationship">
                <text class="rel-title">兄弟关系</text>
                <text class="rel-content">{{liuqinAnalysis.siblings.relationship}}</text>
              </view>
              <view class="siblings-support">
                <text class="support-title">互助情况</text>
                <text class="support-content">{{liuqinAnalysis.siblings.mutual_support}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 父母分析卡片 -->
        <view class="tianggong-card parents-analysis-card">
          <view class="card-header">
            <text class="header-icon">👨‍👩‍👧‍👦</text>
            <text class="card-title">父母分析</text>
          </view>
          <view class="card-content">
            <view class="parents-info">
              <view class="parent-item">
                <text class="parent-title">父亲关系</text>
                <text class="parent-desc">{{liuqinAnalysis.parents.father.relationship}}</text>
                <text class="parent-influence">{{liuqinAnalysis.parents.father.influence}}</text>
              </view>
              <view class="parent-item">
                <text class="parent-title">母亲关系</text>
                <text class="parent-desc">{{liuqinAnalysis.parents.mother.relationship}}</text>
                <text class="parent-influence">{{liuqinAnalysis.parents.mother.influence}}</text>
              </view>
              <view class="parents-overall">
                <text class="overall-title">父母运势影响</text>
                <text class="overall-desc">{{liuqinAnalysis.parents.overall.description}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 六亲关系总结卡片 -->
        <view class="tianggong-card liuqin-summary-card">
          <view class="card-header">
            <text class="header-icon">🎯</text>
            <text class="card-title">六亲关系总结</text>
          </view>
          <view class="card-content">
            <view class="summary-stats">
              <view class="stat-item">
                <text class="stat-number">{{liuqinAnalysis.summary.marriageScore}}</text>
                <text class="stat-label">婚姻指数</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">{{liuqinAnalysis.summary.childrenScore}}</text>
                <text class="stat-label">子女指数</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">{{liuqinAnalysis.summary.siblingsScore}}</text>
                <text class="stat-label">兄弟指数</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">{{liuqinAnalysis.summary.familyScore}}</text>
                <text class="stat-label">家庭和谐</text>
              </view>
            </view>
            <view class="summary-text">
              <text class="summary-title">整体评价：{{liuqinAnalysis.summary.overallLevel}}</text>
              <text class="summary-desc">{{liuqinAnalysis.summary.description}}</text>
              <text class="summary-advice">{{liuqinAnalysis.summary.advice}}</text>
            </view>
          </view>
        </view>

        <!-- 🏛️ 历史名人验证模块 -->
        <view class="tianggong-card historical-verification-card">
          <view class="card-header">
            <text class="header-icon">🏛️</text>
            <text class="card-title">与您八字相似的历史名人</text>
          </view>
          <view class="card-content">
            <!-- 相似度结果 -->
            <view class="similarity-results">
              <view class="celebrity-list" wx:if="{{similarCelebrities && similarCelebrities.length > 0}}">
                <view class="celebrity-item" wx:for="{{similarCelebrities}}" wx:key="name" bindtap="showCelebrityDetail" data-celebrity="{{item.celebrity}}">
                  <view class="celebrity-header">
                    <text class="celebrity-name">{{item.celebrity.name}}</text>
                    <view class="similarity-badge">
                      <text class="similarity-text">相似度</text>
                      <text class="similarity-value">{{item.similarity_display}}%</text>
                    </view>
                  </view>
                  <view class="celebrity-details">
                    <view class="detail-row">
                      <text class="detail-label">八字：</text>
                      <text class="detail-value">{{item.celebrity.bazi.fullBazi || (item.celebrity.bazi.year.gan + item.celebrity.bazi.year.zhi + ' ' + item.celebrity.bazi.month.gan + item.celebrity.bazi.month.zhi + ' ' + item.celebrity.bazi.day.gan + item.celebrity.bazi.day.zhi + ' ' + item.celebrity.bazi.hour.gan + item.celebrity.bazi.hour.zhi)}}</text>
                    </view>
                    <view class="detail-row">
                      <text class="detail-label">格局：</text>
                      <text class="detail-value">{{item.celebrity.pattern || '未知格局'}}</text>
                    </view>
                    <view class="detail-row">
                      <text class="detail-label">主要成就：</text>
                      <text class="detail-value">{{item.celebrity.dynasty}}{{item.celebrity.occupation[0] || '历史名人'}}</text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 加载状态 -->
              <view class="loading-state" wx:if="{{historicalVerificationLoading}}">
                <text class="loading-icon">🔄</text>
                <text class="loading-text">正在匹配历史名人...</text>
              </view>

              <!-- 无结果状态 -->
              <view class="no-results" wx:if="{{!historicalVerificationLoading && (!similarCelebrities || similarCelebrities.length === 0)}}">
                <text class="no-results-icon">📚</text>
                <text class="no-results-text">暂未找到相似的历史名人</text>
                <text class="retry-button" bindtap="retryHistoricalVerification">重新匹配</text>
              </view>
            </view>

            <!-- 操作按钮 -->
            <view class="action-buttons">
              <text class="action-button" bindtap="viewAllSimilarCelebrities">查看更多相似名人</text>
              <text class="action-button secondary" bindtap="browseCelebrityDatabase">浏览名人数据库</text>
            </view>
          </view>
        </view>

      </view>

    </scroll-view>

  </view>

</view>
