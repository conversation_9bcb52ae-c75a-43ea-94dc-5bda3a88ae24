/**
 * 调试应期分析完整数据流程
 * 从算法计算到前端显示的完整链路测试
 */

// 模拟完整的数据流程
function simulateCompleteDataFlow() {
  console.log('🧪 ===== 应期分析完整数据流程调试 =====\n');
  
  // 步骤1: 模拟五行能量提取
  const elementEnergies = {
    金: 45.2,
    木: 23.8,
    水: 67.1,
    火: 34.6,
    土: 29.3
  };
  console.log('📋 步骤1 - 五行能量提取:', elementEnergies);
  
  // 步骤2: 模拟阈值配置
  const thresholdConfig = {
    spouse_star_threshold: 0.30,
    official_seal_threshold: 0.50,
    food_injury_threshold: 0.25,
    wealth_star_threshold: 0.30
  };
  console.log('📋 步骤2 - 阈值配置:', thresholdConfig);
  
  // 步骤3: 模拟各个算法计算
  const algorithmResults = {};
  
  // 婚姻算法
  const marriageEnergy = (elementEnergies.金 + elementEnergies.火) * 0.6 + elementEnergies.水 * 0.4;
  algorithmResults.marriage = {
    marriage_energy: {
      actual: marriageEnergy,
      percentage: `${Math.min(marriageEnergy, 100).toFixed(1)}%`,
      required: thresholdConfig.spouse_star_threshold * 100,
      met: marriageEnergy >= thresholdConfig.spouse_star_threshold * 100,
      completion_ratio: `${Math.min(marriageEnergy, 100).toFixed(1)}% / ${(thresholdConfig.spouse_star_threshold * 100).toFixed(1)}%`
    }
  };
  
  // 升职算法
  const promotionEnergy = (elementEnergies.金 + elementEnergies.水) * 0.6 + (elementEnergies.水 + elementEnergies.木) * 0.4;
  algorithmResults.promotion = {
    promotion_energy: {
      actual: promotionEnergy,
      percentage: `${Math.min(promotionEnergy, 100).toFixed(1)}%`,
      required: thresholdConfig.official_seal_threshold * 100,
      met: promotionEnergy >= thresholdConfig.official_seal_threshold * 100,
      completion_ratio: `${Math.min(promotionEnergy, 100).toFixed(1)}% / ${(thresholdConfig.official_seal_threshold * 100).toFixed(1)}%`
    }
  };
  
  console.log('📋 步骤3 - 算法计算结果:');
  console.log('  婚姻:', algorithmResults.marriage);
  console.log('  升职:', algorithmResults.promotion);
  
  // 步骤4: 模拟能量分析结构
  const energyAnalyses = {};
  Object.keys(algorithmResults).forEach(eventType => {
    energyAnalyses[eventType] = {
      event_type: eventType,
      element_energies: elementEnergies,
      threshold_results: algorithmResults[eventType],
      ancient_basis: '《滴天髓》应期理论'
    };
  });
  
  console.log('📋 步骤4 - 能量分析结构:');
  console.log('  婚姻能量分析:', energyAnalyses.marriage);
  
  // 步骤5: 模拟综合应期计算
  const timingPredictions = {};
  Object.keys(energyAnalyses).forEach(eventType => {
    const energyAnalysis = energyAnalyses[eventType];
    const energyKey = Object.keys(energyAnalysis.threshold_results)[0];
    const energyResult = energyAnalysis.threshold_results[energyKey];
    
    if (!energyResult.met) {
      timingPredictions[eventType] = {
        threshold_status: 'not_met',
        message: '当前能量阈值未达标，时机尚未成熟',
        confidence: 0.3,
        energy_deficit: energyResult,
        estimated_year: null
      };
    } else {
      timingPredictions[eventType] = {
        threshold_status: 'met',
        best_year: '2026年3月',
        best_year_numeric: '2026.03',
        confidence: 0.65,
        energy_analysis: energyResult
      };
    }
  });
  
  console.log('📋 步骤5 - 综合应期计算:');
  console.log('  婚姻应期:', timingPredictions.marriage);
  console.log('  升职应期:', timingPredictions.promotion);
  
  // 步骤6: 模拟前端数据提取
  console.log('\n📋 步骤6 - 前端数据提取:');
  
  const thresholds = {
    marriage_current_energy: 0,
    marriage_required_threshold: 0,
    marriage_met: false,
    marriage_estimated_year: '',
    promotion_current_energy: 0,
    promotion_required_threshold: 0,
    promotion_met: false,
    promotion_estimated_year: ''
  };
  
  Object.keys(timingPredictions).forEach(eventType => {
    const result = timingPredictions[eventType];
    console.log(`\n  处理${eventType}数据:`, result);
    
    let userCurrentEnergy = 0;
    let requiredThreshold = 0;
    let actuallyMet = false;
    
    if (result.energy_deficit) {
      console.log(`    发现energy_deficit字段:`, result.energy_deficit);
      userCurrentEnergy = parseFloat(result.energy_deficit.actual) || 0;
      requiredThreshold = parseFloat(result.energy_deficit.required) || 0;
      actuallyMet = result.energy_deficit.met || false;
    } else if (result.energy_analysis) {
      console.log(`    发现energy_analysis字段:`, result.energy_analysis);
      userCurrentEnergy = parseFloat(result.energy_analysis.actual) || 0;
      requiredThreshold = parseFloat(result.energy_analysis.required) || 0;
      actuallyMet = result.energy_analysis.met || false;
    } else {
      console.log(`    ❌ 未找到energy_deficit或energy_analysis字段`);
      console.log(`    可用字段:`, Object.keys(result));
    }
    
    console.log(`    提取结果: 用户${userCurrentEnergy}% / 所需${requiredThreshold}% = ${actuallyMet ? '达标' : '未达标'}`);
    
    thresholds[`${eventType}_current_energy`] = Math.round(userCurrentEnergy * 10) / 10;
    thresholds[`${eventType}_required_threshold`] = Math.round(requiredThreshold * 10) / 10;
    thresholds[`${eventType}_met`] = actuallyMet;
    
    if (result.best_year) {
      thresholds[`${eventType}_estimated_year`] = result.best_year;
    } else if (result.estimated_year) {
      thresholds[`${eventType}_estimated_year`] = result.estimated_year;
    }
  });
  
  console.log('\n📊 最终前端数据:', thresholds);
  
  // 步骤7: 验证数据流程
  console.log('\n🔍 数据流程验证:');
  
  const hasRealData = thresholds.marriage_current_energy > 0 || thresholds.promotion_current_energy > 0;
  console.log('  有真实数据:', hasRealData ? '✅ 成功' : '❌ 失败');
  
  const hasReasonableThresholds = thresholds.marriage_required_threshold <= 100 && thresholds.promotion_required_threshold <= 100;
  console.log('  阈值合理:', hasReasonableThresholds ? '✅ 成功' : '❌ 失败');
  
  const hasEstimatedYears = thresholds.marriage_estimated_year || thresholds.promotion_estimated_year;
  console.log('  有预计年份:', hasEstimatedYears ? '✅ 成功' : '❌ 失败');
  
  const notAllZero = Object.values(thresholds).some(val => typeof val === 'number' && val > 0);
  console.log('  摆脱全0数据:', notAllZero ? '✅ 成功' : '❌ 失败');
  
  console.log('\n🎯 问题诊断:');
  
  if (!hasRealData) {
    console.log('❌ 问题: 前端提取不到真实数据');
    console.log('   可能原因:');
    console.log('   1. 算法返回的数据结构与提取逻辑不匹配');
    console.log('   2. 数据在传递过程中丢失');
    console.log('   3. 字段名不一致');
  }
  
  if (!hasReasonableThresholds) {
    console.log('❌ 问题: 阈值计算错误');
    console.log('   可能原因:');
    console.log('   1. 阈值配置错误');
    console.log('   2. 数值转换错误');
  }
  
  if (!hasEstimatedYears) {
    console.log('❌ 问题: 预计年份缺失');
    console.log('   可能原因:');
    console.log('   1. 年份计算逻辑有问题');
    console.log('   2. 字段名不匹配');
  }
  
  const successCount = [hasRealData, hasReasonableThresholds, hasEstimatedYears, notAllZero].filter(Boolean).length;
  console.log(`\n🎉 数据流程状态: ${successCount}/4 ${successCount >= 3 ? '✅ 正常' : '❌ 异常'}`);
  
  if (successCount >= 3) {
    console.log('\n✅ 数据流程正常！');
    console.log('💡 前端应该能显示真实的个性化数据');
  } else {
    console.log('\n❌ 数据流程有问题！');
    console.log('💡 需要检查数据传递的每个环节');
  }
  
  return {
    success: successCount >= 3,
    thresholds,
    details: {
      hasRealData,
      hasReasonableThresholds,
      hasEstimatedYears,
      notAllZero
    }
  };
}

// 运行调试
simulateCompleteDataFlow();
