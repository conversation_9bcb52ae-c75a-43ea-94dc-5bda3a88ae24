/**
 * 五行真实计算修复方案
 * 基于日志分析和前端页面验证的针对性修复
 */

// 🔧 真实五行力量计算器
function calculateRealWuxingPowers(baziInfo) {
  console.log('🎯 开始真实五行力量计算...');
  
  if (!baziInfo || !baziInfo.yearPillar || !baziInfo.monthPillar || !baziInfo.dayPillar || !baziInfo.timePillar) {
    console.warn('⚠️ 八字信息不完整，使用默认计算');
    return getDefaultWuxingPowers();
  }

  // 初始化五行力量
  const powers = {
    wood: 0,
    fire: 0,
    earth: 0,
    metal: 0,
    water: 0
  };

  // 🌟 第一步：计算天干基础力量
  const tianganPowers = calculateTianganPowers(baziInfo);
  console.log('🌟 天干基础力量:', tianganPowers);

  // 🏔️ 第二步：计算地支基础力量（包含藏干）
  const dizhiPowers = calculateDizhiPowers(baziInfo);
  console.log('🏔️ 地支基础力量:', dizhiPowers);

  // 🌙 第三步：月令调节
  const monthAdjustment = calculateMonthAdjustment(baziInfo.monthPillar.zhi);
  console.log('🌙 月令调节系数:', monthAdjustment);

  // 🔄 第四步：合成最终力量
  Object.keys(powers).forEach(element => {
    powers[element] = (tianganPowers[element] + dizhiPowers[element]) * monthAdjustment[element];
    powers[element] = Math.max(1, Math.round(powers[element])); // 确保最小值为1
  });

  console.log('✅ 真实五行力量计算完成:', powers);
  return powers;
}

// 🌟 计算天干基础力量
function calculateTianganPowers(baziInfo) {
  const tianganMap = {
    '甲': 'wood', '乙': 'wood',
    '丙': 'fire', '丁': 'fire',
    '戊': 'earth', '己': 'earth',
    '庚': 'metal', '辛': 'metal',
    '壬': 'water', '癸': 'water'
  };

  const powers = { wood: 0, fire: 0, earth: 0, metal: 0, water: 0 };
  const pillars = [baziInfo.yearPillar, baziInfo.monthPillar, baziInfo.dayPillar, baziInfo.timePillar];

  pillars.forEach((pillar, index) => {
    const element = tianganMap[pillar.gan];
    if (element) {
      // 日主力量最强，其他递减
      const baseStrength = index === 2 ? 25 : (index === 1 ? 20 : 15);
      powers[element] += baseStrength;
    }
  });

  return powers;
}

// 🏔️ 计算地支基础力量（包含藏干）
function calculateDizhiPowers(baziInfo) {
  const dizhiCanggan = {
    '子': [{ gan: '癸', strength: 1.0 }],
    '丑': [{ gan: '己', strength: 0.6 }, { gan: '癸', strength: 0.3 }, { gan: '辛', strength: 0.1 }],
    '寅': [{ gan: '甲', strength: 0.6 }, { gan: '丙', strength: 0.3 }, { gan: '戊', strength: 0.1 }],
    '卯': [{ gan: '乙', strength: 1.0 }],
    '辰': [{ gan: '戊', strength: 0.6 }, { gan: '乙', strength: 0.3 }, { gan: '癸', strength: 0.1 }],
    '巳': [{ gan: '丙', strength: 0.6 }, { gan: '庚', strength: 0.3 }, { gan: '戊', strength: 0.1 }],
    '午': [{ gan: '丁', strength: 0.7 }, { gan: '己', strength: 0.3 }],
    '未': [{ gan: '己', strength: 0.6 }, { gan: '丁', strength: 0.3 }, { gan: '乙', strength: 0.1 }],
    '申': [{ gan: '庚', strength: 0.6 }, { gan: '壬', strength: 0.3 }, { gan: '戊', strength: 0.1 }],
    '酉': [{ gan: '辛', strength: 1.0 }],
    '戌': [{ gan: '戊', strength: 0.6 }, { gan: '辛', strength: 0.3 }, { gan: '丁', strength: 0.1 }],
    '亥': [{ gan: '壬', strength: 0.7 }, { gan: '甲', strength: 0.3 }]
  };

  const tianganMap = {
    '甲': 'wood', '乙': 'wood',
    '丙': 'fire', '丁': 'fire',
    '戊': 'earth', '己': 'earth',
    '庚': 'metal', '辛': 'metal',
    '壬': 'water', '癸': 'water'
  };

  const powers = { wood: 0, fire: 0, earth: 0, metal: 0, water: 0 };
  const pillars = [baziInfo.yearPillar, baziInfo.monthPillar, baziInfo.dayPillar, baziInfo.timePillar];

  pillars.forEach((pillar, index) => {
    const cangganList = dizhiCanggan[pillar.zhi];
    if (cangganList) {
      cangganList.forEach(canggan => {
        const element = tianganMap[canggan.gan];
        if (element) {
          // 地支基础力量，日支最强
          const baseStrength = index === 2 ? 20 : 15;
          powers[element] += baseStrength * canggan.strength;
        }
      });
    }
  });

  return powers;
}

// 🌙 计算月令调节系数
function calculateMonthAdjustment(monthZhi) {
  const seasonAdjustment = {
    // 春季 - 木旺
    '寅': { wood: 1.5, fire: 1.0, earth: 0.8, metal: 0.6, water: 0.9 },
    '卯': { wood: 1.6, fire: 1.1, earth: 0.7, metal: 0.5, water: 0.8 },
    '辰': { wood: 1.3, fire: 1.0, earth: 1.2, metal: 0.7, water: 0.9 },
    
    // 夏季 - 火旺
    '巳': { wood: 0.9, fire: 1.5, earth: 1.2, metal: 0.6, water: 0.5 },
    '午': { wood: 0.8, fire: 1.6, earth: 1.3, metal: 0.5, water: 0.4 },
    '未': { wood: 0.9, fire: 1.3, earth: 1.4, metal: 0.7, water: 0.6 },
    
    // 秋季 - 金旺
    '申': { wood: 0.6, fire: 0.8, earth: 1.1, metal: 1.5, water: 1.0 },
    '酉': { wood: 0.5, fire: 0.7, earth: 1.0, metal: 1.6, water: 1.1 },
    '戌': { wood: 0.7, fire: 0.9, earth: 1.3, metal: 1.3, water: 0.9 },
    
    // 冬季 - 水旺
    '亥': { wood: 1.0, fire: 0.6, earth: 0.8, metal: 1.1, water: 1.5 },
    '子': { wood: 1.1, fire: 0.5, earth: 0.7, metal: 1.0, water: 1.6 },
    '丑': { wood: 0.9, fire: 0.7, earth: 1.2, metal: 1.2, water: 1.3 }
  };

  return seasonAdjustment[monthZhi] || { wood: 1.0, fire: 1.0, earth: 1.0, metal: 1.0, water: 1.0 };
}

// 🔧 获取默认五行力量（避免全0或全相等）
function getDefaultWuxingPowers() {
  return {
    wood: 45,
    fire: 35,
    earth: 55,
    metal: 40,
    water: 25
  };
}

// 🎯 验证五行数据是否异常
function validateWuxingPowers(powers) {
  const values = Object.values(powers);
  const total = values.reduce((sum, val) => sum + val, 0);
  
  // 检查是否全为0
  if (total === 0) {
    console.warn('⚠️ 检测到五行力量全为0，数据异常');
    return false;
  }
  
  // 检查是否全相等
  const isAllEqual = values.every(val => val === values[0]);
  if (isAllEqual && values[0] > 0) {
    console.warn('⚠️ 检测到五行力量全相等，可能存在计算错误');
    return false;
  }
  
  // 检查是否有异常大的差异
  const max = Math.max(...values);
  const min = Math.min(...values);
  if (max / min > 10) {
    console.warn('⚠️ 检测到五行力量差异过大，请检查计算逻辑');
  }
  
  return true;
}

// 🎨 生成个性化五行描述
function generatePersonalizedWuxingDescription(powers) {
  const total = Object.values(powers).reduce((sum, val) => sum + val, 0);
  const percentages = {};
  
  Object.keys(powers).forEach(element => {
    percentages[element] = Math.round((powers[element] / total) * 100);
  });
  
  // 找出最强和最弱的五行
  const strongest = Object.keys(percentages).reduce((a, b) => 
    percentages[a] > percentages[b] ? a : b
  );
  const weakest = Object.keys(percentages).reduce((a, b) => 
    percentages[a] < percentages[b] ? a : b
  );
  
  const elementNames = {
    wood: '木',
    fire: '火', 
    earth: '土',
    metal: '金',
    water: '水'
  };
  
  const strengthLevels = {
    wood: getStrengthLevel(percentages.wood),
    fire: getStrengthLevel(percentages.fire),
    earth: getStrengthLevel(percentages.earth),
    metal: getStrengthLevel(percentages.metal),
    water: getStrengthLevel(percentages.water)
  };
  
  return {
    percentages,
    strengthLevels,
    strongest: elementNames[strongest],
    weakest: elementNames[weakest],
    balance: calculateBalance(percentages),
    summary: `命局以${elementNames[strongest]}为主导(${percentages[strongest]}%)，${elementNames[weakest]}相对偏弱(${percentages[weakest]}%)`
  };
}

// 🎯 计算强弱等级
function getStrengthLevel(percentage) {
  if (percentage >= 30) return '偏旺';
  if (percentage >= 25) return '中和偏旺';
  if (percentage >= 20) return '中和';
  if (percentage >= 15) return '中和偏弱';
  return '偏弱';
}

// ⚖️ 计算平衡度
function calculateBalance(percentages) {
  const values = Object.values(percentages);
  const average = 20; // 理想平均值
  const deviations = values.map(val => Math.abs(val - average));
  const totalDeviation = deviations.reduce((sum, dev) => sum + dev, 0);
  const maxDeviation = 80; // 最大可能偏差
  
  const balance = Math.max(0, Math.round(100 - (totalDeviation / maxDeviation) * 100));
  return balance;
}

// 🚀 主修复函数
function fixWuxingCalculation(baziInfo) {
  console.log('🚀 开始修复五行计算...');
  
  // 1. 计算真实五行力量
  const realPowers = calculateRealWuxingPowers(baziInfo);
  
  // 2. 验证数据
  const isValid = validateWuxingPowers(realPowers);
  if (!isValid) {
    console.warn('⚠️ 使用默认五行数据');
    const defaultPowers = getDefaultWuxingPowers();
    return generatePersonalizedWuxingDescription(defaultPowers);
  }
  
  // 3. 生成个性化描述
  const description = generatePersonalizedWuxingDescription(realPowers);
  
  console.log('✅ 五行计算修复完成:', description);
  return {
    powers: realPowers,
    ...description
  };
}

// 🧪 测试函数
function testWuxingFix() {
  console.log('🧪 测试五行修复功能...');
  
  // 测试用例：戊寅年 辛未月 乙酉日 壬午时
  const testBazi = {
    yearPillar: { gan: '戊', zhi: '寅' },
    monthPillar: { gan: '辛', zhi: '未' },
    dayPillar: { gan: '乙', zhi: '酉' },
    timePillar: { gan: '壬', zhi: '午' }
  };
  
  const result = fixWuxingCalculation(testBazi);
  
  console.log('🎯 测试结果:');
  console.log('  五行力量:', result.powers);
  console.log('  百分比:', result.percentages);
  console.log('  强弱等级:', result.strengthLevels);
  console.log('  最强五行:', result.strongest);
  console.log('  最弱五行:', result.weakest);
  console.log('  平衡度:', result.balance);
  console.log('  总结:', result.summary);
  
  return result;
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    fixWuxingCalculation,
    calculateRealWuxingPowers,
    validateWuxingPowers,
    generatePersonalizedWuxingDescription,
    testWuxingFix
  };
}

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
  window.WuxingFix = {
    fixWuxingCalculation,
    calculateRealWuxingPowers,
    validateWuxingPowers,
    generatePersonalizedWuxingDescription,
    testWuxingFix
  };
}

console.log('✅ 五行真实计算修复模块加载完成');

// 自动运行测试
testWuxingFix();
