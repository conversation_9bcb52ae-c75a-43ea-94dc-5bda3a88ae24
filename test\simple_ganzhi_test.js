// test/simple_ganzhi_test.js
// 简单测试干支计算

const ProfessionalTimingEngine = require('../utils/professional_timing_engine.js');

const engine = new ProfessionalTimingEngine();

console.log('🔍 测试干支计算...');

// 测试几个已知年份
const testYears = [
  { year: 2023, expected: '癸卯' },
  { year: 1999, expected: '己亥' },
  { year: 2009, expected: '己丑' }, // 修正：2009年应该是己丑年
  { year: 1984, expected: '甲子' },
  { year: 2024, expected: '甲辰' }
];

testYears.forEach(test => {
  const calculated = engine.getYearGanZhi(test.year);
  console.log(`${test.year}年: 计算=${calculated}, 期望=${test.expected}, 匹配=${calculated === test.expected ? '✅' : '❌'}`);
});

// 测试年龄推算
console.log('\n🔍 测试年龄推算...');

const testBazi = {
  year_pillar: { heavenly: '癸', earthly: '卯' }
};

const extractedYear = engine.extractBirthYear(testBazi);
console.log(`推算年份: ${extractedYear}年`);

// 手动验证
console.log('\n🔍 手动验证干支推算...');
const currentYear = new Date().getFullYear();
const inputGanZhi = '癸卯';

for (let year = currentYear; year >= currentYear - 10; year--) {
  const yearGanZhi = engine.getYearGanZhi(year);
  console.log(`${year}年: ${yearGanZhi} ${yearGanZhi === inputGanZhi ? '✅ 匹配' : ''}`);
}
