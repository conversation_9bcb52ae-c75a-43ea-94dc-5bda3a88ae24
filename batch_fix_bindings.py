#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def batch_fix_data_bindings():
    """批量修复WXML文件中的数据绑定问题"""
    
    print("🔧 开始批量修复数据绑定...")
    
    # 读取文件
    with open('pages/bazi-result/index.wxml', 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 定义替换规则
    replacements = [
        # 基本的unifiedData替换
        (r'unifiedData\.([a-zA-Z_][a-zA-Z0-9_]*)', r'baziData.\1'),
        
        # 特殊的四柱数据结构修复
        (r'baziData\.year_pillar\.heavenly_stem', 'baziData.year_gan || baziData.baziInfo.yearPillar.heavenly'),
        (r'baziData\.year_pillar\.earthly_branch', 'baziData.year_zhi || baziData.baziInfo.yearPillar.earthly'),
        (r'baziData\.month_pillar\.heavenly_stem', 'baziData.month_gan || baziData.baziInfo.monthPillar.heavenly'),
        (r'baziData\.month_pillar\.earthly_branch', 'baziData.month_zhi || baziData.baziInfo.monthPillar.earthly'),
        (r'baziData\.day_pillar\.heavenly_stem', 'baziData.day_gan || baziData.baziInfo.dayPillar.heavenly'),
        (r'baziData\.day_pillar\.earthly_branch', 'baziData.day_zhi || baziData.baziInfo.dayPillar.earthly'),
        (r'baziData\.hour_pillar\.heavenly_stem', 'baziData.hour_gan || baziData.baziInfo.timePillar.heavenly'),
        (r'baziData\.hour_pillar\.earthly_branch', 'baziData.hour_zhi || baziData.baziInfo.timePillar.earthly'),
        
        # 纳音数据修复
        (r'baziData\.year_pillar\.nayin', 'baziData.nayin.year_pillar || baziData.baziInfo.yearPillar.nayin'),
        (r'baziData\.month_pillar\.nayin', 'baziData.nayin.month_pillar || baziData.baziInfo.monthPillar.nayin'),
        (r'baziData\.day_pillar\.nayin', 'baziData.nayin.day_pillar || baziData.baziInfo.dayPillar.nayin'),
        (r'baziData\.hour_pillar\.nayin', 'baziData.nayin.hour_pillar || baziData.baziInfo.timePillar.nayin'),
        
        # 藏干数据修复
        (r'baziData\.year_pillar\.hidden_stems', 'baziData.year_canggan || "计算中"'),
        (r'baziData\.month_pillar\.hidden_stems', 'baziData.month_canggan || "计算中"'),
        (r'baziData\.day_pillar\.hidden_stems', 'baziData.day_canggan || "计算中"'),
        (r'baziData\.hour_pillar\.hidden_stems', 'baziData.hour_canggan || "计算中"'),
        
        # 其他常见数据结构修复
        (r'baziData\.birth_date', 'baziData.birthDate || baziData.userInfo.birthDate'),
        (r'baziData\.birth_time', 'baziData.birthTime || baziData.userInfo.birthTime'),
        (r'baziData\.birth_location', 'baziData.location || baziData.userInfo.location'),
        (r'baziData\.lunar_date', 'baziData.lunar_time || baziData.userInfo.lunar_time'),
        
        # 五行分析数据修复
        (r'baziData\.wuxing_analysis', 'baziData.wuxing_analysis || baziData.fiveElements'),
        (r'baziData\.wuxing_summary', 'baziData.wuxing_summary || "五行分析计算中..."'),
        
        # 神煞数据修复
        (r'baziData\.auspicious_stars', 'baziData.auspiciousStars'),
        (r'baziData\.inauspicious_stars', 'baziData.inauspiciousStars'),
        
        # 大运流年数据修复
        (r'baziData\.current_dayun', 'baziData.currentDayun || professionalDayunData.currentDayun'),
        (r'baziData\.liunian_list', 'baziData.liunianList || professionalLiunianData.liunianList'),
        (r'baziData\.dayun_overview', 'baziData.dayunOverview || professionalDayunData.dayunSequence'),
    ]
    
    # 应用替换
    changes_made = 0
    for pattern, replacement in replacements:
        new_content = re.sub(pattern, replacement, content)
        if new_content != content:
            changes_count = len(re.findall(pattern, content))
            print(f"✅ 替换 '{pattern}' -> '{replacement}' ({changes_count} 处)")
            changes_made += changes_count
            content = new_content
    
    # 写回文件
    if content != original_content:
        with open('pages/bazi-result/index.wxml', 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"\n🎉 批量修复完成！总共修复了 {changes_made} 处数据绑定")
    else:
        print("\n📝 没有发现需要修复的数据绑定")
    
    return changes_made

def verify_fixes():
    """验证修复结果"""
    print("\n🔍 验证修复结果...")
    
    with open('pages/bazi-result/index.wxml', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否还有unifiedData绑定
    remaining_unified = re.findall(r'unifiedData\.[a-zA-Z_][a-zA-Z0-9_]*', content)
    if remaining_unified:
        print(f"⚠️ 仍有 {len(remaining_unified)} 个 unifiedData 绑定未修复:")
        for binding in set(remaining_unified):
            print(f"  - {binding}")
    else:
        print("✅ 所有 unifiedData 绑定已修复")
    
    # 检查数据绑定总数
    all_bindings = re.findall(r'\{\{([^}]+)\}\}', content)
    print(f"📊 当前总数据绑定: {len(all_bindings)} 个")
    
    # 分类统计
    binding_types = {
        'baziData': len([b for b in all_bindings if 'baziData' in b]),
        'professionalAnalysis': len([b for b in all_bindings if 'professionalAnalysis' in b]),
        'timingAnalysis': len([b for b in all_bindings if 'timingAnalysis' in b]),
        'liuqinAnalysis': len([b for b in all_bindings if 'liuqinAnalysis' in b]),
        'classicalAnalysis': len([b for b in all_bindings if 'classicalAnalysis' in b]),
        'pageState': len([b for b in all_bindings if 'pageState' in b]),
        'currentTab': len([b for b in all_bindings if 'currentTab' in b]),
        'item': len([b for b in all_bindings if 'item.' in b]),
    }
    
    print("\n📋 数据绑定分类统计:")
    for binding_type, count in binding_types.items():
        if count > 0:
            print(f"  {binding_type}: {count} 个")

if __name__ == "__main__":
    changes = batch_fix_data_bindings()
    verify_fixes()
    
    if changes > 0:
        print(f"\n🚀 建议下一步:")
        print(f"1. 测试页面加载和数据显示")
        print(f"2. 检查控制台是否有数据绑定错误")
        print(f"3. 验证所有tab页面的功能")
        print(f"4. 确认用户交互功能正常")
