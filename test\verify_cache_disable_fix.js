/**
 * 验证缓存禁用修复效果
 * 测试专业级引擎是否真的禁用了缓存
 */

// 清除Node.js模块缓存，确保加载最新的代码
function clearModuleCache() {
  const moduleKeys = Object.keys(require.cache);
  moduleKeys.forEach(key => {
    if (key.includes('professional_wuxing_engine') || 
        key.includes('unified_wuxing_calculator')) {
      delete require.cache[key];
      console.log('🗑️ 清除模块缓存:', key);
    }
  });
}

// 清除模块缓存
clearModuleCache();

// 重新加载模块
const ProfessionalWuxingEngine = require('../utils/professional_wuxing_engine.js');
const UnifiedWuxingCalculator = require('../utils/unified_wuxing_calculator_safe.js');

function testCacheDisableFix() {
  console.log('🧪 ===== 缓存禁用修复验证测试 =====\n');
  
  // 测试用例：乙亥年 辛巳月 庚子日 壬午时
  const testBazi = {
    year: { gan: '乙', zhi: '亥' },
    month: { gan: '辛', zhi: '巳' },
    day: { gan: '庚', zhi: '子' },
    hour: { gan: '壬', zhi: '午' }
  };
  
  const fourPillars = [
    { gan: '乙', zhi: '亥' },
    { gan: '辛', zhi: '巳' },
    { gan: '庚', zhi: '子' },
    { gan: '壬', zhi: '午' }
  ];
  
  console.log('📋 测试八字:', testBazi);
  console.log('   完整八字: 乙亥年 辛巳月 庚子日 壬午时\n');
  
  console.log('🧪 测试1: 直接测试专业级引擎');
  const engine = new ProfessionalWuxingEngine();
  
  // 检查缓存配置
  console.log('   缓存配置:', engine.cacheConfig);
  console.log('   缓存启用:', engine.cacheConfig.enableCache);
  
  // 第一次计算
  console.log('\n   第一次计算:');
  const result1 = engine.generateDetailedReport(fourPillars);
  
  // 第二次计算
  console.log('\n   第二次计算:');
  const result2 = engine.generateDetailedReport(fourPillars);
  
  // 检查缓存统计
  const stats = engine.getCacheStats();
  console.log('\n   缓存统计:', stats);
  
  console.log('\n🧪 测试2: 通过统一计算器测试');
  
  // 第一次通过统一计算器
  console.log('\n   第一次统一计算:');
  const unifiedResult1 = UnifiedWuxingCalculator.calculate(testBazi);
  
  // 第二次通过统一计算器
  console.log('\n   第二次统一计算:');
  const unifiedResult2 = UnifiedWuxingCalculator.calculate(testBazi);
  
  console.log('\n📊 测试结果分析:');
  
  // 分析专业级引擎结果
  console.log('   专业级引擎结果1:', {
    wood: result1.results?.finalPowers?.木 || 'N/A',
    fire: result1.results?.finalPowers?.火 || 'N/A',
    earth: result1.results?.finalPowers?.土 || 'N/A',
    metal: result1.results?.finalPowers?.金 || 'N/A',
    water: result1.results?.finalPowers?.水 || 'N/A'
  });
  
  console.log('   专业级引擎结果2:', {
    wood: result2.results?.finalPowers?.木 || 'N/A',
    fire: result2.results?.finalPowers?.火 || 'N/A',
    earth: result2.results?.finalPowers?.土 || 'N/A',
    metal: result2.results?.finalPowers?.金 || 'N/A',
    water: result2.results?.finalPowers?.水 || 'N/A'
  });
  
  // 分析统一计算器结果
  console.log('   统一计算器结果1:', {
    wood: unifiedResult1.wood,
    fire: unifiedResult1.fire,
    earth: unifiedResult1.earth,
    metal: unifiedResult1.metal,
    water: unifiedResult1.water
  });
  
  console.log('   统一计算器结果2:', {
    wood: unifiedResult2.wood,
    fire: unifiedResult2.fire,
    earth: unifiedResult2.earth,
    metal: unifiedResult2.metal,
    water: unifiedResult2.water
  });
  
  console.log('\n🎯 修复验证:');
  
  // 验证1: 缓存是否被禁用
  const cacheDisabled = !engine.cacheConfig.enableCache;
  console.log('   缓存禁用:', cacheDisabled ? '✅ 成功' : '❌ 失败');
  
  // 验证2: 缓存命中次数应该为0
  const noCacheHits = stats.hits === 0;
  console.log('   无缓存命中:', noCacheHits ? '✅ 成功' : '❌ 失败');
  
  // 验证3: 统一计算器结果不应该是50平均值
  const notFixedFifty = !(unifiedResult1.wood === 50 && unifiedResult1.fire === 50 && 
                          unifiedResult1.earth === 50 && unifiedResult1.metal === 50 && 
                          unifiedResult1.water === 50);
  console.log('   摆脱固定50:', notFixedFifty ? '✅ 成功' : '❌ 失败');
  
  // 验证4: 结果应该是个性化的
  const values = [unifiedResult1.wood, unifiedResult1.fire, unifiedResult1.earth, 
                  unifiedResult1.metal, unifiedResult1.water];
  const isPersonalized = !values.every(v => v === values[0]);
  console.log('   结果个性化:', isPersonalized ? '✅ 成功' : '❌ 失败');
  
  // 验证5: 两次计算结果应该一致（因为输入相同）
  const resultsConsistent = (unifiedResult1.wood === unifiedResult2.wood && 
                            unifiedResult1.fire === unifiedResult2.fire &&
                            unifiedResult1.earth === unifiedResult2.earth &&
                            unifiedResult1.metal === unifiedResult2.metal &&
                            unifiedResult1.water === unifiedResult2.water);
  console.log('   结果一致性:', resultsConsistent ? '✅ 成功' : '❌ 失败');
  
  console.log('\n🎉 修复效果总结:');
  const successCount = [cacheDisabled, noCacheHits, notFixedFifty, isPersonalized, resultsConsistent].filter(Boolean).length;
  console.log(`   成功项目: ${successCount}/5`);
  console.log(`   修复状态: ${successCount >= 4 ? '✅ 修复成功' : '❌ 需要进一步修复'}`);
  
  if (successCount >= 4) {
    console.log('\n✅ 缓存禁用修复成功！');
    console.log('💡 现在应该显示：');
    console.log('   - 每次都重新计算，不使用缓存');
    console.log('   - 个性化的五行分布');
    console.log('   - 不再是固定的50平均值');
    console.log('   - 基于真实八字的计算结果');
  } else {
    console.log('\n❌ 修复不完整，需要进一步调试');
    console.log('💡 可能的问题：');
    console.log('   - 模块缓存导致修改未生效');
    console.log('   - 还有其他缓存机制');
    console.log('   - 计算逻辑本身有问题');
  }
  
  return {
    success: successCount >= 4,
    details: {
      cacheDisabled,
      noCacheHits,
      notFixedFifty,
      isPersonalized,
      resultsConsistent
    },
    results: {
      unified1: unifiedResult1,
      unified2: unifiedResult2,
      engine1: result1,
      engine2: result2
    }
  };
}

// 运行测试
testCacheDisableFix();
