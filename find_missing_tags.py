#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def find_missing_tags(file_path):
    """找到缺失的结束标签"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    # 使用栈来跟踪标签
    view_stack = []
    
    for line_num, line in enumerate(lines, 1):
        # 查找view标签（忽略注释）
        if '<!--' in line and '-->' in line:
            # 移除单行注释
            line = re.sub(r'<!--.*?-->', '', line)
        
        view_opens = re.findall(r'<view(?:\s[^>]*)?(?<!/)>', line)
        view_closes = re.findall(r'</view>', line)
        
        # 处理开始标签
        for _ in view_opens:
            view_stack.append(line_num)
        
        # 处理结束标签
        for _ in view_closes:
            if view_stack:
                view_stack.pop()
    
    print(f"🔍 分析结果:")
    print(f"  未闭合的 <view> 标签数量: {len(view_stack)}")
    
    if view_stack:
        print(f"  未闭合标签的开始行号: {view_stack}")
        
        # 显示前10个未闭合标签的内容
        print(f"\n📋 前10个未闭合标签的内容:")
        for i, line_num in enumerate(view_stack[:10]):
            if line_num <= len(lines):
                print(f"  第{line_num}行: {lines[line_num-1].strip()}")
    
    return view_stack

if __name__ == "__main__":
    find_missing_tags("pages/bazi-result/index.wxml")
