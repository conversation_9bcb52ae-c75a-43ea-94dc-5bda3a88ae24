#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import json
from collections import defaultdict

def comprehensive_data_validation():
    """全面验证八字结果页面的数据显示功能"""
    
    print("🔍 八字结果页面数据显示全面验证")
    print("=" * 60)
    
    # 读取WXML文件
    with open('pages/bazi-result/index.wxml', 'r', encoding='utf-8') as f:
        wxml_content = f.read()
    
    # 根据JavaScript分析，定义正确的数据结构
    js_data_structure = {
        # 页面状态数据
        'pageState': {
            'loading': 'boolean',
            'progress': 'number',
            'loadingTip': 'string',
            'error': 'boolean',
            'errorMessage': 'string'
        },
        
        # 当前标签页
        'currentTab': 'string',
        
        # 主要数据对象
        'baziData': {
            # 用户信息 (通过 baziData.userInfo 访问)
            'userInfo': {
                'name': 'string',
                'gender': 'string',
                'birthDate': 'string',
                'birthTime': 'string',
                'location': 'string',
                'zodiac': 'string',
                'solar_time': 'string',
                'lunar_time': 'string',
                'true_solar_time': 'string',
                'longitude': 'string',
                'latitude': 'string',
                'timezone': 'string',
                'solar_term': 'string'
            },
            
            # 八字信息 (通过 baziData.baziInfo 访问)
            'baziInfo': {
                'yearPillar': {'heavenly': 'string', 'earthly': 'string', 'nayin': 'string'},
                'monthPillar': {'heavenly': 'string', 'earthly': 'string', 'nayin': 'string'},
                'dayPillar': {'heavenly': 'string', 'earthly': 'string', 'nayin': 'string'},
                'timePillar': {'heavenly': 'string', 'earthly': 'string', 'nayin': 'string'}
            },
            
            # 五行数据 (通过 baziData.fiveElements 访问)
            'fiveElements': {
                'wood': 'number',
                'fire': 'number', 
                'earth': 'number',
                'metal': 'number',
                'water': 'number'
            },
            
            # 直接字段
            'year_gan': 'string',
            'month_gan': 'string',
            'day_gan': 'string',
            'hour_gan': 'string',
            'year_zhi': 'string',
            'month_zhi': 'string',
            'day_zhi': 'string',
            'hour_zhi': 'string',
            
            # 扩展数据
            'balanceIndex': 'number',
            'balanceStatus': 'string',
            'birth_solar_term': 'string',
            'kong_wang': 'string',
            'ming_gua': 'string',
            'constellation': 'string',
            'star_mansion': 'string',
            'auxiliary_stars': 'array',
            'shen_sha': 'array',
            'classical_analysis': 'string',
            'jieqiInfo': 'string'
        },
        
        # 分析数据对象
        'professionalAnalysis': {
            'layeredResults': {
                'confidence': 'number',
                'confidencePercent': 'string',
                'totalMatched': 'number'
            },
            'enhancedRules': 'array',
            'wuxingScores': {
                'wood': 'number',
                'fire': 'number',
                'earth': 'number',
                'metal': 'number',
                'water': 'number'
            }
        },
        
        'timingAnalysis': {
            'career': 'object',
            'wealth': 'object',
            'marriage': 'object',
            'health': 'object'
        },
        
        'liuqinAnalysis': {
            'spouse': {
                'characteristics': 'string',
                'relationship': 'string',
                'marriage_timing': 'string'
            }
        },
        
        'classicalAnalysis': {
            'quotes': 'array',
            'personality_judgment': 'string',
            'career_judgment': 'string'
        }
    }
    
    # 提取所有数据绑定
    bindings = re.findall(r'\{\{([^}]+)\}\}', wxml_content)
    
    print(f"📊 总数据绑定: {len(bindings)} 个")
    
    # 分析每个tab页面
    tab_pages = [
        'basic', 'paipan', 'advanced', 'fortune', 
        'professional', 'classical', 'timing', 'liuqin'
    ]
    
    validation_results = {}
    
    for tab in tab_pages:
        print(f"\n🔍 验证 {tab} 页面...")
        tab_results = validate_tab_page(wxml_content, tab, js_data_structure)
        validation_results[tab] = tab_results
    
    # 生成验证报告
    generate_validation_report(validation_results)
    
    return validation_results

def validate_tab_page(wxml_content, tab_name, js_structure):
    """验证单个tab页面的数据绑定"""

    # 提取该tab页面的内容
    tab_pattern = r"wx:(?:if|elif)=\"{{currentTab === '" + tab_name + r"'}}\".*?(?=<view wx:(?:elif|if)=\"{{currentTab === '|</view>\s*</scroll-view>)"
    tab_match = re.search(tab_pattern, wxml_content, re.DOTALL)
    
    if not tab_match:
        return {
            'found': False,
            'issues': [f"未找到 {tab_name} 页面内容"],
            'bindings': [],
            'cards': []
        }
    
    tab_content = tab_match.group(0)
    
    # 提取该页面的数据绑定
    tab_bindings = re.findall(r'\{\{([^}]+)\}\}', tab_content)
    
    # 分析卡片组件
    card_pattern = r'<view class="tianggong-card[^"]*"[^>]*>(.*?)</view>\s*</view>'
    cards = re.findall(card_pattern, tab_content, re.DOTALL)
    
    issues = []
    
    # 验证数据绑定
    for binding in tab_bindings:
        binding = binding.strip()
        
        # 跳过条件表达式和简单变量
        if any(op in binding for op in ['?', '===', '!==', '||', '&&', 'item.']):
            continue
            
        if binding == 'currentTab':
            continue
        
        # 检查数据绑定是否有效
        binding_issue = validate_data_binding(binding, js_structure)
        if binding_issue:
            issues.append({
                'type': 'data_binding',
                'binding': binding,
                'issue': binding_issue,
                'tab': tab_name
            })
    
    return {
        'found': True,
        'bindings_count': len(tab_bindings),
        'cards_count': len(cards),
        'issues': issues,
        'bindings': tab_bindings
    }

def validate_data_binding(binding, js_structure):
    """验证单个数据绑定的有效性"""
    
    # 检查是否使用了已废弃的 unifiedData
    if binding.startswith('unifiedData.'):
        return f"使用了已废弃的 unifiedData 前缀，应改为 baziData"
    
    # 检查直接访问应该通过 baziData 访问的字段
    direct_access_fields = ['userInfo.', 'baziInfo.', 'birthInfo.', 'fiveElements.']
    for field in direct_access_fields:
        if binding.startswith(field):
            return f"应通过 baziData.{field} 访问，而不是直接访问 {field}"
    
    # 检查 baziData 字段是否存在
    if binding.startswith('baziData.'):
        field_path = binding[9:]  # 去掉 'baziData.' 前缀
        
        # 检查嵌套字段
        if '.' in field_path:
            parts = field_path.split('.')
            current_structure = js_structure.get('baziData', {})
            
            for part in parts:
                if isinstance(current_structure, dict) and part in current_structure:
                    current_structure = current_structure[part]
                else:
                    return f"字段路径 baziData.{field_path} 不存在"
        else:
            # 检查直接字段
            if field_path not in js_structure.get('baziData', {}):
                return f"字段 baziData.{field_path} 不存在"
    
    # 检查其他分析数据
    analysis_fields = ['professionalAnalysis', 'timingAnalysis', 'liuqinAnalysis', 'classicalAnalysis']
    for field in analysis_fields:
        if binding.startswith(f'{field}.'):
            # 这些字段应该存在，暂时不报错
            pass
    
    return None

def generate_validation_report(validation_results):
    """生成验证报告"""
    
    print(f"\n📋 验证报告总结")
    print("=" * 50)
    
    total_issues = 0
    critical_issues = []
    
    for tab_name, results in validation_results.items():
        if not results['found']:
            critical_issues.append(f"❌ {tab_name} 页面未找到")
            continue
            
        issues_count = len(results['issues'])
        total_issues += issues_count
        
        status = "✅" if issues_count == 0 else "⚠️"
        print(f"{status} {tab_name} 页面: {results['bindings_count']} 个绑定, {results['cards_count']} 个卡片, {issues_count} 个问题")
        
        # 显示具体问题
        for issue in results['issues']:
            if issue['type'] == 'data_binding':
                print(f"    ❌ 数据绑定问题: {issue['binding']}")
                print(f"       原因: {issue['issue']}")
    
    print(f"\n🎯 总体评估:")
    print(f"  总问题数: {total_issues}")
    print(f"  关键问题: {len(critical_issues)}")
    
    if critical_issues:
        print(f"\n❌ 关键问题:")
        for issue in critical_issues:
            print(f"  {issue}")
    
    if total_issues == 0:
        print(f"\n🎉 验证通过！所有数据绑定都正确。")
        print(f"📋 建议测试:")
        print(f"  1. 在微信开发者工具中编译项目")
        print(f"  2. 测试每个tab页面的数据显示")
        print(f"  3. 验证交互功能正常")
    else:
        print(f"\n⚠️ 发现 {total_issues} 个问题需要修复")

if __name__ == "__main__":
    results = comprehensive_data_validation()
