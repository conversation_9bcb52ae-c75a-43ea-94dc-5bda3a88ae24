/**
 * 动态分析引擎数据修复验证测试
 * 验证前端能否正确提取和显示动态分析数据
 */

const fs = require('fs');
const path = require('path');

function testDynamicAnalysisDataExtraction() {
  console.log('🔍 动态分析引擎数据修复验证测试\n');
  
  try {
    // 模拟日志中的实际数据结构
    const mockTimingAnalysisData = {
      childbirth: {
        threshold_status: "met",
        best_year: "2027年5月",
        best_year_numeric: "2027.05",
        timing_basis: "undefined，[object Object]",
        energy_analysis: {
          actual: 28.709999999999997,
          percentage: "28.7%",
          required: 25,
          met: true,
          completion_ratio: "28.7% / 25.0%"
        },
        activation_analysis: {
          star_activation: {
            triggered: true,
            description: "食神、伤官星透干，星动有力",
            strength: 0.85,
            ancient_basis: "《三命通会》：十神透干，其力倍增"
          },
          palace_activation: {
            triggered: false,
            description: "巳戌无明显地支作用关系",
            combination_type: "无明显作用",
            strength: 0.1
          },
          god_activation: {
            triggered: false,
            active_gods: [],
            description: "相关神煞未激活",
            strength: 0
          }
        },
        dynamic_analysis: {
          three_point_analysis: {
            ancient_basis: "《滴天髓》：三点一线，应期立现"
          },
          spacetime_force: {
            formula_used: "初始值 × e^(-0.1×运程年数)",
            ancient_basis: "《渊海子平》：大运流年，时空作用"
          },
          turning_points: {},
          energy_connection: {
            is_connected: false,
            connection_type: "微弱能量联系",
            connection_strength: 0.44999999999999996,
            pathway_description: "病神(0.50) → 药神(0.50) → 引动(0.50)"
          },
          overall_dynamic_score: 0.47500000000000003,
          calculation_method: "《滴天髓·应期章》动态分析引擎"
        }
      },
      marriage: {
        threshold_status: "not_met",
        message: "当前能量阈值未达标，时机尚未成熟",
        confidence: 0.3,
        energy_deficit: {
          actual: 23.64,
          percentage: "23.6%",
          required: 30,
          met: false,
          completion_ratio: "23.6% / 30.0%"
        },
        estimated_year: null
      }
    };

    console.log('📋 动态分析数据提取测试:\n');

    // 模拟修复后的数据提取逻辑
    function extractActualDynamicAnalysisData(professionalResults) {
      console.log('🔍 开始提取动态分析数据...');
      
      // 寻找包含dynamic_analysis数据的事件
      let bestDynamicAnalysis = null;
      let bestEventType = null;
      
      Object.keys(professionalResults).forEach(eventType => {
        const result = professionalResults[eventType];
        if (result && result.dynamic_analysis) {
          console.log(`🔍 发现${eventType}的动态分析数据:`, result.dynamic_analysis);
          bestDynamicAnalysis = result.dynamic_analysis;
          bestEventType = eventType;
        }
      });

      if (bestDynamicAnalysis) {
        console.log(`✅ 使用${bestEventType}的动态分析数据`);
        return formatDynamicAnalysisForUI(bestDynamicAnalysis);
      } else {
        console.warn('⚠️ 未找到动态分析数据，使用默认值');
        return getDefaultDynamicAnalysis();
      }
    }

    function formatDynamicAnalysisForUI(dynamicAnalysis) {
      const result = {};

      // 1. 三点一线法则
      if (dynamicAnalysis.three_point_analysis) {
        const threePoint = dynamicAnalysis.three_point_analysis;
        if (threePoint.ancient_basis) {
          result.three_point_rule = `${threePoint.ancient_basis}`;
          if (threePoint.connection_strength !== undefined) {
            const percentage = (threePoint.connection_strength * 100).toFixed(1);
            result.three_point_rule += ` (连线强度: ${percentage}%)`;
          }
        } else {
          result.three_point_rule = '三点一线法则检测中，暂未发现明显连线';
        }
      } else {
        result.three_point_rule = '三点一线法则检测中，暂未发现明显连线';
      }

      // 2. 时空力量
      if (dynamicAnalysis.spacetime_force) {
        const spacetime = dynamicAnalysis.spacetime_force;
        let spacetimeText = '';
        
        if (spacetime.formula_used) {
          spacetimeText += `计算公式: ${spacetime.formula_used}`;
        }
        if (spacetime.ancient_basis) {
          spacetimeText += spacetimeText ? ` | ${spacetime.ancient_basis}` : spacetime.ancient_basis;
        }
        
        // 🔧 修复：从overall_dynamic_score提取时空力量数值
        if (dynamicAnalysis.overall_dynamic_score !== undefined) {
          const percentage = (dynamicAnalysis.overall_dynamic_score * 100).toFixed(1);
          spacetimeText += ` (强度: ${percentage}%)`;
        }
        
        result.spacetime_force = spacetimeText || '时空力量计算中...';
      } else {
        result.spacetime_force = '时空力量计算中...';
      }

      // 3. 转折点识别
      if (dynamicAnalysis.turning_points && Object.keys(dynamicAnalysis.turning_points).length > 0) {
        const turningPoints = dynamicAnalysis.turning_points;
        result.turning_points = `检测到转折点: ${Object.keys(turningPoints).join(', ')}`;
      } else {
        result.turning_points = '未来3年内暂无明显转折点';
      }

      console.log('🎯 格式化后的动态分析数据:', result);
      return result;
    }

    function getDefaultDynamicAnalysis() {
      return {
        three_point_rule: '三点一线法则检测中，暂未发现明显连线',
        spacetime_force: '时空力量计算中...',
        turning_points: '未来3年内暂无明显转折点'
      };
    }

    // 执行数据提取测试
    const extractedData = extractActualDynamicAnalysisData(mockTimingAnalysisData);

    console.log('\n📊 提取结果验证:\n');

    // 验证三点一线法则
    const threePointCorrect = extractedData.three_point_rule && 
                             extractedData.three_point_rule.includes('《滴天髓》：三点一线，应期立现');
    console.log(`🔗 三点一线法则: ${threePointCorrect ? '✅' : '❌'} ${extractedData.three_point_rule}`);

    // 验证时空力量
    const spacetimeCorrect = extractedData.spacetime_force && 
                            extractedData.spacetime_force.includes('初始值 × e^(-0.1×运程年数)') &&
                            extractedData.spacetime_force.includes('47.5%');
    console.log(`⚡ 时空力量: ${spacetimeCorrect ? '✅' : '❌'} ${extractedData.spacetime_force}`);

    // 验证转折点识别
    const turningPointsCorrect = extractedData.turning_points && 
                                extractedData.turning_points.includes('未来3年内暂无明显转折点');
    console.log(`🔄 转折点识别: ${turningPointsCorrect ? '✅' : '❌'} ${extractedData.turning_points}`);

    // 验证数据不再是默认的"计算中"状态
    const hasRealData = extractedData.three_point_rule.includes('《滴天髓》') &&
                       extractedData.spacetime_force.includes('47.5%') &&
                       !extractedData.spacetime_force.includes('计算中');

    console.log(`\n🎯 真实数据检查: ${hasRealData ? '✅' : '❌'} ${hasRealData ? '显示真实计算数据' : '仍显示默认计算中状态'}`);

    // 验证WXML绑定兼容性
    console.log('\n🔗 WXML绑定兼容性验证:');
    const wxmlFields = ['three_point_rule', 'spacetime_force', 'turning_points'];

    const allFieldsPresent = wxmlFields.every(field => extractedData.hasOwnProperty(field));
    console.log(`   📋 字段完整性: ${allFieldsPresent ? '✅' : '❌'} ${allFieldsPresent ? '所有WXML绑定字段都存在' : '缺少必要字段'}`);

    const allFieldsValid = wxmlFields.every(field => 
      typeof extractedData[field] === 'string' && extractedData[field].length > 0
    );
    console.log(`   📝 数据类型: ${allFieldsValid ? '✅' : '❌'} ${allFieldsValid ? '所有字段都是有效字符串' : '存在无效数据类型'}`);

    // 计算总体修复成功率
    const checks = [threePointCorrect, spacetimeCorrect, turningPointsCorrect, hasRealData, allFieldsPresent, allFieldsValid];
    const passedChecks = checks.filter(check => check).length;
    const successRate = (passedChecks / checks.length * 100).toFixed(1);

    console.log(`\n📊 修复验证总结:`);
    console.log(`   🎯 通过检查: ${passedChecks}/${checks.length}`);
    console.log(`   📈 修复成功率: ${successRate}%`);

    if (successRate >= 95) {
      console.log(`   ✅ 动态分析数据提取修复完成！前端应该能正确显示动态分析结果`);
      console.log(`   🎉 不再显示"计算中"状态，而是显示真实的分析数据`);
    } else if (successRate >= 80) {
      console.log(`   ⚠️ 动态分析数据提取基本修复，但仍有小问题需要解决`);
    } else {
      console.log(`   ❌ 动态分析数据提取修复不完整，需要进一步处理`);
    }

    console.log(`\n🎯 预期前端显示效果:`);
    console.log(`   🔗 三点一线法则: 《滴天髓》：三点一线，应期立现`);
    console.log(`   ⚡ 时空力量: 计算公式: 初始值 × e^(-0.1×运程年数) | 《渊海子平》：大运流年，时空作用 (强度: 47.5%)`);
    console.log(`   🔄 转折点识别: 未来3年内暂无明显转折点`);

    if (successRate >= 95) {
      console.log(`\n🚀 修复效果:`);
      console.log(`   1. 前端不再显示"时空力量强度(0.0%)"的错误数据`);
      console.log(`   2. 正确显示时空力量强度为47.5%`);
      console.log(`   3. 显示完整的古籍依据和计算公式`);
      console.log(`   4. 数据与后端计算结果完全一致`);
      console.log(`   5. WXML绑定字段完全兼容`);
    }

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  }
}

// 运行测试
testDynamicAnalysisDataExtraction();
