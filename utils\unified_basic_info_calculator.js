/**
 * 统一基本信息计算器
 * 前端唯一的基本信息计算系统
 * 替代所有分散的计算逻辑
 */

class UnifiedBasicInfoCalculator {
  constructor() {
    this.version = '1.0.0';
    this.calculationType = 'frontend_unified';
    console.log('🔧 初始化统一基本信息计算器 v' + this.version);
  }

  /**
   * 统一计算接口 - 前端唯一入口
   * @param {Object} birthInfo - 出生信息
   * @param {Array} fourPillars - 四柱八字
   * @returns {Object} 完整的基本信息
   */
  calculate(birthInfo, fourPillars) {
    console.log('🔄 开始统一基本信息计算...');
    console.log('📋 输入数据:', { birthInfo, fourPillars });

    try {
      const result = {
        // 基础用户信息
        name: birthInfo.name || '用户',
        gender: birthInfo.gender || '未知',
        birthDate: `${birthInfo.year}年${birthInfo.month}月${birthInfo.day}日`,
        birthTime: this.formatTime(birthInfo.hour, birthInfo.minute),
        location: birthInfo.birthCity || birthInfo.location || '未知',
        
        // 生肖计算
        zodiac: this.calculateZodiac(birthInfo.year),
        
        // 农历信息
        lunar_time: this.calculateLunarTime(birthInfo),
        
        // 真太阳时计算
        true_solar_time: this.calculateTrueSolarTime(birthInfo),
        
        // 出生节气
        birth_solar_term: this.calculateSolarTerm(birthInfo),
        jieqiInfo: this.calculateSolarTerm(birthInfo),
        
        // 空亡计算
        kong_wang: this.calculateKongWang(fourPillars),
        
        // 命卦计算
        ming_gua: this.calculateMingGua(birthInfo, fourPillars),
        
        // 星座计算
        constellation: this.calculateConstellation(birthInfo.month, birthInfo.day),
        
        // 星宿计算
        star_mansion: this.calculateStarMansion(fourPillars[2].zhi),
        
        // 计算时间戳
        calculatedAt: new Date().toISOString(),
        calculatorVersion: this.version,
        dataSource: 'unified_frontend_calculator'
      };

      console.log('✅ 统一基本信息计算完成');
      return result;
      
    } catch (error) {
      console.error('❌ 统一基本信息计算失败:', error);
      return this.getDefaultBasicInfo(birthInfo);
    }
  }

  /**
   * 生肖计算
   */
  calculateZodiac(year) {
    const zodiacs = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];
    return zodiacs[(year - 4) % 12];
  }

  /**
   * 农历时间计算（简化版）
   */
  calculateLunarTime(birthInfo) {
    // 这里使用简化的农历转换，实际项目中应该使用专业的农历库
    const lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月', 
                        '七月', '八月', '九月', '十月', '冬月', '腊月'];
    const lunarMonth = lunarMonths[(birthInfo.month - 1) % 12];
    return `${this.calculateZodiac(birthInfo.year)}年${lunarMonth}${birthInfo.day}日`;
  }

  /**
   * 真太阳时计算
   */
  calculateTrueSolarTime(birthInfo) {
    const longitude = birthInfo.longitude || 116.4074; // 默认北京经度
    const timeDifference = (longitude - 120) * 4; // 分钟
    
    const originalMinutes = birthInfo.hour * 60 + birthInfo.minute;
    const correctedMinutes = originalMinutes + timeDifference;
    
    const correctedHour = Math.floor(correctedMinutes / 60) % 24;
    const correctedMinute = Math.floor(correctedMinutes % 60);
    
    return this.formatTime(correctedHour, correctedMinute);
  }

  /**
   * 节气计算
   */
  calculateSolarTerm(birthInfo) {
    const month = birthInfo.month;
    const day = birthInfo.day;
    
    // 简化的节气计算表
    const solarTerms = {
      1: { early: '小寒', late: '大寒', divider: 20 },
      2: { early: '立春', late: '雨水', divider: 19 },
      3: { early: '惊蛰', late: '春分', divider: 21 },
      4: { early: '清明', late: '谷雨', divider: 20 },
      5: { early: '立夏', late: '小满', divider: 21 },
      6: { early: '芒种', late: '夏至', divider: 22 },
      7: { early: '小暑', late: '大暑', divider: 23 },
      8: { early: '立秋', late: '处暑', divider: 23 },
      9: { early: '白露', late: '秋分', divider: 23 },
      10: { early: '寒露', late: '霜降', divider: 24 },
      11: { early: '立冬', late: '小雪', divider: 22 },
      12: { early: '大雪', late: '冬至', divider: 22 }
    };

    const termInfo = solarTerms[month];
    if (!termInfo) return '未知节气';

    const currentTerm = day >= termInfo.divider ? termInfo.late : termInfo.early;
    const daysAfter = day >= termInfo.divider ? day - termInfo.divider : day - (termInfo.divider - 15);
    
    return `${currentTerm}后${Math.max(0, daysAfter)}天`;
  }

  /**
   * 空亡计算
   */
  calculateKongWang(fourPillars) {
    if (!fourPillars || fourPillars.length < 3) return '未知';
    
    const dayGan = fourPillars[2].gan;
    const dayZhi = fourPillars[2].zhi;
    
    // 空亡计算表（简化版）
    const kongWangTable = {
      '甲子': '戌亥', '甲寅': '子丑', '甲辰': '寅卯', '甲午': '辰巳', '甲申': '午未', '甲戌': '申酉',
      '乙丑': '戌亥', '乙卯': '子丑', '乙巳': '寅卯', '乙未': '辰巳', '乙酉': '午未', '乙亥': '申酉',
      '丙寅': '戌亥', '丙辰': '子丑', '丙午': '寅卯', '丙申': '辰巳', '丙戌': '午未', '丙子': '申酉',
      '丁卯': '戌亥', '丁巳': '子丑', '丁未': '寅卯', '丁酉': '辰巳', '丁亥': '午未', '丁丑': '申酉',
      '戊辰': '戌亥', '戊午': '子丑', '戊申': '寅卯', '戊戌': '辰巳', '戊子': '午未', '戊寅': '申酉',
      '己巳': '戌亥', '己未': '子丑', '己酉': '寅卯', '己亥': '辰巳', '己丑': '午未', '己卯': '申酉',
      '庚午': '戌亥', '庚申': '子丑', '庚戌': '寅卯', '庚子': '辰巳', '庚寅': '午未', '庚辰': '申酉',
      '辛未': '戌亥', '辛酉': '子丑', '辛亥': '寅卯', '辛丑': '辰巳', '辛卯': '午未', '辛巳': '申酉',
      '壬申': '戌亥', '壬戌': '子丑', '壬子': '寅卯', '壬寅': '辰巳', '壬辰': '午未', '壬午': '申酉',
      '癸酉': '戌亥', '癸亥': '子丑', '癸丑': '寅卯', '癸卯': '辰巳', '癸巳': '午未', '癸未': '申酉'
    };

    const dayPillar = dayGan + dayZhi;
    return kongWangTable[dayPillar] + '空' || '未知';
  }

  /**
   * 命卦计算
   */
  calculateMingGua(birthInfo, fourPillars) {
    const year = birthInfo.year;
    const gender = birthInfo.gender;
    
    const yearLastTwo = year % 100;
    let mingGuaNumber;
    
    if (gender === '男') {
      mingGuaNumber = (99 - yearLastTwo) % 9;
    } else {
      mingGuaNumber = (yearLastTwo + 6) % 9;
    }
    
    if (mingGuaNumber === 0) mingGuaNumber = 9;

    const mingGuaNames = {
      1: '坎卦', 2: '坤卦', 3: '震卦', 4: '巽卦',
      5: '中宫', 6: '乾卦', 7: '兑卦', 8: '艮卦', 9: '离卦'
    };

    return mingGuaNames[mingGuaNumber] || '未知';
  }

  /**
   * 星座计算
   */
  calculateConstellation(month, day) {
    const constellations = [
      { name: '摩羯座', start: [12, 22], end: [1, 19] },
      { name: '水瓶座', start: [1, 20], end: [2, 18] },
      { name: '双鱼座', start: [2, 19], end: [3, 20] },
      { name: '白羊座', start: [3, 21], end: [4, 19] },
      { name: '金牛座', start: [4, 20], end: [5, 20] },
      { name: '双子座', start: [5, 21], end: [6, 21] },
      { name: '巨蟹座', start: [6, 22], end: [7, 22] },
      { name: '狮子座', start: [7, 23], end: [8, 22] },
      { name: '处女座', start: [8, 23], end: [9, 22] },
      { name: '天秤座', start: [9, 23], end: [10, 23] },
      { name: '天蝎座', start: [10, 24], end: [11, 22] },
      { name: '射手座', start: [11, 23], end: [12, 21] }
    ];

    for (const constellation of constellations) {
      const [startMonth, startDay] = constellation.start;
      const [endMonth, endDay] = constellation.end;
      
      if (startMonth === endMonth) {
        if (month === startMonth && day >= startDay && day <= endDay) {
          return constellation.name;
        }
      } else {
        if ((month === startMonth && day >= startDay) || 
            (month === endMonth && day <= endDay)) {
          return constellation.name;
        }
      }
    }
    return '未知';
  }

  /**
   * 星宿计算
   */
  calculateStarMansion(dayZhi) {
    const starMansions = {
      '子': '虚日鼠', '丑': '危月燕', '寅': '室火猪', '卯': '壁水貐',
      '辰': '奎木狼', '巳': '娄金狗', '午': '胃土雉', '未': '昴日鸡',
      '申': '毕月乌', '酉': '觜火猴', '戌': '参水猿', '亥': '井木犴'
    };
    return starMansions[dayZhi] || '未知';
  }

  /**
   * 格式化时间
   */
  formatTime(hour, minute) {
    const h = parseInt(hour) || 0;
    const m = parseInt(minute) || 0;
    return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
  }

  /**
   * 获取默认基本信息（错误时使用）
   */
  getDefaultBasicInfo(birthInfo) {
    return {
      name: birthInfo.name || '用户',
      gender: birthInfo.gender || '未知',
      birthDate: `${birthInfo.year}年${birthInfo.month}月${birthInfo.day}日`,
      birthTime: this.formatTime(birthInfo.hour, birthInfo.minute),
      location: birthInfo.birthCity || birthInfo.location || '未知',
      zodiac: this.calculateZodiac(birthInfo.year),
      lunar_time: '未知',
      true_solar_time: this.formatTime(birthInfo.hour, birthInfo.minute),
      birth_solar_term: '未知',
      jieqiInfo: '未知',
      kong_wang: '未知',
      ming_gua: '未知',
      constellation: '未知',
      star_mansion: '未知',
      calculatedAt: new Date().toISOString(),
      calculatorVersion: this.version,
      dataSource: 'unified_frontend_calculator_fallback'
    };
  }

  /**
   * 清除缓存（如果需要）
   */
  clearCache() {
    console.log('🗑️ 清除统一基本信息计算器缓存');
  }

  /**
   * 获取计算器信息
   */
  getInfo() {
    return {
      name: '统一基本信息计算器',
      version: this.version,
      type: this.calculationType,
      description: '前端唯一的基本信息计算系统',
      capabilities: [
        '生肖计算', '农历转换', '真太阳时', '节气计算',
        '空亡计算', '命卦计算', '星座计算', '星宿计算'
      ]
    };
  }
}

// 创建全局实例
const unifiedBasicInfoCalculator = new UnifiedBasicInfoCalculator();

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = UnifiedBasicInfoCalculator;
} else if (typeof window !== 'undefined') {
  window.UnifiedBasicInfoCalculator = UnifiedBasicInfoCalculator;
  window.unifiedBasicInfoCalculator = unifiedBasicInfoCalculator;
}

console.log('✅ 统一基本信息计算器已加载');
