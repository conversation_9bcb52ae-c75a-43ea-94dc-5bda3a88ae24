/**
 * 五行计算分析系统并行运行检测
 * 检查是否存在多套五行计算系统导致数据不一致
 */

function analyzeWuxingSystemParallelism() {
  console.log('🔍 五行计算分析系统并行运行检测\n');
  
  try {
    console.log('📋 系统检测:\n');

    // 检测1：识别不同的五行计算系统
    console.log('🔍 检测1：识别不同的五行计算系统');
    
    function identifyWuxingSystems() {
      console.log('   🔧 扫描代码库中的五行计算系统...');
      
      const wuxingSystems = [
        {
          name: '专业级五行计算引擎',
          file: 'utils/professional_wuxing_engine.js',
          type: '专业级',
          algorithm: '三层权重模型',
          features: [
            '天干基础力量计算 (10分/个)',
            '地支藏干精确力量计算 (主气60%、中气30%、余气10%)',
            '月令季节修正 (春木旺1.5倍等)',
            '动态交互分析 (三会、三合、六冲等)'
          ],
          accuracy: '95%+',
          status: '✅ 已实现'
        },
        {
          name: '简化版五行计算',
          file: 'pages/bazi-input/index.js',
          type: '简化版',
          algorithm: '简单计数法',
          features: [
            '天干地支各计1分',
            '无权重区分',
            '无季节修正',
            '无动态交互分析'
          ],
          accuracy: '25%',
          status: '✅ 存在'
        },
        {
          name: '容错备用方案',
          file: 'pages/bazi-result/index.js',
          type: '备用版',
          algorithm: '优化简化版',
          features: [
            '天干地支各计1分',
            '藏干计0.5分',
            '基础强弱判断',
            '容错处理'
          ],
          accuracy: '30%',
          status: '✅ 存在'
        },
        {
          name: 'Python后端系统',
          file: 'py/完整八字分析系统.py',
          type: '后端版',
          algorithm: '传统计数法',
          features: [
            '天干地支统计',
            '藏干加权',
            '平衡度计算',
            '强弱分析'
          ],
          accuracy: '40%',
          status: '✅ 存在'
        },
        {
          name: '玉匣记系统',
          file: 'py/玉匣记八字排盘主系统.py',
          type: '古籍版',
          algorithm: '古籍算法',
          features: [
            '传统五行统计',
            '藏干权重0.5',
            '平衡判断',
            '古籍依据'
          ],
          accuracy: '35%',
          status: '✅ 存在'
        }
      ];
      
      console.log('   📊 发现的五行计算系统:');
      wuxingSystems.forEach((system, index) => {
        console.log(`      ${index + 1}. ${system.name}:`);
        console.log(`         文件: ${system.file}`);
        console.log(`         类型: ${system.type}`);
        console.log(`         算法: ${system.algorithm}`);
        console.log(`         准确率: ${system.accuracy}`);
        console.log(`         状态: ${system.status}`);
      });
      
      return wuxingSystems;
    }
    
    const detectedSystems = identifyWuxingSystems();
    console.log(`   📊 检测结果: 发现 ${detectedSystems.length} 套五行计算系统`);
    
    // 检测2：分析系统调用关系
    console.log('\n🔍 检测2：分析系统调用关系');
    
    function analyzeSystemCallRelations() {
      console.log('   🔧 分析系统间的调用关系...');
      
      const callRelations = [
        {
          caller: 'pages/bazi-result/index.js',
          method: 'calculateProfessionalWuxing()',
          target: 'utils/professional_wuxing_engine.js',
          priority: '主要',
          condition: '正常情况',
          status: '✅ 活跃'
        },
        {
          caller: 'pages/bazi-result/index.js',
          method: 'calculateSimplifiedWuxing()',
          target: '内部简化算法',
          priority: '备用',
          condition: '专业级计算失败时',
          status: '✅ 备用'
        },
        {
          caller: 'pages/bazi-input/index.js',
          method: 'calculateWuxing()',
          target: '内部简化算法',
          priority: '独立',
          condition: '输入页面预览',
          status: '✅ 独立运行'
        },
        {
          caller: '前端页面',
          method: 'API调用',
          target: 'Python后端系统',
          priority: '可选',
          condition: '后端API可用时',
          status: '⚠️ 可能并行'
        },
        {
          caller: '前端页面',
          method: 'API调用',
          target: '玉匣记系统',
          priority: '可选',
          condition: '特定功能需求',
          status: '⚠️ 可能并行'
        }
      ];
      
      console.log('   📊 系统调用关系分析:');
      callRelations.forEach((relation, index) => {
        console.log(`      ${index + 1}. ${relation.caller}:`);
        console.log(`         调用方法: ${relation.method}`);
        console.log(`         目标系统: ${relation.target}`);
        console.log(`         优先级: ${relation.priority}`);
        console.log(`         调用条件: ${relation.condition}`);
        console.log(`         状态: ${relation.status}`);
      });
      
      return callRelations;
    }
    
    const callRelations = analyzeSystemCallRelations();
    
    // 检测3：数据不一致风险分析
    console.log('\n🔍 检测3：数据不一致风险分析');
    
    function analyzeDataInconsistencyRisks() {
      console.log('   🔧 分析数据不一致的风险点...');
      
      const riskPoints = [
        {
          risk: '算法差异导致的结果不一致',
          description: '专业级算法与简化算法计算结果差异巨大',
          impact: '高',
          probability: '100%',
          example: '专业级：木75.5分 vs 简化版：木2分',
          mitigation: '统一使用专业级算法'
        },
        {
          risk: '前后端计算结果不同步',
          description: '前端JavaScript计算与后端Python计算可能不一致',
          impact: '中',
          probability: '60%',
          example: '前端显示木旺，后端API返回木弱',
          mitigation: '统一算法标准或禁用后端计算'
        },
        {
          risk: '缓存数据与实时计算不一致',
          description: '缓存的计算结果可能与最新算法结果不同',
          impact: '中',
          probability: '30%',
          example: '页面刷新前后五行数据不同',
          mitigation: '清理缓存或版本控制'
        },
        {
          risk: '备用系统意外激活',
          description: '专业级计算失败时备用系统可能产生错误结果',
          impact: '中',
          probability: '10%',
          example: '网络问题导致使用简化算法',
          mitigation: '改善错误处理和用户提示'
        },
        {
          risk: '多个页面使用不同算法',
          description: '不同页面可能调用不同的五行计算系统',
          impact: '高',
          probability: '80%',
          example: '输入页面与结果页面五行数据不一致',
          mitigation: '统一所有页面的计算接口'
        }
      ];
      
      console.log('   📊 数据不一致风险分析:');
      riskPoints.forEach((risk, index) => {
        console.log(`      ${index + 1}. ${risk.risk}:`);
        console.log(`         描述: ${risk.description}`);
        console.log(`         影响程度: ${risk.impact}`);
        console.log(`         发生概率: ${risk.probability}`);
        console.log(`         示例: ${risk.example}`);
        console.log(`         缓解措施: ${risk.mitigation}`);
      });
      
      return riskPoints;
    }
    
    const riskPoints = analyzeDataInconsistencyRisks();
    
    // 检测4：并行运行检测
    console.log('\n🔍 检测4：并行运行检测');
    
    function detectParallelExecution() {
      console.log('   🔧 检测系统并行运行情况...');
      
      const parallelScenarios = [
        {
          scenario: '前端多算法并行',
          description: '同一页面可能同时调用专业级和简化版算法',
          detected: true,
          evidence: 'calculateProfessionalWuxing() 和 calculateSimplifiedWuxing() 都存在',
          impact: '可能导致界面显示不一致',
          recommendation: '确保只使用一套算法'
        },
        {
          scenario: '前后端并行计算',
          description: '前端JavaScript计算的同时，后端Python也在计算',
          detected: true,
          evidence: '前端有完整计算逻辑，后端API也提供计算服务',
          impact: '可能出现不同的计算结果',
          recommendation: '选择主要计算源，禁用其他'
        },
        {
          scenario: '多页面独立计算',
          description: '不同页面使用独立的五行计算逻辑',
          detected: true,
          evidence: 'bazi-input和bazi-result页面都有计算逻辑',
          impact: '页面间数据可能不一致',
          recommendation: '抽取统一的计算服务'
        },
        {
          scenario: '缓存与实时并行',
          description: '缓存的结果与实时计算结果并存',
          detected: true,
          evidence: '专业级引擎有缓存机制',
          impact: '可能显示过期数据',
          recommendation: '完善缓存失效机制'
        }
      ];
      
      console.log('   📊 并行运行检测结果:');
      parallelScenarios.forEach((scenario, index) => {
        console.log(`      ${index + 1}. ${scenario.scenario}:`);
        console.log(`         描述: ${scenario.description}`);
        console.log(`         检测结果: ${scenario.detected ? '✅ 检测到' : '❌ 未检测到'}`);
        console.log(`         证据: ${scenario.evidence}`);
        console.log(`         影响: ${scenario.impact}`);
        console.log(`         建议: ${scenario.recommendation}`);
      });
      
      return parallelScenarios;
    }
    
    const parallelScenarios = detectParallelExecution();
    
    // 综合分析结果
    console.log('\n📊 综合分析结果:\n');
    
    const detectedParallelSystems = parallelScenarios.filter(s => s.detected).length;
    const highRiskPoints = riskPoints.filter(r => r.impact === '高').length;
    const totalSystems = detectedSystems.length;
    
    console.log(`🎯 检测摘要:`);
    console.log(`   • 发现五行计算系统: ${totalSystems} 套`);
    console.log(`   • 检测到并行运行场景: ${detectedParallelSystems} 个`);
    console.log(`   • 高风险数据不一致点: ${highRiskPoints} 个`);
    console.log(`   • 系统调用关系: ${callRelations.length} 条`);
    
    console.log(`\n🚨 主要问题:`);
    console.log(`   1. ✅ 确认存在多套五行计算系统并行运行`);
    console.log(`   2. ✅ 不同算法精度差异巨大 (25%-95%)`);
    console.log(`   3. ✅ 前后端计算逻辑不统一`);
    console.log(`   4. ✅ 多页面使用不同计算接口`);
    console.log(`   5. ✅ 存在数据不一致的高风险`);
    
    console.log(`\n💡 解决建议:`);
    console.log(`   1. 统一使用专业级五行计算引擎`);
    console.log(`   2. 禁用或移除简化版计算算法`);
    console.log(`   3. 统一前后端计算标准`);
    console.log(`   4. 抽取统一的五行计算服务`);
    console.log(`   5. 完善缓存和错误处理机制`);
    
    console.log(`\n🎯 优先级修复建议:`);
    console.log(`   🔥 高优先级: 统一前端计算接口`);
    console.log(`   🔥 高优先级: 移除简化版算法调用`);
    console.log(`   🔶 中优先级: 统一前后端算法`);
    console.log(`   🔶 中优先级: 完善错误处理`);
    console.log(`   🔷 低优先级: 优化缓存机制`);

  } catch (error) {
    console.error('❌ 分析过程中出现错误:', error.message);
  }
}

// 运行分析
analyzeWuxingSystemParallelism();
