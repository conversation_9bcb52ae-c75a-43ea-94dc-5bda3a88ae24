#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def analyze_wxml_content(file_path):
    """分析WXML文件的内容完整性"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"📊 文件分析: {file_path}")
    print(f"总行数: {len(content.split('\n'))}")
    
    # 分析tab页面
    tab_pages = ['basic', 'paipan', 'advanced', 'fortune', 'professional', 'classical', 'timing', 'liuqin']
    found_tabs = []
    
    for tab in tab_pages:
        if f"currentTab === '{tab}'" in content:
            found_tabs.append(tab)
    
    print(f"\n🏷️ Tab页面分析:")
    print(f"找到的tab页面: {found_tabs}")
    
    # 分析卡片组件
    card_patterns = [
        r'class="tianggong-card ([^"]*)"',
        r'class="card-header"',
        r'class="card-content"',
        r'class="card-title"'
    ]
    
    cards = []
    for pattern in card_patterns:
        matches = re.findall(pattern, content)
        if pattern == r'class="tianggong-card ([^"]*)"':
            cards.extend(matches)
    
    print(f"\n🎴 卡片组件分析:")
    print(f"找到的卡片类型: {len(set(cards))}")
    for card in sorted(set(cards)):
        print(f"  - {card}")
    
    # 分析数据绑定
    data_bindings = re.findall(r'\{\{([^}]+)\}\}', content)
    unique_bindings = set(data_bindings)
    
    print(f"\n📊 数据绑定分析:")
    print(f"数据绑定表达式总数: {len(data_bindings)}")
    print(f"唯一数据绑定: {len(unique_bindings)}")
    
    # 分析主要数据对象
    data_objects = set()
    for binding in unique_bindings:
        if '.' in binding:
            obj = binding.split('.')[0].strip()
            data_objects.add(obj)
    
    print(f"主要数据对象: {sorted(data_objects)}")
    
    # 分析条件渲染
    wx_if_count = len(re.findall(r'wx:if=', content))
    wx_elif_count = len(re.findall(r'wx:elif=', content))
    wx_else_count = len(re.findall(r'wx:else(?:\s|>)', content))
    wx_for_count = len(re.findall(r'wx:for=', content))
    
    print(f"\n🔄 条件渲染分析:")
    print(f"wx:if: {wx_if_count}")
    print(f"wx:elif: {wx_elif_count}")
    print(f"wx:else: {wx_else_count}")
    print(f"wx:for: {wx_for_count}")
    
    # 分析事件处理
    event_handlers = re.findall(r'bind\w+="([^"]*)"', content)
    unique_handlers = set(event_handlers)
    
    print(f"\n🎯 事件处理分析:")
    print(f"事件处理器: {sorted(unique_handlers)}")
    
    # 分析特殊功能组件
    special_components = {
        '天体图': 'celestial-chart' in content,
        '五行分析': 'wuxing' in content,
        '神煞分析': 'stars-grid' in content or 'auspicious' in content,
        '大运流年': 'dayun' in content or 'liunian' in content,
        '应期分析': 'timing-event' in content,
        '六亲分析': 'liuqin' in content or 'spouse' in content,
        '古籍分析': 'classical' in content or 'quote' in content,
        '格局分析': 'mingge' in content or 'geju' in content
    }
    
    print(f"\n🔮 特殊功能组件:")
    for component, exists in special_components.items():
        status = "✅" if exists else "❌"
        print(f"  {status} {component}")
    
    return {
        'tabs': found_tabs,
        'cards': len(set(cards)),
        'data_bindings': len(unique_bindings),
        'data_objects': data_objects,
        'conditionals': wx_if_count + wx_elif_count + wx_else_count,
        'loops': wx_for_count,
        'events': unique_handlers,
        'special_components': special_components
    }

if __name__ == "__main__":
    result = analyze_wxml_content("pages/bazi-result/index.wxml")
    
    print(f"\n📋 内容完整性评估:")
    
    # 评估标准
    expected_tabs = 8
    expected_min_cards = 20  # 预期至少20种不同的卡片
    expected_min_bindings = 100  # 预期至少100个数据绑定
    
    tab_score = len(result['tabs']) / expected_tabs * 100
    card_score = min(result['cards'] / expected_min_cards * 100, 100)
    binding_score = min(result['data_bindings'] / expected_min_bindings * 100, 100)
    
    print(f"Tab页面完整度: {tab_score:.1f}% ({len(result['tabs'])}/{expected_tabs})")
    print(f"卡片组件丰富度: {card_score:.1f}% ({result['cards']}/{expected_min_cards}+)")
    print(f"数据绑定完整度: {binding_score:.1f}% ({result['data_bindings']}/{expected_min_bindings}+)")
    
    special_count = sum(1 for exists in result['special_components'].values() if exists)
    special_score = special_count / len(result['special_components']) * 100
    print(f"特殊功能完整度: {special_score:.1f}% ({special_count}/{len(result['special_components'])})")
    
    overall_score = (tab_score + card_score + binding_score + special_score) / 4
    print(f"\n🎯 总体完整度评分: {overall_score:.1f}%")
    
    if overall_score >= 90:
        print("✅ 内容完整性优秀")
    elif overall_score >= 70:
        print("⚠️ 内容完整性良好，但可能有部分缺失")
    else:
        print("❌ 内容完整性不足，需要补充")
