/**
 * 统一五行计算接口
 * 唯一的五行计算数据源，封装专业级五行计算引擎
 * 确保整个应用的五行数据一致性
 */

class UnifiedWuxingCalculator {
  constructor() {
    try {
      const ProfessionalWuxingEngine = require('./professional_wuxing_engine.js');
      this.engine = new ProfessionalWuxingEngine();
      this.cache = new Map(); // 结果缓存
      this.version = '1.0.0'; // 版本标识

      console.log('🎯 统一五行计算器初始化完成');
    } catch (error) {
      console.error('❌ 统一五行计算器初始化失败:', error);
      this.engine = null;
      this.cache = new Map();
      this.version = '1.0.0';
    }
  }

  /**
   * 统一五行计算入口
   * @param {Object} baziData - 八字数据
   * @param {Object} options - 计算选项
   * @returns {Object} 标准化的五行计算结果
   */
  calculate(baziData, options = {}) {
    try {
      // 参数验证
      if (!this.validateInput(baziData)) {
        throw new Error('八字数据格式不正确');
      }

      // 生成缓存键
      const cacheKey = this.generateCacheKey(baziData, options);
      
      // 检查缓存
      if (this.cache.has(cacheKey) && !options.forceRecalculate) {
        console.log('📋 使用缓存的五行计算结果');
        return this.cache.get(cacheKey);
      }

      console.log('🔄 开始专业级五行计算...');

      // 检查引擎是否可用
      if (!this.engine) {
        throw new Error('专业级引擎未初始化');
      }

      // 转换八字数据格式为四柱格式
      const fourPillars = this.convertToFourPillars(baziData);

      // 调用专业级引擎计算
      const rawResult = this.engine.generateDetailedReport(fourPillars);

      // 标准化结果格式
      const standardizedResult = this.standardizeResult(rawResult, baziData);
      
      // 缓存结果
      this.cache.set(cacheKey, standardizedResult);
      
      console.log('✅ 五行计算完成');
      return standardizedResult;
      
    } catch (error) {
      console.error('❌ 五行计算错误:', error.message);
      return this.handleCalculationError(error, baziData);
    }
  }

  /**
   * 验证输入数据
   */
  validateInput(baziData) {
    if (!baziData) return false;

    const requiredFields = ['year', 'month', 'day', 'hour'];
    return requiredFields.every(field => {
      return baziData[field] &&
             baziData[field].gan &&
             baziData[field].zhi;
    });
  }

  /**
   * 转换八字数据为四柱格式
   */
  convertToFourPillars(baziData) {
    return [
      { gan: baziData.year.gan, zhi: baziData.year.zhi },
      { gan: baziData.month.gan, zhi: baziData.month.zhi },
      { gan: baziData.day.gan, zhi: baziData.day.zhi },
      { gan: baziData.hour.gan, zhi: baziData.hour.zhi }
    ];
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(baziData, options) {
    const baziString = `${baziData.year.gan}${baziData.year.zhi}_${baziData.month.gan}${baziData.month.zhi}_${baziData.day.gan}${baziData.day.zhi}_${baziData.hour.gan}${baziData.hour.zhi}`;
    const optionsString = JSON.stringify(options);
    return `${baziString}_${optionsString}_v${this.version}`;
  }

  /**
   * 标准化计算结果
   */
  standardizeResult(rawResult, baziData) {
    // 从专业级引擎结果中提取数据
    const finalPowers = rawResult.results?.finalPowers || {};
    const statistics = rawResult.results?.statistics || {};

    // 计算总力量和百分比
    const totalStrength = Object.values(finalPowers).reduce((sum, val) => sum + val, 0);
    const wuxingPercentages = {};
    Object.entries(finalPowers).forEach(([element, power]) => {
      wuxingPercentages[element] = totalStrength > 0 ? (power / totalStrength * 100) : 0;
    });

    // 转换中文五行名称为英文
    const elementMap = { '木': 'wood', '火': 'fire', '土': 'earth', '金': 'metal', '水': 'water' };
    const englishPowers = {};
    const englishPercentages = {};

    Object.entries(finalPowers).forEach(([chineseElement, power]) => {
      const englishElement = elementMap[chineseElement];
      if (englishElement) {
        englishPowers[englishElement] = Math.round(power * 10) / 10;
        englishPercentages[englishElement] = Math.round(wuxingPercentages[chineseElement] * 10) / 10;
      }
    });

    return {
      // 基础信息
      version: this.version,
      timestamp: new Date().toISOString(),
      source: 'professional_wuxing_engine',
      algorithm: rawResult.algorithm || '专业级三层权重模型',

      // 八字信息
      bazi: {
        year: baziData.year,
        month: baziData.month,
        day: baziData.day,
        hour: baziData.hour,
        fullBazi: `${baziData.year.gan}${baziData.year.zhi} ${baziData.month.gan}${baziData.month.zhi} ${baziData.day.gan}${baziData.day.zhi} ${baziData.hour.gan}${baziData.hour.zhi}`
      },

      // 五行力量分析（英文格式，兼容前端）
      wuxingStrength: {
        wood: {
          value: englishPowers.wood || 0,
          percentage: englishPercentages.wood || 0,
          level: this.getStrengthLevel(englishPercentages.wood || 0),
          chineseName: '木'
        },
        fire: {
          value: englishPowers.fire || 0,
          percentage: englishPercentages.fire || 0,
          level: this.getStrengthLevel(englishPercentages.fire || 0),
          chineseName: '火'
        },
        earth: {
          value: englishPowers.earth || 0,
          percentage: englishPercentages.earth || 0,
          level: this.getStrengthLevel(englishPercentages.earth || 0),
          chineseName: '土'
        },
        metal: {
          value: englishPowers.metal || 0,
          percentage: englishPercentages.metal || 0,
          level: this.getStrengthLevel(englishPercentages.metal || 0),
          chineseName: '金'
        },
        water: {
          value: englishPowers.water || 0,
          percentage: englishPercentages.water || 0,
          level: this.getStrengthLevel(englishPercentages.water || 0),
          chineseName: '水'
        }
      },

      // 简化格式（兼容现有前端代码）
      wood: englishPowers.wood || 0,
      fire: englishPowers.fire || 0,
      earth: englishPowers.earth || 0,
      metal: englishPowers.metal || 0,
      water: englishPowers.water || 0,

      // 五行平衡分析
      balance: {
        strongest: statistics.strongest || '未知',
        weakest: statistics.weakest || '未知',
        balanceScore: statistics.balanceIndex || 0,
        balanceLevel: this.getBalanceLevel(statistics.balanceIndex || 0),
        recommendation: this.generateBalanceRecommendation(statistics)
      },

      // 季节修正信息
      seasonalAdjustment: rawResult.inputData || {},

      // 计算详情
      calculationDetails: {
        totalStrength: totalStrength,
        rawPowers: finalPowers,
        statistics: statistics,
        calculationSteps: rawResult.calculationSteps || {},
        confidence: 95
      }
    };
  }

  /**
   * 获取五行强度等级
   */
  getStrengthLevel(percentage) {
    if (percentage >= 35) return '极旺';
    if (percentage >= 25) return '偏旺';
    if (percentage >= 15) return '中和';
    if (percentage >= 8) return '偏弱';
    return '极弱';
  }

  /**
   * 获取平衡等级
   */
  getBalanceLevel(score) {
    if (score >= 80) return '非常平衡';
    if (score >= 60) return '较为平衡';
    if (score >= 40) return '一般平衡';
    if (score >= 20) return '不太平衡';
    return '严重失衡';
  }

  /**
   * 生成平衡建议
   */
  generateBalanceRecommendation(statistics) {
    if (!statistics.strongest || !statistics.weakest) {
      return '需要进一步分析五行配置';
    }

    const elementAdvice = {
      '木': '宜多接触绿色植物，从事文教、林业相关工作',
      '火': '宜多晒太阳，从事能源、娱乐相关工作',
      '土': '宜多接触土地，从事房地产、农业相关工作',
      '金': '宜多接触金属，从事金融、机械相关工作',
      '水': '宜多接触水源，从事航运、水利相关工作'
    };

    if (statistics.strongest === statistics.weakest) {
      return '五行配置相对均衡，宜保持现状';
    }

    return `${statistics.strongest}偏旺，${statistics.weakest}偏弱。建议：${elementAdvice[statistics.weakest] || '平衡五行配置'}`;
  }

  /**
   * 处理计算错误
   */
  handleCalculationError(error, baziData) {
    console.error('🚨 五行计算失败，返回错误结果');
    
    return {
      version: this.version,
      timestamp: new Date().toISOString(),
      source: 'error_handler',
      error: true,
      errorMessage: error.message,
      
      bazi: baziData ? {
        year: baziData.year,
        month: baziData.month,
        day: baziData.day,
        hour: baziData.hour,
        fullBazi: baziData.year && baziData.month && baziData.day && baziData.hour ? 
          `${baziData.year.gan}${baziData.year.zhi} ${baziData.month.gan}${baziData.month.zhi} ${baziData.day.gan}${baziData.day.zhi} ${baziData.hour.gan}${baziData.hour.zhi}` : '数据不完整'
      } : null,
      
      wuxingStrength: {
        wood: { value: 0, percentage: 0, level: '计算失败', sources: [] },
        fire: { value: 0, percentage: 0, level: '计算失败', sources: [] },
        earth: { value: 0, percentage: 0, level: '计算失败', sources: [] },
        metal: { value: 0, percentage: 0, level: '计算失败', sources: [] },
        water: { value: 0, percentage: 0, level: '计算失败', sources: [] }
      },
      
      balance: {
        strongest: '计算失败',
        weakest: '计算失败',
        balanceScore: 0,
        balanceLevel: '计算失败',
        recommendation: '请检查八字数据后重新计算'
      },
      
      calculationDetails: {
        totalStrength: 0,
        adjustmentFactors: {},
        warnings: ['计算过程中发生错误'],
        confidence: 0
      }
    };
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.cache.clear();
    console.log('🗑️ 五行计算缓存已清理');
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      version: this.version
    };
  }
}

// 创建单例实例
const unifiedCalculator = new UnifiedWuxingCalculator();

module.exports = unifiedCalculator;
