/**
 * 🎯 神煞系统架构管理器
 * 统一神煞计算架构，消除前后端多套并行系统
 * 建立单一权威计算入口，防止重复计算和数据不一致
 */

class ShenshaArchitectureManager {
  constructor() {
    this.calculationLock = false;
    this.currentCalculationSource = null;
    this.calculationHistory = [];
    this.architectureStatus = 'UNIFIED'; // UNIFIED | MIXED | CHAOS
  }

  /**
   * 🔒 获取计算锁
   */
  acquireCalculationLock(source) {
    if (this.calculationLock) {
      console.warn(`⚠️ 神煞计算锁已被 ${this.currentCalculationSource} 占用`);
      return false;
    }
    
    this.calculationLock = true;
    this.currentCalculationSource = source;
    console.log(`🔒 神煞计算锁已获取：${source}`);
    return true;
  }

  /**
   * 🔓 释放计算锁
   */
  releaseCalculationLock() {
    if (!this.calculationLock) {
      console.warn('⚠️ 尝试释放未获取的神煞计算锁');
      return;
    }
    
    console.log(`🔓 神煞计算锁已释放：${this.currentCalculationSource}`);
    this.calculationLock = false;
    this.currentCalculationSource = null;
  }

  /**
   * 🏗️ 验证架构一致性
   */
  validateArchitectureConsistency() {
    const issues = [];
    
    // 检查是否存在多套计算系统
    const systemChecks = [
      { name: '内置计算器', exists: true }, // 总是存在
      { name: '外部计算器', exists: false }, // 应该不存在
      { name: '版本控制系统', exists: false }, // 应该不存在
      { name: 'Web神煞系统', exists: false } // 应该不存在
    ];
    
    systemChecks.forEach(check => {
      if (check.name === '内置计算器' && !check.exists) {
        issues.push(`❌ ${check.name} 缺失`);
      } else if (check.name !== '内置计算器' && check.exists) {
        issues.push(`⚠️ ${check.name} 仍然存在，造成重复计算`);
      }
    });
    
    if (issues.length === 0) {
      this.architectureStatus = 'UNIFIED';
      console.log('✅ 神煞架构一致性验证通过');
    } else {
      this.architectureStatus = 'MIXED';
      console.warn('⚠️ 神煞架构一致性验证失败:', issues);
    }
    
    return { status: this.architectureStatus, issues: issues };
  }

  /**
   * 🎯 执行统一神煞计算
   */
  executeUnifiedCalculation(calculationFunction, params, source = 'UNIFIED_SYSTEM') {
    // 获取计算锁
    if (!this.acquireCalculationLock(source)) {
      throw new Error('无法获取神煞计算锁，可能存在并发计算');
    }
    
    try {
      console.log('🎯 开始统一神煞计算...');
      const startTime = Date.now();
      
      // 执行计算
      const results = calculationFunction(...params);
      
      const endTime = Date.now();
      const calculationRecord = {
        source: source,
        timestamp: new Date().toISOString(),
        duration: endTime - startTime,
        resultCount: results ? results.length : 0,
        success: true
      };
      
      this.calculationHistory.push(calculationRecord);
      console.log(`✅ 统一神煞计算完成：${calculationRecord.resultCount} 个神煞，耗时 ${calculationRecord.duration}ms`);
      
      return results;
      
    } catch (error) {
      console.error('❌ 统一神煞计算失败:', error);
      
      const errorRecord = {
        source: source,
        timestamp: new Date().toISOString(),
        error: error.message,
        success: false
      };
      
      this.calculationHistory.push(errorRecord);
      throw error;
      
    } finally {
      // 释放计算锁
      this.releaseCalculationLock();
    }
  }

  /**
   * 📊 检查数据源一致性
   */
  checkDataSourceConsistency() {
    const recentCalculations = this.calculationHistory.slice(-10);
    const sources = [...new Set(recentCalculations.map(calc => calc.source))];
    
    if (sources.length === 1 && sources[0] === 'UNIFIED_SYSTEM') {
      console.log('✅ 数据源一致性检查通过：单一统一系统');
      return { consistent: true, sources: sources };
    } else {
      console.warn('⚠️ 数据源一致性检查失败：发现多个计算源', sources);
      return { consistent: false, sources: sources };
    }
  }

  /**
   * 🧹 清理不一致的计算历史
   */
  cleanupInconsistentHistory() {
    const beforeCount = this.calculationHistory.length;
    
    // 只保留统一系统的计算记录
    this.calculationHistory = this.calculationHistory.filter(
      record => record.source === 'UNIFIED_SYSTEM'
    );
    
    const afterCount = this.calculationHistory.length;
    const cleanedCount = beforeCount - afterCount;
    
    if (cleanedCount > 0) {
      console.log(`🧹 清理了 ${cleanedCount} 条不一致的计算历史记录`);
    }
    
    return { cleaned: cleanedCount, remaining: afterCount };
  }

  /**
   * 📈 生成架构状态报告
   */
  generateArchitectureReport() {
    const consistencyCheck = this.validateArchitectureConsistency();
    const dataSourceCheck = this.checkDataSourceConsistency();
    
    const report = {
      timestamp: new Date().toISOString(),
      architectureStatus: this.architectureStatus,
      consistencyIssues: consistencyCheck.issues,
      dataSourceConsistent: dataSourceCheck.consistent,
      activeSources: dataSourceCheck.sources,
      calculationHistory: this.calculationHistory.slice(-5), // 最近5次计算
      recommendations: []
    };
    
    // 生成建议
    if (!dataSourceCheck.consistent) {
      report.recommendations.push('建议清理多余的计算源，统一使用单一计算入口');
    }
    
    if (consistencyCheck.issues.length > 0) {
      report.recommendations.push('建议修复架构一致性问题，删除重复的计算系统');
    }
    
    if (this.architectureStatus === 'UNIFIED' && dataSourceCheck.consistent) {
      report.recommendations.push('架构状态良好，继续保持统一计算模式');
    }
    
    return report;
  }

  /**
   * 🔧 重置架构管理器
   */
  reset() {
    this.calculationLock = false;
    this.currentCalculationSource = null;
    this.calculationHistory = [];
    this.architectureStatus = 'UNIFIED';
    console.log('🔧 神煞架构管理器已重置');
  }
}

// 创建全局单例
const shenshaArchitectureManager = new ShenshaArchitectureManager();

module.exports = shenshaArchitectureManager;
