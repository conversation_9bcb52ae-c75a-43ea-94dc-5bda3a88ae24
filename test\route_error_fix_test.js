/**
 * 路由错误修复测试
 * 验证__route__错误是否已解决
 */

const fs = require('fs');
const path = require('path');

function testRouteErrorFix() {
  console.log('🔍 测试路由错误修复');
  
  const results = {
    appJsFixed: false,
    divinationResultFixed: false,
    baziResultFixed: false,
    safeRouteMethod: false,
    totalIssues: 0,
    fixedIssues: 0
  };
  
  // 1. 检查app.js中的修复
  console.log('\n📄 检查app.js中的路由修复:');
  try {
    const appContent = fs.readFileSync(path.join(__dirname, '../app.js'), 'utf8');
    
    // 检查是否有安全路由获取方法
    const hasSafeRouteMethod = appContent.includes('_getSafeRoute');
    results.safeRouteMethod = hasSafeRouteMethod;
    console.log(`✅ 安全路由获取方法: ${hasSafeRouteMethod ? '已添加' : '缺失'}`);
    
    // 检查路由修复逻辑
    const hasImprovedFix = appContent.includes('this.globalData.__route__');
    results.appJsFixed = hasImprovedFix;
    console.log(`✅ 改进的路由修复: ${hasImprovedFix ? '已实现' : '缺失'}`);
    
  } catch (error) {
    console.log(`❌ 检查app.js失败: ${error.message}`);
  }
  
  // 2. 检查divination-result页面
  console.log('\n📄 检查divination-result页面:');
  try {
    const divinationContent = fs.readFileSync(path.join(__dirname, '../pages/divination-result/index.js'), 'utf8');
    
    // 检查是否移除了直接的__route__使用
    const hasDirectRouteUsage = divinationContent.includes('typeof __route__');
    results.divinationResultFixed = !hasDirectRouteUsage;
    console.log(`✅ 移除直接__route__使用: ${!hasDirectRouteUsage ? '已移除' : '仍存在'}`);
    
    // 检查是否有安全的路由处理
    const hasSafeRouteHandling = divinationContent.includes('this.route = currentRoute');
    console.log(`✅ 安全路由处理: ${hasSafeRouteHandling ? '已添加' : '缺失'}`);
    
  } catch (error) {
    console.log(`❌ 检查divination-result失败: ${error.message}`);
  }
  
  // 3. 检查bazi-result页面
  console.log('\n📄 检查bazi-result页面:');
  try {
    const baziContent = fs.readFileSync(path.join(__dirname, '../pages/bazi-result/index.js'), 'utf8');
    
    // 检查getShenshaCalculator方法的安全性
    const hasPageCheck = baziContent.includes('if (page)');
    const hasTryCatchRoute = baziContent.includes('try {') && baziContent.includes('pageRoute = page.route');
    const hasRouteNullCheck = baziContent.includes('if (pageRoute && pageRoute.includes');
    results.baziResultFixed = hasPageCheck && hasTryCatchRoute && hasRouteNullCheck;
    console.log(`✅ 安全的路由访问: ${results.baziResultFixed ? '已实现' : '缺失'}`);
    console.log(`  - 页面检查: ${hasPageCheck ? '✓' : '✗'}`);
    console.log(`  - try-catch路由: ${hasTryCatchRoute ? '✓' : '✗'}`);
    console.log(`  - 路由空值检查: ${hasRouteNullCheck ? '✓' : '✗'}`);

    // 检查是否使用了安全路由获取方法
    const usesSafeRouteMethod = baziContent.includes('app._getSafeRoute');
    console.log(`✅ 使用安全路由方法: ${usesSafeRouteMethod ? '是' : '否'}`);
    
  } catch (error) {
    console.log(`❌ 检查bazi-result失败: ${error.message}`);
  }
  
  // 4. 搜索所有仍然直接使用__route__的文件
  console.log('\n📄 搜索剩余的__route__直接使用:');
  try {
    const searchDirectories = [
      path.join(__dirname, '../pages'),
      path.join(__dirname, '../utils')
    ];
    
    let remainingIssues = [];
    
    function searchInDirectory(dir) {
      if (!fs.existsSync(dir)) return;
      
      const items = fs.readdirSync(dir);
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          searchInDirectory(fullPath);
        } else if (item.endsWith('.js')) {
          try {
            const content = fs.readFileSync(fullPath, 'utf8');
            if (content.includes('typeof __route__') || content.includes('__route__ ===')) {
              remainingIssues.push(fullPath.replace(__dirname + '/../', ''));
            }
          } catch (e) {
            // 忽略读取错误
          }
        }
      });
    }
    
    searchDirectories.forEach(dir => searchInDirectory(dir));
    
    results.totalIssues = remainingIssues.length;
    console.log(`发现剩余问题: ${remainingIssues.length}个`);
    if (remainingIssues.length > 0) {
      remainingIssues.forEach(file => {
        console.log(`  ❌ ${file}`);
      });
    } else {
      console.log(`✅ 未发现剩余的直接__route__使用`);
    }
    
  } catch (error) {
    console.log(`❌ 搜索失败: ${error.message}`);
  }
  
  // 5. 总结修复状态
  console.log('\n🎯 修复状态总结:');
  results.fixedIssues = [
    results.appJsFixed,
    results.divinationResultFixed,
    results.baziResultFixed,
    results.safeRouteMethod
  ].filter(Boolean).length;
  
  console.log(`${results.appJsFixed ? '✅' : '❌'} app.js路由修复`);
  console.log(`${results.divinationResultFixed ? '✅' : '❌'} divination-result页面修复`);
  console.log(`${results.baziResultFixed ? '✅' : '❌'} bazi-result页面修复`);
  console.log(`${results.safeRouteMethod ? '✅' : '❌'} 安全路由获取方法`);
  console.log(`${results.totalIssues === 0 ? '✅' : '❌'} 无剩余直接__route__使用`);
  
  const allFixed = results.fixedIssues === 4 && results.totalIssues === 0;
  
  if (allFixed) {
    console.log('\n🎉 所有路由错误都已修复！');
    console.log('用户应该不再看到 "__route__ is not defined" 错误。');
  } else {
    console.log('\n⚠️ 仍有部分问题需要解决');
    console.log('建议检查剩余的问题文件并应用相同的修复模式。');
  }
  
  return allFixed;
}

// 运行测试
if (require.main === module) {
  testRouteErrorFix();
}

module.exports = { testRouteErrorFix };
