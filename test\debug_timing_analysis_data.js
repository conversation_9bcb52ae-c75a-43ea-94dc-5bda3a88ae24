/**
 * 调试应期分析数据结构问题
 * 找出为什么前端显示的是硬编码数据而不是真实计算结果
 */

// 模拟微信小程序环境
global.wx = {
  getStorageSync: () => ({}),
  setStorageSync: () => {}
};

// 模拟应期分析的真实计算结果（基于日志信息）
const mockProfessionalTimingResults = {
  marriage: {
    threshold_status: 'analysis_completed',
    energy_deficit: {
      actual: 65.2,
      required: 75.0,
      met: false,
      deficit: 9.8
    },
    best_year_display: '2026年 (丙午)',
    confidence_score: 78.5,
    raw_analysis: {
      energy_analysis: {
        threshold_results: {
          wood_energy: { percentage: 68.0, required: 0.7, met: false },
          fire_energy: { percentage: 72.0, required: 0.8, met: false },
          earth_energy: { percentage: 55.0, required: 0.6, met: true }
        }
      }
    }
  },
  promotion: {
    threshold_status: 'analysis_completed',
    energy_analysis: {
      actual: 82.3,
      required: 70.0,
      met: true
    },
    best_year_display: '2025年 (乙巳)',
    confidence_score: 85.2
  },
  wealth: {
    threshold_status: 'age_not_met',
    reason: '年龄不符合财运分析条件'
  },
  childbirth: {
    threshold_status: 'analysis_completed',
    energy_deficit: {
      actual: 45.8,
      required: 60.0,
      met: false,
      deficit: 14.2
    },
    best_year_display: '2027年 (丁未)',
    confidence_score: 62.3
  }
};

// 模拟页面方法
function createMockPage() {
  return {
    data: {},
    setData: function(data) {
      Object.assign(this.data, data);
      console.log('📊 页面数据更新:', Object.keys(data));
    },
    
    // 模拟能量阈值提取方法
    extractEnergyThresholdsForUI: function(professionalResults) {
      console.log('🔍 应期分析数据结构调试:');
      console.log('  - professionalResults类型:', typeof professionalResults);
      console.log('  - professionalResults键:', Object.keys(professionalResults || {}));
      
      Object.keys(professionalResults || {}).forEach(key => {
        const result = professionalResults[key];
        console.log(`  - ${key}数据结构:`, {
          type: typeof result,
          keys: result ? Object.keys(result) : [],
          hasEnergyDeficit: !!(result && result.energy_deficit),
          hasEnergyAnalysis: !!(result && result.energy_analysis),
          hasRawAnalysis: !!(result && result.raw_analysis)
        });
      });
      
      const thresholds = {
        marriage_current_energy: 0,
        marriage_required_threshold: 0,
        marriage_met: false,
        marriage_estimated_year: '',
        promotion_current_energy: 0,
        promotion_required_threshold: 0,
        promotion_met: false,
        promotion_estimated_year: '',
        childbirth_current_energy: 0,
        childbirth_required_threshold: 0,
        childbirth_met: false,
        childbirth_estimated_year: '',
        wealth_current_energy: 0,
        wealth_required_threshold: 0,
        wealth_met: false,
        wealth_estimated_year: ''
      };

      // 从原始分析结果中提取能量阈值数据
      Object.keys(professionalResults).forEach(eventType => {
        const result = professionalResults[eventType];

        // 检查是否因年龄不符而无法分析
        if (result && result.threshold_status === 'age_not_met') {
          console.log(`⚠️ ${eventType}: 年龄不符，跳过分析`);
          thresholds[`${eventType}_current_energy`] = 0;
          thresholds[`${eventType}_required_threshold`] = 0;
          thresholds[`${eventType}_met`] = false;
          thresholds[`${eventType}_estimated_year`] = '';
          return;
        }

        if (result) {
          let userCurrentEnergy = 0;
          let requiredThreshold = 0;
          let actuallyMet = false;

          // 处理新的数据结构：energy_deficit 或 energy_analysis
          if (result.energy_deficit) {
            userCurrentEnergy = parseFloat(result.energy_deficit.actual) || 0;
            requiredThreshold = parseFloat(result.energy_deficit.required) || 0;
            actuallyMet = result.energy_deficit.met || false;

            console.log(`✅ ${eventType} energy_deficit数据: 用户${userCurrentEnergy}% / 所需${requiredThreshold}% = ${actuallyMet ? '达标' : '未达标'}`);
          } else if (result.energy_analysis) {
            userCurrentEnergy = parseFloat(result.energy_analysis.actual) || 0;
            requiredThreshold = parseFloat(result.energy_analysis.required) || 0;
            actuallyMet = result.energy_analysis.met || false;

            console.log(`✅ ${eventType} energy_analysis数据: 用户${userCurrentEnergy}% / 所需${requiredThreshold}% = ${actuallyMet ? '达标' : '未达标'}`);
          } else {
            console.warn(`⚠️ ${eventType} 无法找到能量数据，使用默认值`);
          }

          // 确保数值在合理范围内
          const clampedUserEnergy = Math.min(Math.max(userCurrentEnergy, 0), 100);
          const clampedRequiredThreshold = Math.min(Math.max(requiredThreshold, 0), 100);

          thresholds[`${eventType}_current_energy`] = Math.round(clampedUserEnergy * 10) / 10;
          thresholds[`${eventType}_required_threshold`] = Math.round(clampedRequiredThreshold * 10) / 10;
          thresholds[`${eventType}_met`] = actuallyMet;

          // 设置预计年份
          if (result.best_year_display) {
            thresholds[`${eventType}_estimated_year`] = result.best_year_display;
          }
        }
      });

      console.log('🎯 提取的能量阈值数据:', thresholds);
      return thresholds;
    },
    
    // 模拟三重引动提取方法
    extractTripleActivationForUI: function(professionalResults) {
      console.log('🔍 三重引动数据结构调试:');
      console.log('  - professionalResults:', professionalResults);
      
      const activation = {
        star_activation: '年龄阶段分析中...',
        palace_activation: '年龄阶段分析中...',
        shensha_activation: '年龄阶段分析中...',
        marriage_confidence: 0,
        promotion_confidence: 0,
        childbirth_confidence: 0,
        wealth_confidence: 0
      };

      // 从真实数据中提取置信度
      Object.keys(professionalResults).forEach(eventType => {
        const result = professionalResults[eventType];
        if (result && result.confidence_score) {
          activation[`${eventType}_confidence`] = Math.round(result.confidence_score);
        }
      });

      // 生成真实的引动描述
      const hasMarriageData = professionalResults.marriage && professionalResults.marriage.best_year_display;
      const hasPromotionData = professionalResults.promotion && professionalResults.promotion.best_year_display;
      
      if (hasMarriageData || hasPromotionData) {
        activation.star_activation = '天喜星、红鸾星引动，感情运势上升';
        activation.palace_activation = '夫妻宫得力，婚姻宫位活跃';
        activation.shensha_activation = '桃花煞化解，正缘神煞显现';
      }

      console.log('🎯 提取的三重引动数据:', activation);
      return activation;
    }
  };
}

// 测试函数
function debugTimingAnalysisData() {
  console.log('🧪 ===== 应期分析数据结构调试 =====\n');
  
  const mockPage = createMockPage();
  
  console.log('📋 模拟的专业应期分析结果:');
  console.log(JSON.stringify(mockProfessionalTimingResults, null, 2));
  
  console.log('\n🧪 测试1: 提取能量阈值数据');
  const energyThresholds = mockPage.extractEnergyThresholdsForUI(mockProfessionalTimingResults);
  
  console.log('\n🧪 测试2: 提取三重引动数据');
  const tripleActivation = mockPage.extractTripleActivationForUI(mockProfessionalTimingResults);
  
  console.log('\n📊 数据提取结果分析:');
  
  // 分析能量阈值数据
  console.log('   能量阈值数据:');
  Object.keys(energyThresholds).forEach(key => {
    if (key.includes('_current_energy') || key.includes('_required_threshold')) {
      console.log(`     ${key}: ${energyThresholds[key]}`);
    }
  });
  
  // 分析三重引动数据
  console.log('   三重引动数据:');
  console.log(`     星曜引动: ${tripleActivation.star_activation}`);
  console.log(`     宫位引动: ${tripleActivation.palace_activation}`);
  console.log(`     神煞引动: ${tripleActivation.shensha_activation}`);
  
  // 分析置信度数据
  console.log('   置信度数据:');
  ['marriage', 'promotion', 'childbirth', 'wealth'].forEach(event => {
    const confidence = tripleActivation[`${event}_confidence`];
    console.log(`     ${event}: ${confidence}%`);
  });
  
  console.log('\n🎯 问题诊断:');
  
  // 验证1: 能量数据是否被正确提取
  const hasRealEnergyData = energyThresholds.marriage_current_energy > 0 || 
                           energyThresholds.promotion_current_energy > 0;
  console.log('   能量数据提取:', hasRealEnergyData ? '✅ 成功' : '❌ 失败');
  
  // 验证2: 置信度是否被正确提取
  const hasRealConfidenceData = tripleActivation.marriage_confidence > 0 || 
                               tripleActivation.promotion_confidence > 0;
  console.log('   置信度提取:', hasRealConfidenceData ? '✅ 成功' : '❌ 失败');
  
  // 验证3: 引动描述是否是真实的
  const hasRealActivationData = !tripleActivation.star_activation.includes('年龄阶段分析中');
  console.log('   引动描述生成:', hasRealActivationData ? '✅ 成功' : '❌ 失败');
  
  // 验证4: 预计年份是否被正确设置
  const hasEstimatedYears = energyThresholds.marriage_estimated_year || 
                           energyThresholds.promotion_estimated_year;
  console.log('   预计年份设置:', hasEstimatedYears ? '✅ 成功' : '❌ 失败');
  
  console.log('\n🎉 调试总结:');
  const successCount = [hasRealEnergyData, hasRealConfidenceData, hasRealActivationData, hasEstimatedYears].filter(Boolean).length;
  console.log(`   成功项目: ${successCount}/4`);
  
  if (successCount >= 3) {
    console.log('✅ 数据提取逻辑正常，问题可能在于：');
    console.log('   1. 实际的应期分析结果数据结构与预期不符');
    console.log('   2. 数据传递过程中丢失');
    console.log('   3. 前端页面没有正确绑定数据');
  } else {
    console.log('❌ 数据提取逻辑有问题，需要修复：');
    console.log('   1. 检查数据结构匹配');
    console.log('   2. 完善数据提取逻辑');
    console.log('   3. 添加更多的数据源支持');
  }
  
  return {
    success: successCount >= 3,
    energyThresholds,
    tripleActivation,
    details: {
      hasRealEnergyData,
      hasRealConfidenceData,
      hasRealActivationData,
      hasEstimatedYears
    }
  };
}

// 运行调试
debugTimingAnalysisData();
