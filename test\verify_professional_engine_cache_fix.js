/**
 * 验证专业级引擎缓存修复效果
 * 测试是否成功清除了专业级引擎的缓存
 */

// 模拟专业级五行引擎
class MockProfessionalWuxingEngine {
  constructor() {
    this.calculationCache = new Map();
    this.cacheStats = {
      hits: 0,
      misses: 0,
      totalRequests: 0
    };
    
    // 模拟已有的缓存数据（问题数据）
    this.calculationCache.set('乙亥-辛巳-庚子-壬午', {
      result: {
        wood: 50, fire: 50, earth: 50, metal: 50, water: 50,
        algorithm: '专业级三层权重模型',
        version: 'V4.0 - 基于《五行计算.txt》文档标准'
      },
      timestamp: Date.now() - 1000 // 1秒前的缓存
    });
    
    console.log('🔧 模拟专业级引擎初始化，已有缓存数据');
  }

  generateCacheKey(fourPillars) {
    return fourPillars.map(pillar => `${pillar.gan}${pillar.zhi}`).join('-');
  }

  getCachedResult(cacheKey) {
    const cached = this.calculationCache.get(cacheKey);
    if (cached) {
      this.cacheStats.hits++;
      console.log(`🚀 缓存命中: ${cacheKey}`);
      return cached.result;
    }
    this.cacheStats.misses++;
    return null;
  }

  setCachedResult(cacheKey, result) {
    this.calculationCache.set(cacheKey, {
      result: result,
      timestamp: Date.now()
    });
  }

  clearCache() {
    console.log('🗑️ 清除专业级引擎缓存');
    this.calculationCache.clear();
    this.cacheStats = {
      hits: 0,
      misses: 0,
      totalRequests: 0
    };
  }

  generateDetailedReport(fourPillars) {
    this.cacheStats.totalRequests++;
    
    const cacheKey = this.generateCacheKey(fourPillars);
    
    // 尝试从缓存获取结果
    const cachedResult = this.getCachedResult(cacheKey);
    if (cachedResult) {
      return cachedResult;
    }

    console.log(`🔄 计算新结果: ${cacheKey}`);
    
    // 模拟真实计算（个性化结果）
    const result = {
      wood: 48, fire: 28, earth: 56, metal: 35, water: 12,
      algorithm: '专业级三层权重模型',
      version: 'V4.0 - 基于《五行计算.txt》文档标准',
      calculationTime: new Date().toISOString()
    };
    
    this.setCachedResult(cacheKey, result);
    return result;
  }

  getCacheStats() {
    return {
      ...this.cacheStats,
      cacheSize: this.calculationCache.size
    };
  }
}

// 模拟统一五行计算器
class MockUnifiedWuxingCalculator {
  constructor() {
    this.cache = new Map();
    this.engine = new MockProfessionalWuxingEngine();
    this.initialized = true;
    this.version = '1.0.0';
  }

  clearCache() {
    this.cache.clear();
    console.log('🗑️ 已清除统一计算器缓存');
  }

  calculate(baziData, options = {}) {
    console.log('🎯 开始统一五行计算');
    
    // 🔧 强制清除所有缓存（包括专业级引擎缓存）
    this.cache.clear();
    if (this.engine && typeof this.engine.clearCache === 'function') {
      this.engine.clearCache();
      console.log('🗑️ 已清除专业级引擎缓存');
    }
    console.log('🗑️ 已清除所有缓存，强制重新计算');
    
    console.log('🔍 引擎状态检查:');
    console.log('   - initialized:', this.initialized);
    console.log('   - engine:', !!this.engine);
    console.log('   - version:', this.version);

    if (this.initialized && this.engine) {
      console.log('✅ 使用专业级引擎计算');
      return this.calculateWithProfessionalEngine(baziData);
    } else {
      console.log('⚠️ 专业级引擎不可用，使用降级计算');
      return this.calculateWithFallback(baziData);
    }
  }

  calculateWithProfessionalEngine(baziData) {
    console.log('🔄 使用专业级引擎计算...');
    
    // 转换八字数据格式
    const fourPillars = [
      { gan: baziData.year.gan, zhi: baziData.year.zhi },
      { gan: baziData.month.gan, zhi: baziData.month.zhi },
      { gan: baziData.day.gan, zhi: baziData.day.zhi },
      { gan: baziData.hour.gan, zhi: baziData.hour.zhi }
    ];
    
    const result = this.engine.generateDetailedReport(fourPillars);
    
    console.log('🎯 计算结果预览:', {
      wood: result.wood,
      fire: result.fire,
      earth: result.earth,
      metal: result.metal,
      water: result.water
    });
    
    return result;
  }

  calculateWithFallback(baziData) {
    // 降级计算逻辑
    return {
      wood: 45, fire: 35, earth: 55, metal: 40, water: 25,
      algorithm: '降级计算模式',
      version: '1.0.0'
    };
  }
}

// 测试函数
function testProfessionalEngineCacheFix() {
  console.log('🧪 ===== 专业级引擎缓存修复测试 =====\n');
  
  // 测试用例：乙亥年 辛巳月 庚子日 壬午时
  const testBazi = {
    year: { gan: '乙', zhi: '亥' },
    month: { gan: '辛', zhi: '巳' },
    day: { gan: '庚', zhi: '子' },
    hour: { gan: '壬', zhi: '午' }
  };
  
  console.log('📋 测试八字:', testBazi);
  console.log('   完整八字: 乙亥年 辛巳月 庚子日 壬午时\n');
  
  // 创建计算器实例
  const calculator = new MockUnifiedWuxingCalculator();
  
  console.log('🧪 测试1: 检查初始缓存状态');
  const initialStats = calculator.engine.getCacheStats();
  console.log('   初始缓存状态:', initialStats);
  console.log('   是否有缓存数据:', initialStats.cacheSize > 0 ? '是' : '否');
  
  console.log('\n🧪 测试2: 第一次计算（应该使用缓存）');
  const firstResult = calculator.calculate(testBazi);
  const afterFirstStats = calculator.engine.getCacheStats();
  
  console.log('\n🧪 测试3: 第二次计算（缓存已清除，应该重新计算）');
  const secondResult = calculator.calculate(testBazi);
  const afterSecondStats = calculator.engine.getCacheStats();
  
  console.log('\n📊 测试结果分析:');
  console.log('   第一次计算结果:', {
    wood: firstResult.wood,
    fire: firstResult.fire,
    earth: firstResult.earth,
    metal: firstResult.metal,
    water: firstResult.water
  });
  
  console.log('   第二次计算结果:', {
    wood: secondResult.wood,
    fire: secondResult.fire,
    earth: secondResult.earth,
    metal: secondResult.metal,
    water: secondResult.water
  });
  
  console.log('\n🎯 缓存修复验证:');
  
  // 验证1: 缓存是否被清除
  const cacheCleared = afterFirstStats.cacheSize === 0;
  console.log('   缓存清除:', cacheCleared ? '✅ 成功' : '❌ 失败');
  
  // 验证2: 是否产生了新的计算结果
  const isNewCalculation = firstResult.calculationTime !== undefined;
  console.log('   新计算生成:', isNewCalculation ? '✅ 成功' : '❌ 失败');
  
  // 验证3: 结果是否不再是固定的50
  const isNotFixedFifty = !(firstResult.wood === 50 && firstResult.fire === 50 && 
                           firstResult.earth === 50 && firstResult.metal === 50 && 
                           firstResult.water === 50);
  console.log('   摆脱固定值:', isNotFixedFifty ? '✅ 成功' : '❌ 失败');
  
  // 验证4: 结果是否个性化
  const values = [firstResult.wood, firstResult.fire, firstResult.earth, firstResult.metal, firstResult.water];
  const isPersonalized = !values.every(v => v === values[0]);
  console.log('   结果个性化:', isPersonalized ? '✅ 成功' : '❌ 失败');
  
  console.log('\n🎉 修复效果总结:');
  const successCount = [cacheCleared, isNewCalculation, isNotFixedFifty, isPersonalized].filter(Boolean).length;
  console.log(`   成功项目: ${successCount}/4`);
  console.log(`   修复状态: ${successCount >= 3 ? '✅ 修复成功' : '❌ 需要进一步修复'}`);
  
  if (successCount >= 3) {
    console.log('\n✅ 专业级引擎缓存修复成功！');
    console.log('💡 现在应该显示：');
    console.log('   - 个性化的五行分布');
    console.log('   - 基于真实计算的结果');
    console.log('   - 不再是固定的50平均值');
  } else {
    console.log('\n❌ 修复不完整，需要进一步调试');
    console.log('💡 可能的问题：');
    console.log('   - 专业级引擎缓存未完全清除');
    console.log('   - 计算逻辑仍有问题');
    console.log('   - 数据传递过程中被重置');
  }
  
  return {
    success: successCount >= 3,
    details: {
      cacheCleared,
      isNewCalculation,
      isNotFixedFifty,
      isPersonalized
    },
    results: {
      first: firstResult,
      second: secondResult
    }
  };
}

// 运行测试
testProfessionalEngineCacheFix();
