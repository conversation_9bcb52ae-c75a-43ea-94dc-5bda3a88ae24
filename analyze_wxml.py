#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WXML标签平衡分析工具
系统性分析WXML文件中的标签配对情况，识别未闭合的标签
"""

import re
import sys
from typing import List, Tuple, Dict

def analyze_wxml_structure(file_path: str) -> Dict:
    """分析WXML文件的标签结构"""

    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    lines = content.split('\n')

    # 标签栈，用于跟踪嵌套结构
    tag_stack = []

    # 问题记录
    issues = []

    # 统计信息
    stats = {
        'total_lines': len(lines),
        'view_tags': 0,
        'scroll_view_tags': 0,
        'conditional_blocks': 0,
        'loop_blocks': 0
    }

    # 正则表达式模式
    patterns = {
        'opening_tag': r'<(view|scroll-view)(?:\s[^>]*)?(?<!/)>',
        'closing_tag': r'</(view|scroll-view)>',
        'self_closing': r'<(view|scroll-view)(?:\s[^>]*)?/>',
        'conditional': r'wx:(if|elif|else)',
        'loop': r'wx:for'
    }

    print("🔍 详细分析过程:")

    for line_num, line in enumerate(lines, 1):
        original_line = line
        line = line.strip()

        # 跳过注释和空行
        if not line or line.startswith('<!--'):
            continue

        # 统计条件渲染和循环
        if re.search(patterns['conditional'], line):
            stats['conditional_blocks'] += 1
        if re.search(patterns['loop'], line):
            stats['loop_blocks'] += 1

        # 查找自闭合标签（不影响栈）
        self_closing_matches = re.findall(patterns['self_closing'], line)
        for tag in self_closing_matches:
            if tag == 'view':
                stats['view_tags'] += 1
            elif tag == 'scroll-view':
                stats['scroll_view_tags'] += 1

        # 查找开始标签
        opening_matches = re.findall(patterns['opening_tag'], line)
        for tag in opening_matches:
            tag_stack.append({
                'tag': tag,
                'line': line_num,
                'content': line
            })
            if tag == 'view':
                stats['view_tags'] += 1
            elif tag == 'scroll-view':
                stats['scroll_view_tags'] += 1
            print(f"  第{line_num}行: 开始标签 <{tag}> (栈深度: {len(tag_stack)})")

        # 查找结束标签
        closing_matches = re.findall(patterns['closing_tag'], line)
        for tag in closing_matches:
            print(f"  第{line_num}行: 闭合标签 </{tag}> (当前栈深度: {len(tag_stack)})")
            if not tag_stack:
                issues.append({
                    'type': 'unexpected_closing',
                    'tag': tag,
                    'line': line_num,
                    'message': f'第{line_num}行：意外的闭合标签 </{tag}>，没有对应的开始标签'
                })
                print(f"    ❌ 栈为空，意外的闭合标签")
            else:
                last_tag = tag_stack.pop()
                if last_tag['tag'] != tag:
                    issues.append({
                        'type': 'tag_mismatch',
                        'tag': tag,
                        'line': line_num,
                        'expected': last_tag['tag'],
                        'message': f'第{line_num}行：标签不匹配，期望 </{last_tag["tag"]}>，实际 </{tag}>'
                    })
                    print(f"    ❌ 标签不匹配，期望 </{last_tag['tag']}>，实际 </{tag}>")
                else:
                    print(f"    ✅ 正确匹配 </{tag}> (栈深度: {len(tag_stack)})")

    # 检查未闭合的标签
    for unclosed_tag in tag_stack:
        issues.append({
            'type': 'unclosed_tag',
            'tag': unclosed_tag['tag'],
            'line': unclosed_tag['line'],
            'message': f'第{unclosed_tag["line"]}行：未闭合的标签 <{unclosed_tag["tag"]}>'
        })

    return {
        'stats': stats,
        'issues': issues,
        'unclosed_count': len(tag_stack),
        'tag_stack': tag_stack
    }

def print_analysis_report(analysis: Dict):
    """打印分析报告"""
    
    print("=" * 60)
    print("WXML 标签结构分析报告")
    print("=" * 60)
    
    stats = analysis['stats']
    print(f"\n📊 统计信息:")
    print(f"  总行数: {stats['total_lines']}")
    print(f"  view标签数量: {stats['view_tags']}")
    print(f"  scroll-view标签数量: {stats['scroll_view_tags']}")
    print(f"  条件渲染块: {stats['conditional_blocks']}")
    print(f"  循环渲染块: {stats['loop_blocks']}")
    
    issues = analysis['issues']
    print(f"\n🔍 发现的问题: {len(issues)}个")
    
    if not issues:
        print("  ✅ 没有发现标签结构问题！")
        return
    
    # 按问题类型分组
    issue_types = {}
    for issue in issues:
        issue_type = issue['type']
        if issue_type not in issue_types:
            issue_types[issue_type] = []
        issue_types[issue_type].append(issue)
    
    for issue_type, issue_list in issue_types.items():
        print(f"\n  📋 {issue_type} ({len(issue_list)}个):")
        for issue in issue_list:
            print(f"    ❌ {issue['message']}")
    
    print(f"\n🔧 修复建议:")
    if analysis['unclosed_count'] > 0:
        print(f"  1. 有 {analysis['unclosed_count']} 个未闭合的标签需要添加对应的闭合标签")
    
    print(f"  2. 重点检查以下区域:")
    print(f"     - 条件渲染块 (wx:if/wx:elif/wx:else)")
    print(f"     - 循环渲染块 (wx:for)")
    print(f"     - 复杂嵌套的卡片结构")
    print(f"     - 标签页面板的边界")

if __name__ == "__main__":
    file_path = "pages/bazi-result/index.wxml"
    
    try:
        analysis = analyze_wxml_structure(file_path)
        print_analysis_report(analysis)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {file_path}")
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
