/**
 * 验证应期分析阈值计算修复
 * 基于真实日志数据测试阈值提取是否正确
 */

// 基于真实日志的数据结构
const realTimingResults = {
  marriage: {
    threshold_status: "not_met",
    message: "当前能量阈值未达标，时机尚未成熟",
    confidence: 0.3,
    energy_deficit: {
      actual: 23.64,
      percentage: "23.6%",
      required: 30,
      met: false,
      completion_ratio: "23.6% / 30.0%"
    },
    estimated_year: null
  },
  promotion: {
    threshold_status: "not_met",
    message: "当前能量阈值未达标，时机尚未成熟",
    confidence: 0.3,
    energy_deficit: {
      actual: 46.272,
      percentage: "46.3%",
      required: 50,
      met: false,
      completion_ratio: "46.3% / 50.0%"
    },
    estimated_year: null
  },
  childbirth: {
    threshold_status: "met",
    best_year: "2027年5月",
    best_year_numeric: "2027.05",
    confidence: 0.65,
    energy_analysis: {
      actual: 28.71,
      percentage: "28.7%",
      required: 25,
      met: true,
      completion_ratio: "28.7% / 25.0%"
    }
  },
  wealth: {
    threshold_status: "not_met",
    message: "当前能量阈值未达标，时机尚未成熟",
    confidence: 0.3,
    energy_deficit: {
      actual: 18.04,
      percentage: "18.0%",
      required: 30,
      met: false,
      completion_ratio: "18.0% / 30.0%"
    },
    estimated_year: null
  }
};

// 修复后的提取方法
function extractEnergyThresholdsForUI_Fixed(professionalResults) {
  console.log('🔍 应期分析数据结构调试:');
  console.log('  - professionalResults类型:', typeof professionalResults);
  console.log('  - professionalResults键:', Object.keys(professionalResults || {}));
  
  const thresholds = {
    marriage_current_energy: 0,
    marriage_required_threshold: 0,
    marriage_met: false,
    marriage_estimated_year: '',
    promotion_current_energy: 0,
    promotion_required_threshold: 0,
    promotion_met: false,
    promotion_estimated_year: '',
    childbirth_current_energy: 0,
    childbirth_required_threshold: 0,
    childbirth_met: false,
    childbirth_estimated_year: '',
    wealth_current_energy: 0,
    wealth_required_threshold: 0,
    wealth_met: false,
    wealth_estimated_year: ''
  };

  // 从原始分析结果中提取能量阈值数据
  Object.keys(professionalResults).forEach(eventType => {
    const result = professionalResults[eventType];

    // 检查是否因年龄不符而无法分析
    if (result && result.threshold_status === 'age_not_met') {
      console.log(`⚠️ ${eventType}: 年龄不符，跳过分析`);
      return;
    }

    if (result) {
      let userCurrentEnergy = 0;
      let requiredThreshold = 0;
      let actuallyMet = false;

      if (result.energy_deficit) {
        // 🔧 修复：正确提取实际数据 - 未达标情况
        userCurrentEnergy = parseFloat(result.energy_deficit.actual) || 0;
        requiredThreshold = parseFloat(result.energy_deficit.required) || 0;
        actuallyMet = result.energy_deficit.met || false;

        console.log(`✅ ${eventType} energy_deficit数据: 用户${userCurrentEnergy}% / 所需${requiredThreshold}% = ${actuallyMet ? '达标' : '未达标'}`);
      } else if (result.energy_analysis) {
        // 🔧 修复：正确提取实际数据 - 达标情况
        userCurrentEnergy = parseFloat(result.energy_analysis.actual) || 0;
        requiredThreshold = parseFloat(result.energy_analysis.required) || 0;
        actuallyMet = result.energy_analysis.met || false;

        console.log(`✅ ${eventType} energy_analysis数据: 用户${userCurrentEnergy}% / 所需${requiredThreshold}% = ${actuallyMet ? '达标' : '未达标'}`);
      } else {
        console.warn(`⚠️ ${eventType} 无法找到能量数据，使用默认值`);
      }

      // 确保数值在合理范围内
      const clampedUserEnergy = Math.min(Math.max(userCurrentEnergy, 0), 100);
      const clampedRequiredThreshold = Math.min(Math.max(requiredThreshold, 0), 100);

      thresholds[`${eventType}_current_energy`] = Math.round(clampedUserEnergy * 10) / 10;
      thresholds[`${eventType}_required_threshold`] = Math.round(clampedRequiredThreshold * 10) / 10;
      thresholds[`${eventType}_met`] = actuallyMet;

      // 设置预计年份
      if (result.best_year) {
        thresholds[`${eventType}_estimated_year`] = result.best_year;
      } else if (result.estimated_year) {
        thresholds[`${eventType}_estimated_year`] = result.estimated_year;
      }
    }
  });

  console.log('🎯 提取的能量阈值数据:', thresholds);
  return thresholds;
}

// 修复前的错误方法（用于对比）
function extractEnergyThresholdsForUI_Broken(professionalResults) {
  const thresholds = {
    marriage_current_energy: 0,
    marriage_required_threshold: 0,
    marriage_met: false,
    promotion_current_energy: 0,
    promotion_required_threshold: 0,
    promotion_met: false,
    childbirth_current_energy: 0,
    childbirth_required_threshold: 0,
    childbirth_met: false,
    wealth_current_energy: 0,
    wealth_required_threshold: 0,
    wealth_met: false
  };

  Object.keys(professionalResults).forEach(eventType => {
    const result = professionalResults[eventType];
    if (result) {
      let userCurrentEnergy = 0;
      let requiredThreshold = 0;
      let actuallyMet = false;

      if (result.energy_deficit) {
        // ❌ 错误：使用percentage字段和错误的乘法
        userCurrentEnergy = parseFloat(result.energy_deficit.percentage) || 0;
        requiredThreshold = parseFloat(result.energy_deficit.required * 100) || 0;
        actuallyMet = result.energy_deficit.met || false;
      } else if (result.energy_analysis) {
        userCurrentEnergy = parseFloat(result.energy_analysis.percentage) || 0;
        requiredThreshold = parseFloat(result.energy_analysis.required * 100) || 0;
        actuallyMet = result.energy_analysis.met || false;
      }

      thresholds[`${eventType}_current_energy`] = Math.round(userCurrentEnergy * 10) / 10;
      thresholds[`${eventType}_required_threshold`] = Math.round(requiredThreshold * 10) / 10;
      thresholds[`${eventType}_met`] = actuallyMet;
    }
  });

  return thresholds;
}

// 测试函数
function testTimingThresholdFix() {
  console.log('🧪 ===== 应期分析阈值计算修复验证 =====\n');
  
  console.log('📋 真实日志数据:');
  console.log('  marriage: 23.64% / 30% (未达标)');
  console.log('  promotion: 46.3% / 50% (未达标)');
  console.log('  childbirth: 28.7% / 25% (达标)');
  console.log('  wealth: 18.0% / 30% (未达标)');
  
  console.log('\n🧪 测试1: 修复前的错误提取');
  const brokenResult = extractEnergyThresholdsForUI_Broken(realTimingResults);
  
  console.log('\n🧪 测试2: 修复后的正确提取');
  const fixedResult = extractEnergyThresholdsForUI_Fixed(realTimingResults);
  
  console.log('\n📊 对比分析:');
  
  const events = ['marriage', 'promotion', 'childbirth', 'wealth'];
  
  events.forEach(event => {
    const brokenCurrent = brokenResult[`${event}_current_energy`];
    const brokenRequired = brokenResult[`${event}_required_threshold`];
    const fixedCurrent = fixedResult[`${event}_current_energy`];
    const fixedRequired = fixedResult[`${event}_required_threshold`];
    
    console.log(`\n   ${event}:`);
    console.log(`     修复前: ${brokenCurrent}% / ${brokenRequired}%`);
    console.log(`     修复后: ${fixedCurrent}% / ${fixedRequired}%`);
    console.log(`     是否合理: ${fixedRequired <= 100 ? '✅' : '❌'}`);
  });
  
  console.log('\n🎯 修复验证:');
  
  // 验证1: 阈值不再超过100%
  const reasonableThresholds = events.every(event => 
    fixedResult[`${event}_required_threshold`] <= 100
  );
  console.log('   阈值合理性:', reasonableThresholds ? '✅ 成功' : '❌ 失败');
  
  // 验证2: 数据不再是0
  const hasRealData = events.some(event => 
    fixedResult[`${event}_current_energy`] > 0
  );
  console.log('   有真实数据:', hasRealData ? '✅ 成功' : '❌ 失败');
  
  // 验证3: 达标状态正确
  const correctStatus = fixedResult.childbirth_met === true && 
                       fixedResult.marriage_met === false;
  console.log('   达标状态正确:', correctStatus ? '✅ 成功' : '❌ 失败');
  
  // 验证4: 预计年份设置
  const hasEstimatedYear = fixedResult.childbirth_estimated_year === '2027年5月';
  console.log('   预计年份设置:', hasEstimatedYear ? '✅ 成功' : '❌ 失败');
  
  console.log('\n🎉 修复效果总结:');
  const successCount = [reasonableThresholds, hasRealData, correctStatus, hasEstimatedYear].filter(Boolean).length;
  console.log(`   成功项目: ${successCount}/4`);
  console.log(`   修复状态: ${successCount >= 3 ? '✅ 修复成功' : '❌ 需要进一步修复'}`);
  
  if (successCount >= 3) {
    console.log('\n✅ 应期分析阈值计算修复成功！');
    console.log('💡 现在前端应该显示：');
    console.log('   - 合理的阈值百分比（不超过100%）');
    console.log('   - 正确的达标状态');
    console.log('   - 真实的能量数据');
    console.log('   - 准确的预计年份');
    
    console.log('\n📊 预期前端显示效果：');
    console.log('   婚姻应期: 23.6% / 30% ⚠️ 能量阈值未达标，预计2026年达标');
    console.log('   升职应期: 46.3% / 50% ⚠️ 能量阈值未达标，预计2026年达标');
    console.log('   生育应期: 28.7% / 25% ✅ 达标 - 2027年5月');
    console.log('   财运应期: 18.0% / 30% ⚠️ 能量阈值未达标，预计2027年达标');
  } else {
    console.log('\n❌ 修复不完整，需要进一步调试');
  }
  
  return {
    success: successCount >= 3,
    fixedResult,
    brokenResult,
    details: {
      reasonableThresholds,
      hasRealData,
      correctStatus,
      hasEstimatedYear
    }
  };
}

// 运行测试
testTimingThresholdFix();
