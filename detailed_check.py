#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def detailed_check(file_path):
    """详细检查WXML文件的标签结构"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    # 检查第35-80行的结构
    print("检查第35-80行的tab导航结构：")
    for line_num in range(34, 80):
        if line_num < len(lines):
            line = lines[line_num]
            if '<view' in line or '</view>' in line:
                print(f"第{line_num+1}行: {line.strip()}")
    
    print("\n" + "="*50)
    
    # 检查第270-290行的结构
    print("检查第270-290行的天体图结构：")
    for line_num in range(269, 290):
        if line_num < len(lines):
            line = lines[line_num]
            if '<view' in line or '</view>' in line:
                print(f"第{line_num+1}行: {line.strip()}")

if __name__ == "__main__":
    detailed_check("pages/bazi-result/index.wxml")
