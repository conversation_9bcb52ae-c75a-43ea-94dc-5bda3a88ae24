/**
 * 🧪 前端历史名人库集成测试
 * 验证前端页面能否正确显示历史名人数据
 */

// 模拟微信小程序环境
global.wx = {
  getStorageSync: () => null,
  setStorageSync: () => {},
  request: () => {}
};

const ProfessionalTimingEngine = require('../utils/professional_timing_engine.js');

async function testFrontendCelebrityIntegration() {
  console.log('🧪 开始测试前端历史名人库集成...\n');

  try {
    // 创建专业应期引擎实例
    const timingEngine = new ProfessionalTimingEngine();

    // 测试用户数据（成年男性）
    const adultMaleUserData = {
      bazi: {
        year: { gan: '甲', zhi: '子' },
        month: { gan: '丙', zhi: '寅' },
        day: { gan: '戊', zhi: '午' },
        time: { gan: '庚', zhi: '申' }
      },
      gender: 'male',
      birthYear: 1990,
      currentYear: 2025
    };

    console.log('👨 测试成年男性用户历史验证...');
    
    // 执行历史验证
    const userAge = adultMaleUserData.currentYear - adultMaleUserData.birthYear;
    const historicalValidation = await timingEngine.performHistoricalValidation(
      adultMaleUserData.bazi,
      'marriage',
      adultMaleUserData.gender,
      userAge
    );
    
    console.log('📊 历史验证结果:');
    console.log(`- 找到相似名人: ${historicalValidation.similar_celebrities.length}位`);
    console.log(`- 数据库规模: ${historicalValidation.database_size}`);
    console.log(`- 验证标准: ${historicalValidation.verification_standard}`);
    console.log(`- 平均准确率: ${historicalValidation.average_accuracy}%`);
    
    if (historicalValidation.similar_celebrities.length > 0) {
      console.log('\n🎯 相似名人列表:');
      historicalValidation.similar_celebrities.forEach((celebrity, index) => {
        console.log(`${index + 1}. ${celebrity.name} (${celebrity.dynasty}) - 相似度: ${celebrity.similarity}%`);
        console.log(`   成就: ${celebrity.achievement}`);
        console.log(`   格局: ${celebrity.pattern}`);
        console.log(`   应期事件: ${celebrity.timing_event}`);
      });
    }

    // 测试用户数据（成年女性）
    const adultFemaleUserData = {
      bazi: {
        year: { gan: '乙', zhi: '丑' },
        month: { gan: '丁', zhi: '卯' },
        day: { gan: '己', zhi: '未' },
        time: { gan: '辛', zhi: '酉' }
      },
      gender: 'female',
      birthYear: 1985,
      currentYear: 2025
    };

    console.log('\n👩 测试成年女性用户历史验证...');
    
    // 执行历史验证
    const femaleUserAge = adultFemaleUserData.currentYear - adultFemaleUserData.birthYear;
    const femaleHistoricalValidation = await timingEngine.performHistoricalValidation(
      adultFemaleUserData.bazi,
      'marriage',
      adultFemaleUserData.gender,
      femaleUserAge
    );
    
    console.log('📊 历史验证结果:');
    console.log(`- 找到相似名人: ${femaleHistoricalValidation.similar_celebrities.length}位`);
    console.log(`- 数据库规模: ${femaleHistoricalValidation.database_size}`);
    console.log(`- 验证标准: ${femaleHistoricalValidation.verification_standard}`);
    console.log(`- 平均准确率: ${femaleHistoricalValidation.average_accuracy}%`);
    
    if (femaleHistoricalValidation.similar_celebrities.length > 0) {
      console.log('\n🎯 相似名人列表:');
      femaleHistoricalValidation.similar_celebrities.forEach((celebrity, index) => {
        console.log(`${index + 1}. ${celebrity.name} (${celebrity.dynasty}) - 相似度: ${celebrity.similarity}%`);
        console.log(`   成就: ${celebrity.achievement}`);
        console.log(`   格局: ${celebrity.pattern}`);
        console.log(`   应期事件: ${celebrity.timing_event}`);
      });
    }

    // 测试婴儿用户（应该没有历史验证数据）
    const babyUserData = {
      bazi: {
        year: { gan: '癸', zhi: '卯' },
        month: { gan: '甲', zhi: '寅' },
        day: { gan: '丙', zhi: '午' },
        time: { gan: '戊', zhi: '戌' }
      },
      gender: 'male',
      birthYear: 2023,
      currentYear: 2025
    };

    console.log('\n👶 测试婴儿用户历史验证...');
    
    // 执行历史验证
    const babyAge = babyUserData.currentYear - babyUserData.birthYear;
    const babyHistoricalValidation = await timingEngine.performHistoricalValidation(
      babyUserData.bazi,
      'marriage',
      babyUserData.gender,
      babyAge
    );
    
    console.log('📊 历史验证结果:');
    console.log(`- 找到相似名人: ${babyHistoricalValidation.similar_celebrities.length}位`);
    console.log(`- 数据库规模: ${babyHistoricalValidation.database_size}`);
    console.log(`- 验证标准: ${babyHistoricalValidation.verification_standard}`);
    console.log(`- 平均准确率: ${babyHistoricalValidation.average_accuracy}%`);

    // 验证测试结果
    console.log('\n🎯 测试结果验证:');
    
    const maleHasCelebrities = historicalValidation.similar_celebrities.length > 0;
    const femaleHasCelebrities = femaleHistoricalValidation.similar_celebrities.length > 0;
    const babyHasNoCelebrities = babyHistoricalValidation.similar_celebrities.length === 0;
    
    console.log(`✅ 成年男性找到名人: ${maleHasCelebrities ? '是' : '否'}`);
    console.log(`✅ 成年女性找到名人: ${femaleHasCelebrities ? '是' : '否'}`);
    console.log(`✅ 婴儿用户无名人数据: ${babyHasNoCelebrities ? '是' : '否'}`);

    if (maleHasCelebrities && femaleHasCelebrities && babyHasNoCelebrities) {
      console.log('\n🎉 前端历史名人库集成测试通过！');
      console.log('✅ 性别过滤正常工作');
      console.log('✅ 年龄验证正常工作');
      console.log('✅ 数据结构正确传递');
    } else {
      console.log('\n❌ 部分测试失败，需要进一步检查');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
testFrontendCelebrityIntegration();
