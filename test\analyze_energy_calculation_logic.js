/**
 * 分析用户能量计算逻辑的合理性
 * 检查能量计算是否存在设计缺陷
 */

// 模拟当前的能量计算逻辑
function analyzeEnergyCalculationLogic() {
  console.log('🧪 ===== 用户能量计算逻辑分析 =====\n');
  
  // 模拟五行数据（基于真实范围）
  const testElementEnergies = {
    金: 45.2,
    木: 23.8,
    水: 67.1,
    火: 34.6,
    土: 29.3
  };
  
  console.log('📋 输入五行数据:', testElementEnergies);
  console.log('   五行总和:', Object.values(testElementEnergies).reduce((sum, val) => sum + val, 0).toFixed(1));
  
  // 当前的算法逻辑分析
  console.log('\n🔍 当前算法逻辑分析:');
  
  // 婚姻能量计算
  const marriageCalculation = {
    spouseStarEnergy: (testElementEnergies.金 + testElementEnergies.火) * 0.3,
    palaceEnergy: testElementEnergies.水 * 0.2,
    constraintFactor: Math.min((testElementEnergies.木 + testElementEnergies.土) * 0.1, ((testElementEnergies.金 + testElementEnergies.火) * 0.3) * 0.3)
  };
  marriageCalculation.totalEnergy = Math.max(0, marriageCalculation.spouseStarEnergy + marriageCalculation.palaceEnergy - marriageCalculation.constraintFactor);
  
  console.log('\n  婚姻能量计算:');
  console.log(`    配偶星能量: (${testElementEnergies.金} + ${testElementEnergies.火}) × 0.3 = ${marriageCalculation.spouseStarEnergy.toFixed(1)}`);
  console.log(`    夫妻宫能量: ${testElementEnergies.水} × 0.2 = ${marriageCalculation.palaceEnergy.toFixed(1)}`);
  console.log(`    制约因素: ${marriageCalculation.constraintFactor.toFixed(1)}`);
  console.log(`    总能量: ${marriageCalculation.totalEnergy.toFixed(1)}%`);
  
  // 升职能量计算
  const promotionCalculation = {
    officialPower: (testElementEnergies.金 + testElementEnergies.水) * 0.25,
    sealPower: (testElementEnergies.水 + testElementEnergies.木) * 0.2,
    synergyBonus: 0,
    constraintFactor: 0
  };
  promotionCalculation.synergyBonus = Math.min(promotionCalculation.officialPower, promotionCalculation.sealPower) * 0.15;
  promotionCalculation.constraintFactor = Math.min((testElementEnergies.火 + testElementEnergies.木) * 0.08, promotionCalculation.officialPower * 0.4);
  promotionCalculation.totalEnergy = Math.max(0, promotionCalculation.officialPower + promotionCalculation.sealPower + promotionCalculation.synergyBonus - promotionCalculation.constraintFactor);
  
  console.log('\n  升职能量计算:');
  console.log(`    官星力量: (${testElementEnergies.金} + ${testElementEnergies.水}) × 0.25 = ${promotionCalculation.officialPower.toFixed(1)}`);
  console.log(`    印星力量: (${testElementEnergies.水} + ${testElementEnergies.木}) × 0.2 = ${promotionCalculation.sealPower.toFixed(1)}`);
  console.log(`    相生加成: ${promotionCalculation.synergyBonus.toFixed(1)}`);
  console.log(`    制约因素: ${promotionCalculation.constraintFactor.toFixed(1)}`);
  console.log(`    总能量: ${promotionCalculation.totalEnergy.toFixed(1)}%`);
  
  // 生育能量计算
  const childbirthCalculation = {
    foodInjuryEnergy: (testElementEnergies.水 + testElementEnergies.木) * 0.35,
    childrenPalaceEnergy: testElementEnergies.火 * 0.15,
    constraintFactor: 0
  };
  childbirthCalculation.constraintFactor = Math.max(0, (childbirthCalculation.foodInjuryEnergy - 25) * 0.2);
  childbirthCalculation.totalEnergy = Math.max(0, childbirthCalculation.foodInjuryEnergy + childbirthCalculation.childrenPalaceEnergy - childbirthCalculation.constraintFactor);
  
  console.log('\n  生育能量计算:');
  console.log(`    食伤能量: (${testElementEnergies.水} + ${testElementEnergies.木}) × 0.35 = ${childbirthCalculation.foodInjuryEnergy.toFixed(1)}`);
  console.log(`    子女宫能量: ${testElementEnergies.火} × 0.15 = ${childbirthCalculation.childrenPalaceEnergy.toFixed(1)}`);
  console.log(`    制约因素: ${childbirthCalculation.constraintFactor.toFixed(1)}`);
  console.log(`    总能量: ${childbirthCalculation.totalEnergy.toFixed(1)}%`);
  
  // 财运能量计算
  const wealthCalculation = {
    directWealthPower: (testElementEnergies.木 + testElementEnergies.火) * 0.25,
    treasuryPower: testElementEnergies.土 * 0.15,
    officialBonus: 0,
    wealthConstraint: 0
  };
  wealthCalculation.officialBonus = Math.min(((testElementEnergies.金 + testElementEnergies.水) * 0.25 + (testElementEnergies.水 + testElementEnergies.木) * 0.2) * 0.1, 15);
  const bodyStrength = (testElementEnergies.金 + testElementEnergies.水 + testElementEnergies.木) / 3;
  wealthCalculation.wealthConstraint = Math.max(0, (wealthCalculation.directWealthPower - bodyStrength) * 0.3);
  wealthCalculation.totalEnergy = Math.max(0, wealthCalculation.directWealthPower + wealthCalculation.treasuryPower + wealthCalculation.officialBonus - wealthCalculation.wealthConstraint);
  
  console.log('\n  财运能量计算:');
  console.log(`    财星力量: (${testElementEnergies.木} + ${testElementEnergies.火}) × 0.25 = ${wealthCalculation.directWealthPower.toFixed(1)}`);
  console.log(`    财库力量: ${testElementEnergies.土} × 0.15 = ${wealthCalculation.treasuryPower.toFixed(1)}`);
  console.log(`    官贵加成: ${wealthCalculation.officialBonus.toFixed(1)}`);
  console.log(`    身弱制约: ${wealthCalculation.wealthConstraint.toFixed(1)}`);
  console.log(`    总能量: ${wealthCalculation.totalEnergy.toFixed(1)}%`);
  
  // 问题分析
  console.log('\n🚨 问题分析:');
  
  const allEnergies = [
    marriageCalculation.totalEnergy,
    promotionCalculation.totalEnergy,
    childbirthCalculation.totalEnergy,
    wealthCalculation.totalEnergy
  ];
  
  const maxEnergy = Math.max(...allEnergies);
  const minEnergy = Math.min(...allEnergies);
  const avgEnergy = allEnergies.reduce((sum, e) => sum + e, 0) / allEnergies.length;
  
  console.log(`  能量范围: ${minEnergy.toFixed(1)}% - ${maxEnergy.toFixed(1)}%`);
  console.log(`  平均能量: ${avgEnergy.toFixed(1)}%`);
  
  // 问题1: 能量超过100%
  const hasEnergyOver100 = allEnergies.some(e => e > 100);
  console.log(`  能量超过100%: ${hasEnergyOver100 ? '❌ 是' : '✅ 否'}`);
  
  // 问题2: 能量计算缺乏统一标准
  const energyVariance = allEnergies.reduce((sum, e) => sum + Math.pow(e - avgEnergy, 2), 0) / allEnergies.length;
  const energyStdDev = Math.sqrt(energyVariance);
  console.log(`  能量分布标准差: ${energyStdDev.toFixed(1)} (过大表示缺乏统一标准)`);
  
  // 问题3: 权重系数分析
  console.log('\n🔍 权重系数分析:');
  console.log('  婚姻: 配偶星0.3 + 夫妻宫0.2 = 最大0.5倍五行值');
  console.log('  升职: 官星0.25 + 印星0.2 + 加成0.15 = 最大0.6倍五行值');
  console.log('  生育: 食伤0.35 + 子女宫0.15 = 最大0.5倍五行值');
  console.log('  财运: 财星0.25 + 财库0.15 + 加成0.1 = 最大0.5倍五行值');
  
  // 问题4: 与阈值的关联性
  const thresholds = { marriage: 15, promotion: 20, childbirth: 12, wealth: 18 };
  console.log('\n🎯 能量与阈值关联性分析:');
  
  const eventTypes = ['marriage', 'promotion', 'childbirth', 'wealth'];
  const calculations = [marriageCalculation, promotionCalculation, childbirthCalculation, wealthCalculation];
  
  eventTypes.forEach((eventType, index) => {
    const energy = calculations[index].totalEnergy;
    const threshold = thresholds[eventType];
    const ratio = energy / threshold;
    const met = energy >= threshold;
    
    console.log(`  ${eventType}: ${energy.toFixed(1)}% / ${threshold}% = ${ratio.toFixed(2)}倍 ${met ? '✅' : '❌'}`);
  });
  
  console.log('\n💡 设计缺陷总结:');
  
  const issues = [];
  
  if (hasEnergyOver100) {
    issues.push('能量值可能超过100%，不符合百分比概念');
  }
  
  if (energyStdDev > 20) {
    issues.push('不同维度能量计算缺乏统一标准');
  }
  
  if (maxEnergy / minEnergy > 3) {
    issues.push('能量分布极不均匀，可能存在计算偏差');
  }
  
  // 检查权重合理性
  const totalWeights = {
    marriage: 0.3 + 0.2, // 0.5
    promotion: 0.25 + 0.2 + 0.15, // 0.6
    childbirth: 0.35 + 0.15, // 0.5
    wealth: 0.25 + 0.15 + 0.1 // 0.5
  };
  
  const weightVariance = Object.values(totalWeights).reduce((sum, w, _, arr) => {
    const avg = arr.reduce((s, v) => s + v, 0) / arr.length;
    return sum + Math.pow(w - avg, 2);
  }, 0) / Object.values(totalWeights).length;
  
  if (Math.sqrt(weightVariance) > 0.05) {
    issues.push('不同维度的权重系数不一致');
  }
  
  console.log(`  发现问题: ${issues.length}个`);
  issues.forEach((issue, index) => {
    console.log(`    ${index + 1}. ${issue}`);
  });
  
  console.log('\n🔧 修正建议:');
  console.log('  1. 统一能量计算标准，确保结果在0-100%范围内');
  console.log('  2. 建立能量与阈值的直接关联关系');
  console.log('  3. 统一各维度的权重系数');
  console.log('  4. 基于五行平衡度计算能量，而不是简单加权');
  
  return {
    hasEnergyOver100,
    energyRange: [minEnergy, maxEnergy],
    avgEnergy,
    energyStdDev,
    issues: issues.length,
    needsRedesign: issues.length > 2
  };
}

// 运行分析
analyzeEnergyCalculationLogic();
