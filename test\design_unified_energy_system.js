/**
 * 设计统一的能量-阈值关联系统
 * 解决能量计算与阈值设定脱节的问题
 */

// 统一的能量计算系统
function designUnifiedEnergySystem() {
  console.log('🧪 ===== 统一能量-阈值关联系统设计 =====\n');
  
  // 基础设计原则
  console.log('📋 设计原则:');
  console.log('  1. 能量值必须在0-100%范围内');
  console.log('  2. 阈值基于能量分布的统计规律设定');
  console.log('  3. 不同维度使用统一的计算标准');
  console.log('  4. 达标率控制在合理范围(30-70%)');
  
  // 测试数据集（模拟不同强度的八字）
  const testDataSet = [
    { name: '极弱', elements: { 金: 10, 木: 8, 水: 12, 火: 9, 土: 11 } },
    { name: '偏弱', elements: { 金: 20, 木: 18, 水: 22, 火: 19, 土: 21 } },
    { name: '中等', elements: { 金: 35, 木: 32, 水: 38, 火: 34, 土: 36 } },
    { name: '偏强', elements: { 金: 50, 木: 48, 水: 52, 火: 49, 土: 51 } },
    { name: '很强', elements: { 金: 70, 木: 68, 水: 72, 火: 69, 土: 71 } }
  ];
  
  // 统一的能量计算方法
  function calculateUnifiedEnergy(elements, eventType) {
    const { 金, 木, 水, 火, 土 } = elements;
    
    // 计算五行总力量
    const totalPower = 金 + 木 + 水 + 火 + 土;
    
    // 各维度的五行权重配置（基于古籍理论）
    const elementWeights = {
      marriage: { 金: 0.3, 木: 0.1, 水: 0.2, 火: 0.3, 土: 0.1 }, // 财官为主
      promotion: { 金: 0.4, 木: 0.2, 水: 0.3, 火: 0.05, 土: 0.05 }, // 官印为主
      childbirth: { 金: 0.1, 木: 0.3, 水: 0.4, 火: 0.15, 土: 0.05 }, // 食伤为主
      wealth: { 金: 0.2, 木: 0.3, 火: 0.3, 土: 0.15, 水: 0.05 } // 财库为主
    };
    
    const weights = elementWeights[eventType];
    
    // 计算加权能量
    const weightedEnergy = 金 * weights.金 + 木 * weights.木 + 水 * weights.水 + 
                          火 * weights.火 + 土 * weights.土;
    
    // 标准化到0-100%范围（基于理论最大值）
    const maxPossibleEnergy = 100 * (weights.金 + weights.木 + weights.水 + weights.火 + weights.土); // 100
    const normalizedEnergy = (weightedEnergy / maxPossibleEnergy) * 100;
    
    // 应用平衡度调整
    const balance = calculateBalance(elements);
    const balanceAdjustment = (balance - 50) * 0.2; // 平衡度影响±10%
    
    const finalEnergy = Math.max(0, Math.min(100, normalizedEnergy + balanceAdjustment));
    
    return {
      weightedEnergy: weightedEnergy.toFixed(1),
      normalizedEnergy: normalizedEnergy.toFixed(1),
      balanceAdjustment: balanceAdjustment.toFixed(1),
      finalEnergy: finalEnergy.toFixed(1)
    };
  }
  
  // 计算五行平衡度
  function calculateBalance(elements) {
    const values = Object.values(elements);
    const average = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - average, 2), 0) / values.length;
    const standardDeviation = Math.sqrt(variance);
    
    // 标准差越小，平衡度越高
    const balance = Math.max(0, Math.min(100, 100 - standardDeviation * 2));
    return balance;
  }
  
  // 测试统一系统
  console.log('\n🧪 统一系统测试:');
  
  const eventTypes = ['marriage', 'promotion', 'childbirth', 'wealth'];
  const allResults = [];
  
  testDataSet.forEach((testCase, index) => {
    console.log(`\n🔍 ${testCase.name}命格测试:`);
    console.log(`  五行: 金${testCase.elements.金} 木${testCase.elements.木} 水${testCase.elements.水} 火${testCase.elements.火} 土${testCase.elements.土}`);
    
    const balance = calculateBalance(testCase.elements);
    console.log(`  平衡度: ${balance.toFixed(1)}%`);
    
    const caseResults = {};
    eventTypes.forEach(eventType => {
      const result = calculateUnifiedEnergy(testCase.elements, eventType);
      caseResults[eventType] = parseFloat(result.finalEnergy);
      console.log(`    ${eventType}: ${result.finalEnergy}%`);
    });
    
    allResults.push({
      case: testCase.name,
      balance: balance,
      results: caseResults
    });
  });
  
  // 基于统计数据设定阈值
  console.log('\n📊 基于统计的阈值设定:');
  
  const thresholds = {};
  eventTypes.forEach(eventType => {
    const energyValues = allResults.map(r => r.results[eventType]);
    energyValues.sort((a, b) => a - b);
    
    // 使用60分位数作为阈值（40%的人达标）
    const thresholdIndex = Math.floor(energyValues.length * 0.6);
    const threshold = energyValues[thresholdIndex];
    
    thresholds[eventType] = threshold;
    
    console.log(`  ${eventType}: ${threshold.toFixed(1)}% (60分位数)`);
    console.log(`    能量分布: [${energyValues.map(v => v.toFixed(1)).join(', ')}]`);
  });
  
  // 验证达标率
  console.log('\n🎯 达标率验证:');
  
  allResults.forEach(result => {
    const metCount = eventTypes.filter(eventType => 
      result.results[eventType] >= thresholds[eventType]
    ).length;
    const metRate = (metCount / eventTypes.length * 100).toFixed(1);
    
    console.log(`  ${result.case}: ${metCount}/4 (${metRate}%)`);
  });
  
  // 整体达标率
  const totalMet = allResults.reduce((sum, result) => {
    return sum + eventTypes.filter(eventType => 
      result.results[eventType] >= thresholds[eventType]
    ).length;
  }, 0);
  const totalPossible = allResults.length * eventTypes.length;
  const overallMetRate = (totalMet / totalPossible * 100).toFixed(1);
  
  console.log(`\n📈 整体达标率: ${totalMet}/${totalPossible} (${overallMetRate}%)`);
  
  // 系统优势分析
  console.log('\n✅ 统一系统优势:');
  console.log('  1. 能量值严格控制在0-100%范围内');
  console.log('  2. 阈值基于真实数据分布设定');
  console.log('  3. 不同维度使用统一计算标准');
  console.log('  4. 达标率控制在合理范围');
  console.log('  5. 考虑五行平衡度的影响');
  console.log('  6. 基于古籍理论的权重配置');
  
  console.log('\n🔧 实施建议:');
  console.log('  1. 替换现有的独立计算方法');
  console.log('  2. 使用统计方法设定阈值');
  console.log('  3. 建立能量-阈值的内在关联');
  console.log('  4. 定期根据用户数据调整阈值');
  
  return {
    thresholds: thresholds,
    overallMetRate: parseFloat(overallMetRate),
    systemValid: parseFloat(overallMetRate) >= 30 && parseFloat(overallMetRate) <= 70,
    allResults: allResults
  };
}

// 运行设计
designUnifiedEnergySystem();
