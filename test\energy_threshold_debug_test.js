// test/energy_threshold_debug_test.js
// 专门调试能量阈值逻辑矛盾

const ProfessionalTimingEngine = require('../utils/professional_timing_engine.js');

class EnergyThresholdDebugTest {
  constructor() {
    this.engine = new ProfessionalTimingEngine();
  }

  async runDebugTest() {
    console.log('🔍 开始能量阈值逻辑调试...\n');

    // 测试用例：成年人八字
    const testBazi = {
      year_pillar: { heavenly: '庚', earthly: '午' },
      month_pillar: { heavenly: '丙', earthly: '寅' },
      day_pillar: { heavenly: '戊', earthly: '午' },
      time_pillar: { heavenly: '壬', earthly: '戌' },
      day_master: '戊',
      birth_info: {
        year: 1990,
        month: 2,
        day: 15,
        hour: 10,
        gender: '男'
      }
    };

    console.log('📊 测试八字: 庚午 丙寅 戊午 壬戌\n');

    // 1. 测试能量阈值计算
    console.log('⚡ 步骤1: 能量阈值计算');
    const energyAnalysis = this.engine.calculateEnergyThresholds(testBazi, 'marriage');
    
    console.log('  五行能量分布:');
    Object.entries(energyAnalysis.element_energies).forEach(([element, energy]) => {
      console.log(`    ${element}: ${energy.toFixed(3)}`);
    });

    console.log('\n  阈值检测结果:');
    Object.entries(energyAnalysis.threshold_results).forEach(([key, result]) => {
      const status = result.met ? '✅ 达标' : '❌ 未达标';
      console.log(`    ${key}: ${result.percentage}% (要求${(result.required * 100).toFixed(1)}%) ${status}`);
    });

    // 2. 测试阈值状态检查
    console.log('\n🔍 步骤2: 阈值状态检查');
    const thresholdStatus = this.engine.checkEnergyThresholdStatus(energyAnalysis);
    
    console.log('  状态检查结果:');
    console.log(`    整体达标: ${thresholdStatus.overall_met ? '✅ 是' : '❌ 否'}`);
    console.log(`    达标数量: ${thresholdStatus.met_count}/${thresholdStatus.total_count}`);
    console.log(`    完成率: ${thresholdStatus.completion_rate}%`);
    
    if (thresholdStatus.deficit_info.deficits.length > 0) {
      console.log('  未达标项目:');
      thresholdStatus.deficit_info.deficits.forEach(deficit => {
        console.log(`    - ${deficit.threshold_name}: 缺口${deficit.deficit_percentage}%`);
      });
    }

    // 3. 测试综合应期计算的逻辑
    console.log('\n🎯 步骤3: 综合应期计算逻辑');
    
    // 模拟病药分析和三重引动分析
    const mockDiseaseAnalysis = {
      diseases: [{ type: '比劫', severity: 0.5 }],
      medicines: [{ type: '官杀', effectiveness: 0.6 }],
      balance_score: 0.5
    };

    const mockActivationAnalysis = {
      activation_results: [],
      best_years: [],
      star_activation: '未检测到星动',
      palace_activation: '未检测到宫动',
      god_activation: '未检测到神煞动'
    };

    const timingResult = this.engine.calculateComprehensiveTiming(
      mockDiseaseAnalysis,
      energyAnalysis,
      mockActivationAnalysis,
      2024,
      5
    );

    console.log('  综合应期结果:');
    console.log(`    阈值状态: ${timingResult.threshold_status}`);
    console.log(`    最佳年份: ${timingResult.best_year || 'N/A'}`);
    console.log(`    置信度: ${timingResult.confidence || 'N/A'}`);

    // 4. 分析矛盾原因
    console.log('\n🔍 步骤4: 矛盾分析');
    
    const energyMet = Object.values(energyAnalysis.threshold_results).every(r => r.met);
    const overallMet = thresholdStatus.overall_met;
    const timingMet = timingResult.threshold_status === 'met';

    console.log('  各模块结果对比:');
    console.log(`    能量阈值模块: ${energyMet ? '✅ 达标' : '❌ 未达标'}`);
    console.log(`    状态检查模块: ${overallMet ? '✅ 达标' : '❌ 未达标'}`);
    console.log(`    综合应期模块: ${timingMet ? '✅ 达标' : '❌ 未达标'}`);

    if (energyMet !== overallMet || overallMet !== timingMet) {
      console.log('\n🚨 发现逻辑矛盾！');
      
      if (energyMet && !overallMet) {
        console.log('  问题1: 能量阈值达标但状态检查未达标');
        console.log('  可能原因: checkEnergyThresholdStatus 逻辑有误');
      }
      
      if (overallMet && !timingMet) {
        console.log('  问题2: 状态检查达标但综合应期未达标');
        console.log('  可能原因: calculateComprehensiveTiming 使用了不同的判断标准');
      }
      
      if (energyMet && !timingMet) {
        console.log('  问题3: 能量阈值达标但综合应期未达标');
        console.log('  可能原因: 两个模块使用了不同的阈值配置');
      }
    } else {
      console.log('\n✅ 各模块逻辑一致！');
    }

    // 5. 详细调试信息
    console.log('\n🔧 详细调试信息:');
    console.log('  energyAnalysis.threshold_results:');
    console.log(JSON.stringify(energyAnalysis.threshold_results, null, 2));
    
    console.log('\n  thresholdStatus:');
    console.log(JSON.stringify(thresholdStatus, null, 2));
  }
}

// 运行调试测试
const debugTest = new EnergyThresholdDebugTest();
debugTest.runDebugTest().catch(console.error);
