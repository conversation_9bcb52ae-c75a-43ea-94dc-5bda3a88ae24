/**
 * 容器结构分析器
 * 专门分析主要容器的嵌套结构
 */

const fs = require('fs');
const path = require('path');

function analyzeContainerStructure() {
  console.log('🔍 分析容器结构');
  
  const wxmlPath = path.join(__dirname, '../pages/bazi-result/index.wxml');
  const content = fs.readFileSync(wxmlPath, 'utf8');
  const lines = content.split('\n');
  
  // 找到主要容器的位置
  const mainContainer = lines.findIndex(line => line.includes('<view class="tianggong-container">'));
  const scrollViewStart = lines.findIndex(line => line.includes('<scroll-view'));
  const scrollViewEnd = lines.findIndex(line => line.includes('</scroll-view>'));
  
  console.log(`📍 主要结构位置:`);
  console.log(`tianggong-container: 第${mainContainer + 1}行`);
  console.log(`scroll-view 开始: 第${scrollViewStart + 1}行`);
  console.log(`scroll-view 结束: 第${scrollViewEnd + 1}行`);
  
  // 分析scroll-view之前的结构
  console.log(`\n🔍 分析scroll-view之前的结构 (第${mainContainer + 1}行 - 第${scrollViewStart}行):`);
  
  let depth = 0;
  const openTags = [];
  
  for (let i = mainContainer; i < scrollViewStart; i++) {
    const line = lines[i];
    const lineNum = i + 1;
    
    // 匹配view标签
    const openViews = (line.match(/<view[^>]*>/g) || []).length;
    const closeViews = (line.match(/<\/view>/g) || []).length;
    
    if (openViews > 0) {
      for (let j = 0; j < openViews; j++) {
        openTags.push(lineNum);
        depth++;
      }
      console.log(`第${lineNum}行: +${openViews} view标签 (深度: ${depth})`);
    }
    
    if (closeViews > 0) {
      for (let j = 0; j < closeViews; j++) {
        if (openTags.length > 0) {
          openTags.pop();
        }
        depth--;
      }
      console.log(`第${lineNum}行: -${closeViews} view标签 (深度: ${depth})`);
    }
  }
  
  console.log(`\n📊 scroll-view之前的统计:`);
  console.log(`最终深度: ${depth}`);
  console.log(`未关闭的view标签数: ${openTags.length}`);
  
  if (openTags.length > 0) {
    console.log(`未关闭的view标签位置:`);
    openTags.forEach(line => console.log(`  第${line}行`));
  }
  
  // 分析scroll-view之后的结构
  console.log(`\n🔍 分析scroll-view之后的结构 (第${scrollViewEnd + 1}行 - 文件结尾):`);
  
  let afterDepth = 0;
  for (let i = scrollViewEnd + 1; i < lines.length; i++) {
    const line = lines[i];
    const lineNum = i + 1;
    
    const openViews = (line.match(/<view[^>]*>/g) || []).length;
    const closeViews = (line.match(/<\/view>/g) || []).length;
    
    if (openViews > 0 || closeViews > 0) {
      afterDepth += openViews - closeViews;
      console.log(`第${lineNum}行: ${line.trim()} (深度变化: ${openViews - closeViews}, 当前深度: ${afterDepth})`);
    }
  }
  
  console.log(`\n📊 scroll-view之后的统计:`);
  console.log(`最终深度: ${afterDepth}`);
  
  // 计算需要的结束标签
  const totalNeeded = depth + afterDepth;
  console.log(`\n🔧 修复建议:`);
  console.log(`scroll-view之前需要关闭: ${depth} 个view标签`);
  console.log(`scroll-view之后应该有: ${-afterDepth} 个view结束标签`);
  console.log(`总共需要的结束标签: ${totalNeeded}`);
  
  if (totalNeeded === 1) {
    console.log(`✅ 理论上应该只需要1个结束标签来关闭tianggong-container`);
  } else {
    console.log(`❌ 结构不平衡，需要调整`);
  }
}

// 运行分析
if (require.main === module) {
  analyzeContainerStructure();
}

module.exports = { analyzeContainerStructure };
