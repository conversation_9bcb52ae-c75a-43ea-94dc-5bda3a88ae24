#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def detailed_feature_analysis(file_path):
    """详细分析WXML文件的功能特性"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"🔍 详细功能分析: {file_path}")
    
    # 分析每个tab页面的具体内容
    tab_analysis = {
        'basic': {
            'expected_cards': ['user-info-card', 'bazi-card', 'wuxing-card', 'celestial-chart-card'],
            'key_features': ['四柱八字', '五行分析', '天体图', '用户信息']
        },
        'paipan': {
            'expected_cards': ['paipan-card', 'shishen-card', 'dayun-card'],
            'key_features': ['排盘表格', '十神分析', '大运信息']
        },
        'advanced': {
            'expected_cards': ['wuxing-strength-card', 'auspicious-stars-card', 'inauspicious-stars-card', 'yongshen-card'],
            'key_features': ['五行强弱', '吉星神煞', '凶星神煞', '用神喜忌']
        },
        'fortune': {
            'expected_cards': ['current-dayun-card', 'liunian-card', 'dayun-overview-card'],
            'key_features': ['当前大运', '流年运势', '大运总览']
        },
        'professional': {
            'expected_cards': ['mingge-card', 'yongshen-professional-card', 'geju-success-card', 'career-advice-card'],
            'key_features': ['命格分析', '用神分析', '格局成败', '职业建议']
        },
        'classical': {
            'expected_cards': ['classical-analysis-card', 'classical-judgment-card', 'classical-pattern-card', 'classical-advice-card'],
            'key_features': ['古籍命理', '古法断语', '古籍格局', '古法建议']
        },
        'timing': {
            'expected_cards': ['career-timing-card', 'wealth-timing-card', 'marriage-timing-card', 'health-timing-card'],
            'key_features': ['事业应期', '财运应期', '婚姻应期', '健康应期']
        },
        'liuqin': {
            'expected_cards': ['spouse-analysis-card', 'children-analysis-card', 'siblings-analysis-card', 'parents-analysis-card', 'liuqin-summary-card'],
            'key_features': ['配偶分析', '子女分析', '兄弟分析', '父母分析', '六亲总结']
        }
    }
    
    print(f"\n📋 各Tab页面功能完整性检查:")
    
    total_expected_cards = 0
    total_found_cards = 0
    
    for tab, info in tab_analysis.items():
        print(f"\n🏷️ {tab.upper()} 页面:")
        
        # 检查tab页面是否存在
        tab_exists = f"currentTab === '{tab}'" in content
        print(f"  页面存在: {'✅' if tab_exists else '❌'}")
        
        if tab_exists:
            # 检查预期的卡片
            found_cards = []
            for card in info['expected_cards']:
                if card in content:
                    found_cards.append(card)
                    total_found_cards += 1
                total_expected_cards += 1
            
            print(f"  预期卡片: {len(info['expected_cards'])}")
            print(f"  找到卡片: {len(found_cards)}")
            print(f"  卡片完整度: {len(found_cards)/len(info['expected_cards'])*100:.1f}%")
            
            # 检查关键功能
            found_features = []
            for feature in info['key_features']:
                # 简单的关键词匹配
                if any(keyword in content for keyword in feature.split()):
                    found_features.append(feature)
            
            print(f"  关键功能: {found_features}")
    
    print(f"\n📊 总体卡片完整性: {total_found_cards}/{total_expected_cards} ({total_found_cards/total_expected_cards*100:.1f}%)")
    
    # 分析复杂交互功能
    print(f"\n🎯 复杂功能组件分析:")
    
    complex_features = {
        '天体图交互': ['house-lines', 'zodiac-circle', 'planets-container', 'celestial-legend'],
        '排盘表格': ['paipan-table', 'paipan-header', 'paipan-row'],
        '五行图表': ['wuxing-stats', 'element-bar', 'bar-fill'],
        '时间轴组件': ['dayun-timeline', 'timeline-item', 'timeline-content'],
        '统计图表': ['summary-stats', 'stat-item', 'stat-number'],
        '事件列表': ['timing-events', 'timing-event', 'event-period']
    }
    
    for feature, components in complex_features.items():
        found_components = sum(1 for comp in components if comp in content)
        completeness = found_components / len(components) * 100
        status = "✅" if completeness >= 80 else "⚠️" if completeness >= 50 else "❌"
        print(f"  {status} {feature}: {found_components}/{len(components)} ({completeness:.1f}%)")
    
    # 分析数据绑定的复杂性
    print(f"\n📊 数据绑定复杂性分析:")
    
    # 统计不同类型的数据绑定
    binding_types = {
        '基础数据': r'unifiedData\.[^}]+',
        '分析数据': r'(classicalAnalysis|professionalAnalysis|timingAnalysis|liuqinAnalysis)\.[^}]+',
        '状态数据': r'pageState\.[^}]+',
        '循环数据': r'item\.[^}]+',
        '条件数据': r'\w+\s*&&\s*\w+'
    }
    
    for binding_type, pattern in binding_types.items():
        matches = re.findall(pattern, content)
        print(f"  {binding_type}: {len(matches)} 个绑定")
    
    return True

if __name__ == "__main__":
    detailed_feature_analysis("pages/bazi-result/index.wxml")
